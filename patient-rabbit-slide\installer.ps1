# مثبت خطة مشروعي - PowerShell 
Add-Type -AssemblyName System.Windows.Forms 
Add-Type -AssemblyName System.Drawing 
 
# واجهة المثبت 
$form = New-Object System.Windows.Forms.Form 
$form.Text = "مثبت خطة مشروعي v1.0.0" 
$form.Size = New-Object System.Drawing.Size(500,400) 
$form.StartPosition = "CenterScreen" 
$form.FormBorderStyle = "FixedDialog" 
$form.MaximizeBox = $false 
 
# عنوان 
$label = New-Object System.Windows.Forms.Label 
$label.Location = New-Object System.Drawing.Point(50,20) 
$label.Size = New-Object System.Drawing.Size(400,30) 
$label.Text = "مرحباً بك في مثبت خطة مشروعي" 
$label.Font = New-Object System.Drawing.Font("Arial",12,[System.Drawing.FontStyle]::Bold) 
$form.Controls.Add($label) 
 
# وصف 
$desc = New-Object System.Windows.Forms.Label 
$desc.Location = New-Object System.Drawing.Point(50,60) 
$desc.Size = New-Object System.Drawing.Size(400,40) 
$desc.Text = "تطبيق متكامل لإنشاء خطط الأعمال باللغة العربية`nالمطور: saif aldulaimi" 
$form.Controls.Add($desc) 
 
# مسار التثبيت 
$pathLabel = New-Object System.Windows.Forms.Label 
$pathLabel.Location = New-Object System.Drawing.Point(50,120) 
$pathLabel.Size = New-Object System.Drawing.Size(100,20) 
$pathLabel.Text = "مجلد التثبيت:" 
$form.Controls.Add($pathLabel) 
 
$pathBox = New-Object System.Windows.Forms.TextBox 
$pathBox.Location = New-Object System.Drawing.Point(50,145) 
$pathBox.Size = New-Object System.Drawing.Size(300,20) 
$pathBox.Text = "C:\Program Files\خطة مشروعي" 
$form.Controls.Add($pathBox) 
 
# زر تصفح 
$browseBtn = New-Object System.Windows.Forms.Button 
$browseBtn.Location = New-Object System.Drawing.Point(360,144) 
$browseBtn.Size = New-Object System.Drawing.Size(80,22) 
$browseBtn.Text = "تصفح..." 
$browseBtn.Add_Click({ 
    $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog 
    if($folderBrowser.ShowDialog() -eq "OK"){ 
        $pathBox.Text = $folderBrowser.SelectedPath + "\خطة مشروعي" 
    } 
}) 
$form.Controls.Add($browseBtn) 
 
# شريط التقدم 
$progressBar = New-Object System.Windows.Forms.ProgressBar 
$progressBar.Location = New-Object System.Drawing.Point(50,200) 
$progressBar.Size = New-Object System.Drawing.Size(390,20) 
$progressBar.Style = "Continuous" 
$form.Controls.Add($progressBar) 
 
# نص الحالة 
$statusLabel = New-Object System.Windows.Forms.Label 
$statusLabel.Location = New-Object System.Drawing.Point(50,230) 
$statusLabel.Size = New-Object System.Drawing.Size(390,20) 
$statusLabel.Text = "جاهز للتثبيت..." 
$form.Controls.Add($statusLabel) 
 
# زر التثبيت 
$installBtn = New-Object System.Windows.Forms.Button 
$installBtn.Location = New-Object System.Drawing.Point(250,280) 
$installBtn.Size = New-Object System.Drawing.Size(80,30) 
$installBtn.Text = "تثبيت" 
$installBtn.BackColor = [System.Drawing.Color]::LightGreen 
$installBtn.Add_Click({ 
    $installBtn.Enabled = $false 
    $statusLabel.Text = "جاري التثبيت..." 
    $progressBar.Value = 20 
    Start-Sleep -Milliseconds 500 
 
    # إنشاء مجلد التثبيت 
    $installPath = $pathBox.Text 
    if(!(Test-Path $installPath)){ 
        New-Item -ItemType Directory -Path $installPath -Force 
    } 
    $progressBar.Value = 40 
    $statusLabel.Text = "نسخ الملفات..." 
    Start-Sleep -Milliseconds 500 
 
    # نسخ ملفات التطبيق 
    if(Test-Path "business-plan-clean"){ 
        Copy-Item "business-plan-clean\*" $installPath -Recurse -Force 
    } 
    $progressBar.Value = 70 
    $statusLabel.Text = "إنشاء الاختصارات..." 
    Start-Sleep -Milliseconds 500 
 
    # إنشاء اختصار سطح المكتب 
    $WshShell = New-Object -comObject WScript.Shell 
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\خطة مشروعي.lnk") 
    $Shortcut.TargetPath = "$installPath\run-app.bat" 
    $Shortcut.WorkingDirectory = $installPath 
    $Shortcut.IconLocation = "$installPath\icon.ico" 
    $Shortcut.Save() 
 
    $progressBar.Value = 100 
    $statusLabel.Text = "تم التثبيت بنجاح!" 
    Start-Sleep -Milliseconds 1000 
 
    [System.Windows.Forms.MessageBox]::Show("تم تثبيت خطة مشروعي بنجاح!`n`nيمكنك الآن تشغيل التطبيق من سطح المكتب.","تم التثبيت","OK","Information") 
    $form.Close() 
}) 
$form.Controls.Add($installBtn) 
 
# زر الإلغاء 
$cancelBtn = New-Object System.Windows.Forms.Button 
$cancelBtn.Location = New-Object System.Drawing.Point(350,280) 
$cancelBtn.Size = New-Object System.Drawing.Size(80,30) 
$cancelBtn.Text = "إلغاء" 
$cancelBtn.Add_Click({ $form.Close() }) 
$form.Controls.Add($cancelBtn) 
 
# عرض النافذة 
$form.ShowDialog() 

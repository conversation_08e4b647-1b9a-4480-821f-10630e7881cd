# دليل استخدام تطبيق خطة مشروعي 📋

## 🚀 كيفية تشغيل التطبيق

### الطريقة الأولى: تشغيل التطبيق المبني
```bash
# انقر مرتين على الملف أو شغله من سطر الأوامر
.\run-app.bat
```

### الطريقة الثانية: تشغيل في وضع التطوير
```bash
# للمطورين - يشغل خادم التطوير مع إعادة التحميل التلقائي
.\dev-app.bat
```

### الطريقة الثالثة: استخدام npm
```bash
# تشغيل في وضع التطوير
npm run electron:dev

# أو تشغيل التطبيق المبني
npm run electron
```

## 🛠️ بناء التطبيق للتوزيع

```bash
# بناء التطبيق كملف تنفيذي
npm run electron:build

# أو بناء مجلد فقط (أسرع)
npx electron-builder --dir
```

## 📁 هيكل الملفات المهمة

```
├── run-app.bat          # ملف تشغيل سريع للتطبيق
├── dev-app.bat          # ملف تشغيل وضع التطوير
├── dist/                # ملفات React المبنية
├── dist-electron/       # ملفات Electron المبنية
├── dist-app/           # التطبيق النهائي المبني
├── electron/           # كود Electron الأساسي
│   ├── main.ts         # العملية الرئيسية
│   └── preload.ts      # سكريبت الأمان
└── src/                # كود React
```

## ⚡ اختصارات لوحة المفاتيح

- `Ctrl+S` / `Cmd+S`: حفظ المشروع
- `Ctrl+O` / `Cmd+O`: فتح مشروع
- `Ctrl+N` / `Cmd+N`: مشروع جديد
- `F11`: الشاشة الكاملة
- `F12`: أدوات المطور

## 🔧 حل المشاكل الشائعة

### التطبيق لا يفتح
1. تأكد من تثبيت Node.js
2. شغل `npm install` لتثبيت التبعيات
3. جرب `.\run-app.bat`

### أخطاء في البناء
1. احذف مجلد `node_modules`
2. شغل `npm install` مرة أخرى
3. شغل `npm run build`

### مشاكل في وضع التطوير
1. تأكد من أن المنفذ 5173 غير مستخدم
2. جرب إعادة تشغيل `.\dev-app.bat`

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، تواصل معنا.

---
**تم تطوير هذا التطبيق باستخدام Dyad Platform** 🚀

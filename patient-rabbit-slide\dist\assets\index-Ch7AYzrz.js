var jp=n=>{throw TypeError(n)};var zu=(n,r,s)=>r.has(n)||jp("Cannot "+s);var B=(n,r,s)=>(zu(n,r,"read from private field"),s?s.call(n):r.get(n)),_e=(n,r,s)=>r.has(n)?jp("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(n):r.set(n,s),we=(n,r,s,a)=>(zu(n,r,"write to private field"),a?a.call(n,s):r.set(n,s),s),ht=(n,r,s)=>(zu(n,r,"access private method"),s);var Na=(n,r,s,a)=>({set _(c){we(n,r,c,s)},get _(){return B(n,r,a)}});function Wv(n,r){for(var s=0;s<r.length;s++){const a=r[s];if(typeof a!="string"&&!Array.isArray(a)){for(const c in a)if(c!=="default"&&!(c in n)){const f=Object.getOwnPropertyDescriptor(a,c);f&&Object.defineProperty(n,c,f.get?f:{enumerable:!0,get:()=>a[c]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))a(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const p of f.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&a(p)}).observe(document,{childList:!0,subtree:!0});function s(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function a(c){if(c.ep)return;c.ep=!0;const f=s(c);fetch(c.href,f)}})();function Th(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Uu={exports:{}},Ws={},$u={exports:{}},Ne={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kp;function Qv(){if(kp)return Ne;kp=1;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.provider"),p=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),b=Symbol.iterator;function N(R){return R===null||typeof R!="object"?null:(R=b&&R[b]||R["@@iterator"],typeof R=="function"?R:null)}var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},P=Object.assign,k={};function j(R,L,ee){this.props=R,this.context=L,this.refs=k,this.updater=ee||y}j.prototype.isReactComponent={},j.prototype.setState=function(R,L){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,L,"setState")},j.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function _(){}_.prototype=j.prototype;function E(R,L,ee){this.props=R,this.context=L,this.refs=k,this.updater=ee||y}var A=E.prototype=new _;A.constructor=E,P(A,j.prototype),A.isPureReactComponent=!0;var z=Array.isArray,O=Object.prototype.hasOwnProperty,F={current:null},$={key:!0,ref:!0,__self:!0,__source:!0};function G(R,L,ee){var re,he={},be=null,ce=null;if(L!=null)for(re in L.ref!==void 0&&(ce=L.ref),L.key!==void 0&&(be=""+L.key),L)O.call(L,re)&&!$.hasOwnProperty(re)&&(he[re]=L[re]);var Ce=arguments.length-2;if(Ce===1)he.children=ee;else if(1<Ce){for(var Pe=Array(Ce),Ge=0;Ge<Ce;Ge++)Pe[Ge]=arguments[Ge+2];he.children=Pe}if(R&&R.defaultProps)for(re in Ce=R.defaultProps,Ce)he[re]===void 0&&(he[re]=Ce[re]);return{$$typeof:n,type:R,key:be,ref:ce,props:he,_owner:F.current}}function J(R,L){return{$$typeof:n,type:R.type,key:L,ref:R.ref,props:R.props,_owner:R._owner}}function Z(R){return typeof R=="object"&&R!==null&&R.$$typeof===n}function pe(R){var L={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(ee){return L[ee]})}var te=/\/+/g;function ge(R,L){return typeof R=="object"&&R!==null&&R.key!=null?pe(""+R.key):L.toString(36)}function X(R,L,ee,re,he){var be=typeof R;(be==="undefined"||be==="boolean")&&(R=null);var ce=!1;if(R===null)ce=!0;else switch(be){case"string":case"number":ce=!0;break;case"object":switch(R.$$typeof){case n:case r:ce=!0}}if(ce)return ce=R,he=he(ce),R=re===""?"."+ge(ce,0):re,z(he)?(ee="",R!=null&&(ee=R.replace(te,"$&/")+"/"),X(he,L,ee,"",function(Ge){return Ge})):he!=null&&(Z(he)&&(he=J(he,ee+(!he.key||ce&&ce.key===he.key?"":(""+he.key).replace(te,"$&/")+"/")+R)),L.push(he)),1;if(ce=0,re=re===""?".":re+":",z(R))for(var Ce=0;Ce<R.length;Ce++){be=R[Ce];var Pe=re+ge(be,Ce);ce+=X(be,L,ee,Pe,he)}else if(Pe=N(R),typeof Pe=="function")for(R=Pe.call(R),Ce=0;!(be=R.next()).done;)be=be.value,Pe=re+ge(be,Ce++),ce+=X(be,L,ee,Pe,he);else if(be==="object")throw L=String(R),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.");return ce}function ve(R,L,ee){if(R==null)return R;var re=[],he=0;return X(R,re,"","",function(be){return L.call(ee,be,he++)}),re}function se(R){if(R._status===-1){var L=R._result;L=L(),L.then(function(ee){(R._status===0||R._status===-1)&&(R._status=1,R._result=ee)},function(ee){(R._status===0||R._status===-1)&&(R._status=2,R._result=ee)}),R._status===-1&&(R._status=0,R._result=L)}if(R._status===1)return R._result.default;throw R._result}var le={current:null},U={transition:null},V={ReactCurrentDispatcher:le,ReactCurrentBatchConfig:U,ReactCurrentOwner:F};function q(){throw Error("act(...) is not supported in production builds of React.")}return Ne.Children={map:ve,forEach:function(R,L,ee){ve(R,function(){L.apply(this,arguments)},ee)},count:function(R){var L=0;return ve(R,function(){L++}),L},toArray:function(R){return ve(R,function(L){return L})||[]},only:function(R){if(!Z(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},Ne.Component=j,Ne.Fragment=s,Ne.Profiler=c,Ne.PureComponent=E,Ne.StrictMode=a,Ne.Suspense=m,Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,Ne.act=q,Ne.cloneElement=function(R,L,ee){if(R==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+R+".");var re=P({},R.props),he=R.key,be=R.ref,ce=R._owner;if(L!=null){if(L.ref!==void 0&&(be=L.ref,ce=F.current),L.key!==void 0&&(he=""+L.key),R.type&&R.type.defaultProps)var Ce=R.type.defaultProps;for(Pe in L)O.call(L,Pe)&&!$.hasOwnProperty(Pe)&&(re[Pe]=L[Pe]===void 0&&Ce!==void 0?Ce[Pe]:L[Pe])}var Pe=arguments.length-2;if(Pe===1)re.children=ee;else if(1<Pe){Ce=Array(Pe);for(var Ge=0;Ge<Pe;Ge++)Ce[Ge]=arguments[Ge+2];re.children=Ce}return{$$typeof:n,type:R.type,key:he,ref:be,props:re,_owner:ce}},Ne.createContext=function(R){return R={$$typeof:p,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},R.Provider={$$typeof:f,_context:R},R.Consumer=R},Ne.createElement=G,Ne.createFactory=function(R){var L=G.bind(null,R);return L.type=R,L},Ne.createRef=function(){return{current:null}},Ne.forwardRef=function(R){return{$$typeof:h,render:R}},Ne.isValidElement=Z,Ne.lazy=function(R){return{$$typeof:w,_payload:{_status:-1,_result:R},_init:se}},Ne.memo=function(R,L){return{$$typeof:v,type:R,compare:L===void 0?null:L}},Ne.startTransition=function(R){var L=U.transition;U.transition={};try{R()}finally{U.transition=L}},Ne.unstable_act=q,Ne.useCallback=function(R,L){return le.current.useCallback(R,L)},Ne.useContext=function(R){return le.current.useContext(R)},Ne.useDebugValue=function(){},Ne.useDeferredValue=function(R){return le.current.useDeferredValue(R)},Ne.useEffect=function(R,L){return le.current.useEffect(R,L)},Ne.useId=function(){return le.current.useId()},Ne.useImperativeHandle=function(R,L,ee){return le.current.useImperativeHandle(R,L,ee)},Ne.useInsertionEffect=function(R,L){return le.current.useInsertionEffect(R,L)},Ne.useLayoutEffect=function(R,L){return le.current.useLayoutEffect(R,L)},Ne.useMemo=function(R,L){return le.current.useMemo(R,L)},Ne.useReducer=function(R,L,ee){return le.current.useReducer(R,L,ee)},Ne.useRef=function(R){return le.current.useRef(R)},Ne.useState=function(R){return le.current.useState(R)},Ne.useSyncExternalStore=function(R,L,ee){return le.current.useSyncExternalStore(R,L,ee)},Ne.useTransition=function(){return le.current.useTransition()},Ne.version="18.3.1",Ne}var Np;function vc(){return Np||(Np=1,$u.exports=Qv()),$u.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sp;function qv(){if(Sp)return Ws;Sp=1;var n=vc(),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,c=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(h,m,v){var w,b={},N=null,y=null;v!==void 0&&(N=""+v),m.key!==void 0&&(N=""+m.key),m.ref!==void 0&&(y=m.ref);for(w in m)a.call(m,w)&&!f.hasOwnProperty(w)&&(b[w]=m[w]);if(h&&h.defaultProps)for(w in m=h.defaultProps,m)b[w]===void 0&&(b[w]=m[w]);return{$$typeof:r,type:h,key:N,ref:y,props:b,_owner:c.current}}return Ws.Fragment=s,Ws.jsx=p,Ws.jsxs=p,Ws}var Cp;function Kv(){return Cp||(Cp=1,Uu.exports=qv()),Uu.exports}var l=Kv(),Sa={},Bu={exports:{}},Tt={},Vu={exports:{}},Hu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ep;function Gv(){return Ep||(Ep=1,function(n){function r(U,V){var q=U.length;U.push(V);e:for(;0<q;){var R=q-1>>>1,L=U[R];if(0<c(L,V))U[R]=V,U[q]=L,q=R;else break e}}function s(U){return U.length===0?null:U[0]}function a(U){if(U.length===0)return null;var V=U[0],q=U.pop();if(q!==V){U[0]=q;e:for(var R=0,L=U.length,ee=L>>>1;R<ee;){var re=2*(R+1)-1,he=U[re],be=re+1,ce=U[be];if(0>c(he,q))be<L&&0>c(ce,he)?(U[R]=ce,U[be]=q,R=be):(U[R]=he,U[re]=q,R=re);else if(be<L&&0>c(ce,q))U[R]=ce,U[be]=q,R=be;else break e}}return V}function c(U,V){var q=U.sortIndex-V.sortIndex;return q!==0?q:U.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var p=Date,h=p.now();n.unstable_now=function(){return p.now()-h}}var m=[],v=[],w=1,b=null,N=3,y=!1,P=!1,k=!1,j=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,E=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function A(U){for(var V=s(v);V!==null;){if(V.callback===null)a(v);else if(V.startTime<=U)a(v),V.sortIndex=V.expirationTime,r(m,V);else break;V=s(v)}}function z(U){if(k=!1,A(U),!P)if(s(m)!==null)P=!0,se(O);else{var V=s(v);V!==null&&le(z,V.startTime-U)}}function O(U,V){P=!1,k&&(k=!1,_(G),G=-1),y=!0;var q=N;try{for(A(V),b=s(m);b!==null&&(!(b.expirationTime>V)||U&&!pe());){var R=b.callback;if(typeof R=="function"){b.callback=null,N=b.priorityLevel;var L=R(b.expirationTime<=V);V=n.unstable_now(),typeof L=="function"?b.callback=L:b===s(m)&&a(m),A(V)}else a(m);b=s(m)}if(b!==null)var ee=!0;else{var re=s(v);re!==null&&le(z,re.startTime-V),ee=!1}return ee}finally{b=null,N=q,y=!1}}var F=!1,$=null,G=-1,J=5,Z=-1;function pe(){return!(n.unstable_now()-Z<J)}function te(){if($!==null){var U=n.unstable_now();Z=U;var V=!0;try{V=$(!0,U)}finally{V?ge():(F=!1,$=null)}}else F=!1}var ge;if(typeof E=="function")ge=function(){E(te)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,ve=X.port2;X.port1.onmessage=te,ge=function(){ve.postMessage(null)}}else ge=function(){j(te,0)};function se(U){$=U,F||(F=!0,ge())}function le(U,V){G=j(function(){U(n.unstable_now())},V)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(U){U.callback=null},n.unstable_continueExecution=function(){P||y||(P=!0,se(O))},n.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<U?Math.floor(1e3/U):5},n.unstable_getCurrentPriorityLevel=function(){return N},n.unstable_getFirstCallbackNode=function(){return s(m)},n.unstable_next=function(U){switch(N){case 1:case 2:case 3:var V=3;break;default:V=N}var q=N;N=V;try{return U()}finally{N=q}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(U,V){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var q=N;N=U;try{return V()}finally{N=q}},n.unstable_scheduleCallback=function(U,V,q){var R=n.unstable_now();switch(typeof q=="object"&&q!==null?(q=q.delay,q=typeof q=="number"&&0<q?R+q:R):q=R,U){case 1:var L=-1;break;case 2:L=250;break;case 5:L=**********;break;case 4:L=1e4;break;default:L=5e3}return L=q+L,U={id:w++,callback:V,priorityLevel:U,startTime:q,expirationTime:L,sortIndex:-1},q>R?(U.sortIndex=q,r(v,U),s(m)===null&&U===s(v)&&(k?(_(G),G=-1):k=!0,le(z,q-R))):(U.sortIndex=L,r(m,U),P||y||(P=!0,se(O))),U},n.unstable_shouldYield=pe,n.unstable_wrapCallback=function(U){var V=N;return function(){var q=N;N=V;try{return U.apply(this,arguments)}finally{N=q}}}}(Hu)),Hu}var Pp;function Yv(){return Pp||(Pp=1,Vu.exports=Gv()),Vu.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tp;function Xv(){if(Tp)return Tt;Tp=1;var n=vc(),r=Yv();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,o=1;o<arguments.length;o++)t+="&args[]="+encodeURIComponent(arguments[o]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,c={};function f(e,t){p(e,t),p(e+"Capture",t)}function p(e,t){for(c[e]=t,e=0;e<t.length;e++)a.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,v=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,w={},b={};function N(e){return m.call(b,e)?!0:m.call(w,e)?!1:v.test(e)?b[e]=!0:(w[e]=!0,!1)}function y(e,t,o,i){if(o!==null&&o.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:o!==null?!o.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function P(e,t,o,i){if(t===null||typeof t>"u"||y(e,t,o,i))return!0;if(i)return!1;if(o!==null)switch(o.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function k(e,t,o,i,u,d,g){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=u,this.mustUseProperty=o,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=g}var j={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){j[e]=new k(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];j[t]=new k(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){j[e]=new k(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){j[e]=new k(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){j[e]=new k(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){j[e]=new k(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){j[e]=new k(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){j[e]=new k(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){j[e]=new k(e,5,!1,e.toLowerCase(),null,!1,!1)});var _=/[\-:]([a-z])/g;function E(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_,E);j[t]=new k(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_,E);j[t]=new k(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_,E);j[t]=new k(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){j[e]=new k(e,1,!1,e.toLowerCase(),null,!1,!1)}),j.xlinkHref=new k("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){j[e]=new k(e,1,!1,e.toLowerCase(),null,!0,!0)});function A(e,t,o,i){var u=j.hasOwnProperty(t)?j[t]:null;(u!==null?u.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(P(t,o,u,i)&&(o=null),i||u===null?N(t)&&(o===null?e.removeAttribute(t):e.setAttribute(t,""+o)):u.mustUseProperty?e[u.propertyName]=o===null?u.type===3?!1:"":o:(t=u.attributeName,i=u.attributeNamespace,o===null?e.removeAttribute(t):(u=u.type,o=u===3||u===4&&o===!0?"":""+o,i?e.setAttributeNS(i,t,o):e.setAttribute(t,o))))}var z=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,O=Symbol.for("react.element"),F=Symbol.for("react.portal"),$=Symbol.for("react.fragment"),G=Symbol.for("react.strict_mode"),J=Symbol.for("react.profiler"),Z=Symbol.for("react.provider"),pe=Symbol.for("react.context"),te=Symbol.for("react.forward_ref"),ge=Symbol.for("react.suspense"),X=Symbol.for("react.suspense_list"),ve=Symbol.for("react.memo"),se=Symbol.for("react.lazy"),le=Symbol.for("react.offscreen"),U=Symbol.iterator;function V(e){return e===null||typeof e!="object"?null:(e=U&&e[U]||e["@@iterator"],typeof e=="function"?e:null)}var q=Object.assign,R;function L(e){if(R===void 0)try{throw Error()}catch(o){var t=o.stack.trim().match(/\n( *(at )?)/);R=t&&t[1]||""}return`
`+R+e}var ee=!1;function re(e,t){if(!e||ee)return"";ee=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(I){var i=I}Reflect.construct(e,[],t)}else{try{t.call()}catch(I){i=I}e.call(t.prototype)}else{try{throw Error()}catch(I){i=I}e()}}catch(I){if(I&&i&&typeof I.stack=="string"){for(var u=I.stack.split(`
`),d=i.stack.split(`
`),g=u.length-1,S=d.length-1;1<=g&&0<=S&&u[g]!==d[S];)S--;for(;1<=g&&0<=S;g--,S--)if(u[g]!==d[S]){if(g!==1||S!==1)do if(g--,S--,0>S||u[g]!==d[S]){var C=`
`+u[g].replace(" at new "," at ");return e.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",e.displayName)),C}while(1<=g&&0<=S);break}}}finally{ee=!1,Error.prepareStackTrace=o}return(e=e?e.displayName||e.name:"")?L(e):""}function he(e){switch(e.tag){case 5:return L(e.type);case 16:return L("Lazy");case 13:return L("Suspense");case 19:return L("SuspenseList");case 0:case 2:case 15:return e=re(e.type,!1),e;case 11:return e=re(e.type.render,!1),e;case 1:return e=re(e.type,!0),e;default:return""}}function be(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $:return"Fragment";case F:return"Portal";case J:return"Profiler";case G:return"StrictMode";case ge:return"Suspense";case X:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pe:return(e.displayName||"Context")+".Consumer";case Z:return(e._context.displayName||"Context")+".Provider";case te:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ve:return t=e.displayName||null,t!==null?t:be(e.type)||"Memo";case se:t=e._payload,e=e._init;try{return be(e(t))}catch{}}return null}function ce(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return be(t);case 8:return t===G?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ce(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ge(e){var t=Pe(e)?"checked":"value",o=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof o<"u"&&typeof o.get=="function"&&typeof o.set=="function"){var u=o.get,d=o.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(g){i=""+g,d.call(this,g)}}),Object.defineProperty(e,t,{enumerable:o.enumerable}),{getValue:function(){return i},setValue:function(g){i=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _t(e){e._valueTracker||(e._valueTracker=Ge(e))}function Fn(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var o=t.getValue(),i="";return e&&(i=Pe(e)?e.checked?"true":"false":e.value),e=i,e!==o?(t.setValue(e),!0):!1}function At(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function nr(e,t){var o=t.checked;return q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:o??e._wrapperState.initialChecked})}function ho(e,t){var o=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;o=Ce(t.value!=null?t.value:o),e._wrapperState={initialChecked:i,initialValue:o,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ur(e,t){t=t.checked,t!=null&&A(e,"checked",t,!1)}function $r(e,t){Ur(e,t);var o=Ce(t.value),i=t.type;if(o!=null)i==="number"?(o===0&&e.value===""||e.value!=o)&&(e.value=""+o):e.value!==""+o&&(e.value=""+o);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vn(e,t.type,o):t.hasOwnProperty("defaultValue")&&vn(e,t.type,Ce(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rr(e,t,o){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,o||t===e.value||(e.value=t),e.defaultValue=t}o=e.name,o!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,o!==""&&(e.name=o)}function vn(e,t,o){(t!=="number"||At(e.ownerDocument)!==e)&&(o==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+o&&(e.defaultValue=""+o))}var or=Array.isArray;function Ut(e,t,o,i){if(e=e.options,t){t={};for(var u=0;u<o.length;u++)t["$"+o[u]]=!0;for(o=0;o<e.length;o++)u=t.hasOwnProperty("$"+e[o].value),e[o].selected!==u&&(e[o].selected=u),u&&i&&(e[o].defaultSelected=!0)}else{for(o=""+Ce(o),t=null,u=0;u<e.length;u++){if(e[u].value===o){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function mo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function yn(e,t){var o=t.value;if(o==null){if(o=t.children,t=t.defaultValue,o!=null){if(t!=null)throw Error(s(92));if(or(o)){if(1<o.length)throw Error(s(93));o=o[0]}t=o}t==null&&(t=""),o=t}e._wrapperState={initialValue:Ce(o)}}function di(e,t){var o=Ce(t.value),i=Ce(t.defaultValue);o!=null&&(o=""+o,o!==e.value&&(e.value=o),t.defaultValue==null&&e.defaultValue!==o&&(e.defaultValue=o)),i!=null&&(e.defaultValue=""+i)}function fi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gt(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function xn(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gt(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var go,pi=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,o,i,u){MSApp.execUnsafeLocalFunction(function(){return e(t,o,i,u)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(go=go||document.createElement("div"),go.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=go.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function wn(e,t){if(t){var o=e.firstChild;if(o&&o===e.lastChild&&o.nodeType===3){o.nodeValue=t;return}}e.textContent=t}var Br={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},hi=["Webkit","ms","Moz","O"];Object.keys(Br).forEach(function(e){hi.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Br[t]=Br[e]})});function vo(e,t,o){return t==null||typeof t=="boolean"||t===""?"":o||typeof t!="number"||t===0||Br.hasOwnProperty(e)&&Br[e]?(""+t).trim():t+"px"}function Ln(e,t){e=e.style;for(var o in t)if(t.hasOwnProperty(o)){var i=o.indexOf("--")===0,u=vo(o,t[o],i);o==="float"&&(o="cssFloat"),i?e.setProperty(o,u):e[o]=u}}var mi=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function bn(e,t){if(t){if(mi[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function us(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cs=null;function yo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var xo=null,sr=null,jn=null;function Jt(e){if(e=Rs(e)){if(typeof xo!="function")throw Error(s(280));var t=e.stateNode;t&&(t=zi(t),xo(e.stateNode,e.type,t))}}function gi(e){sr?jn?jn.push(e):jn=[e]:sr=e}function xe(){if(sr){var e=sr,t=jn;if(jn=sr=null,Jt(e),t)for(e=0;e<t.length;e++)Jt(t[e])}}function Ae(e,t){return e(t)}function Ie(){}var vt=!1;function jt(e,t,o){if(vt)return e(t,o);vt=!0;try{return Ae(e,t,o)}finally{vt=!1,(sr!==null||jn!==null)&&(Ie(),xe())}}function kt(e,t){var o=e.stateNode;if(o===null)return null;var i=zi(o);if(i===null)return null;o=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(o&&typeof o!="function")throw Error(s(231,t,typeof o));return o}var Zt=!1;if(h)try{var it={};Object.defineProperty(it,"passive",{get:function(){Zt=!0}}),window.addEventListener("test",it,it),window.removeEventListener("test",it,it)}catch{Zt=!1}function kn(e,t,o,i,u,d,g,S,C){var I=Array.prototype.slice.call(arguments,3);try{t.apply(o,I)}catch(W){this.onError(W)}}var ds=!1,vi=null,yi=!1,ol=null,Jg={onError:function(e){ds=!0,vi=e}};function Zg(e,t,o,i,u,d,g,S,C){ds=!1,vi=null,kn.apply(Jg,arguments)}function e0(e,t,o,i,u,d,g,S,C){if(Zg.apply(this,arguments),ds){if(ds){var I=vi;ds=!1,vi=null}else throw Error(s(198));yi||(yi=!0,ol=I)}}function Vr(e){var t=e,o=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(o=t.return),e=t.return;while(e)}return t.tag===3?o:null}function Hc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Wc(e){if(Vr(e)!==e)throw Error(s(188))}function t0(e){var t=e.alternate;if(!t){if(t=Vr(e),t===null)throw Error(s(188));return t!==e?null:e}for(var o=e,i=t;;){var u=o.return;if(u===null)break;var d=u.alternate;if(d===null){if(i=u.return,i!==null){o=i;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===o)return Wc(u),e;if(d===i)return Wc(u),t;d=d.sibling}throw Error(s(188))}if(o.return!==i.return)o=u,i=d;else{for(var g=!1,S=u.child;S;){if(S===o){g=!0,o=u,i=d;break}if(S===i){g=!0,i=u,o=d;break}S=S.sibling}if(!g){for(S=d.child;S;){if(S===o){g=!0,o=d,i=u;break}if(S===i){g=!0,i=d,o=u;break}S=S.sibling}if(!g)throw Error(s(189))}}if(o.alternate!==i)throw Error(s(190))}if(o.tag!==3)throw Error(s(188));return o.stateNode.current===o?e:t}function Qc(e){return e=t0(e),e!==null?qc(e):null}function qc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=qc(e);if(t!==null)return t;e=e.sibling}return null}var Kc=r.unstable_scheduleCallback,Gc=r.unstable_cancelCallback,n0=r.unstable_shouldYield,r0=r.unstable_requestPaint,Ye=r.unstable_now,o0=r.unstable_getCurrentPriorityLevel,sl=r.unstable_ImmediatePriority,Yc=r.unstable_UserBlockingPriority,xi=r.unstable_NormalPriority,s0=r.unstable_LowPriority,Xc=r.unstable_IdlePriority,wi=null,Nn=null;function i0(e){if(Nn&&typeof Nn.onCommitFiberRoot=="function")try{Nn.onCommitFiberRoot(wi,e,void 0,(e.current.flags&128)===128)}catch{}}var en=Math.clz32?Math.clz32:u0,a0=Math.log,l0=Math.LN2;function u0(e){return e>>>=0,e===0?32:31-(a0(e)/l0|0)|0}var bi=64,ji=4194304;function fs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ki(e,t){var o=e.pendingLanes;if(o===0)return 0;var i=0,u=e.suspendedLanes,d=e.pingedLanes,g=o&268435455;if(g!==0){var S=g&~u;S!==0?i=fs(S):(d&=g,d!==0&&(i=fs(d)))}else g=o&~u,g!==0?i=fs(g):d!==0&&(i=fs(d));if(i===0)return 0;if(t!==0&&t!==i&&(t&u)===0&&(u=i&-i,d=t&-t,u>=d||u===16&&(d&4194240)!==0))return t;if((i&4)!==0&&(i|=o&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)o=31-en(t),u=1<<o,i|=e[o],t&=~u;return i}function c0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function d0(e,t){for(var o=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes;0<d;){var g=31-en(d),S=1<<g,C=u[g];C===-1?((S&o)===0||(S&i)!==0)&&(u[g]=c0(S,t)):C<=t&&(e.expiredLanes|=S),d&=~S}}function il(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Jc(){var e=bi;return bi<<=1,(bi&4194240)===0&&(bi=64),e}function al(e){for(var t=[],o=0;31>o;o++)t.push(e);return t}function ps(e,t,o){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-en(t),e[t]=o}function f0(e,t){var o=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<o;){var u=31-en(o),d=1<<u;t[u]=0,i[u]=-1,e[u]=-1,o&=~d}}function ll(e,t){var o=e.entangledLanes|=t;for(e=e.entanglements;o;){var i=31-en(o),u=1<<i;u&t|e[i]&t&&(e[i]|=t),o&=~u}}var Me=0;function Zc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var ed,ul,td,nd,rd,cl=!1,Ni=[],ir=null,ar=null,lr=null,hs=new Map,ms=new Map,ur=[],p0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function od(e,t){switch(e){case"focusin":case"focusout":ir=null;break;case"dragenter":case"dragleave":ar=null;break;case"mouseover":case"mouseout":lr=null;break;case"pointerover":case"pointerout":hs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ms.delete(t.pointerId)}}function gs(e,t,o,i,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:o,eventSystemFlags:i,nativeEvent:d,targetContainers:[u]},t!==null&&(t=Rs(t),t!==null&&ul(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function h0(e,t,o,i,u){switch(t){case"focusin":return ir=gs(ir,e,t,o,i,u),!0;case"dragenter":return ar=gs(ar,e,t,o,i,u),!0;case"mouseover":return lr=gs(lr,e,t,o,i,u),!0;case"pointerover":var d=u.pointerId;return hs.set(d,gs(hs.get(d)||null,e,t,o,i,u)),!0;case"gotpointercapture":return d=u.pointerId,ms.set(d,gs(ms.get(d)||null,e,t,o,i,u)),!0}return!1}function sd(e){var t=Hr(e.target);if(t!==null){var o=Vr(t);if(o!==null){if(t=o.tag,t===13){if(t=Hc(o),t!==null){e.blockedOn=t,rd(e.priority,function(){td(o)});return}}else if(t===3&&o.stateNode.current.memoizedState.isDehydrated){e.blockedOn=o.tag===3?o.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Si(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var o=fl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(o===null){o=e.nativeEvent;var i=new o.constructor(o.type,o);cs=i,o.target.dispatchEvent(i),cs=null}else return t=Rs(o),t!==null&&ul(t),e.blockedOn=o,!1;t.shift()}return!0}function id(e,t,o){Si(e)&&o.delete(t)}function m0(){cl=!1,ir!==null&&Si(ir)&&(ir=null),ar!==null&&Si(ar)&&(ar=null),lr!==null&&Si(lr)&&(lr=null),hs.forEach(id),ms.forEach(id)}function vs(e,t){e.blockedOn===t&&(e.blockedOn=null,cl||(cl=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,m0)))}function ys(e){function t(u){return vs(u,e)}if(0<Ni.length){vs(Ni[0],e);for(var o=1;o<Ni.length;o++){var i=Ni[o];i.blockedOn===e&&(i.blockedOn=null)}}for(ir!==null&&vs(ir,e),ar!==null&&vs(ar,e),lr!==null&&vs(lr,e),hs.forEach(t),ms.forEach(t),o=0;o<ur.length;o++)i=ur[o],i.blockedOn===e&&(i.blockedOn=null);for(;0<ur.length&&(o=ur[0],o.blockedOn===null);)sd(o),o.blockedOn===null&&ur.shift()}var wo=z.ReactCurrentBatchConfig,Ci=!0;function g0(e,t,o,i){var u=Me,d=wo.transition;wo.transition=null;try{Me=1,dl(e,t,o,i)}finally{Me=u,wo.transition=d}}function v0(e,t,o,i){var u=Me,d=wo.transition;wo.transition=null;try{Me=4,dl(e,t,o,i)}finally{Me=u,wo.transition=d}}function dl(e,t,o,i){if(Ci){var u=fl(e,t,o,i);if(u===null)Tl(e,t,i,Ei,o),od(e,i);else if(h0(u,e,t,o,i))i.stopPropagation();else if(od(e,i),t&4&&-1<p0.indexOf(e)){for(;u!==null;){var d=Rs(u);if(d!==null&&ed(d),d=fl(e,t,o,i),d===null&&Tl(e,t,i,Ei,o),d===u)break;u=d}u!==null&&i.stopPropagation()}else Tl(e,t,i,null,o)}}var Ei=null;function fl(e,t,o,i){if(Ei=null,e=yo(i),e=Hr(e),e!==null)if(t=Vr(e),t===null)e=null;else if(o=t.tag,o===13){if(e=Hc(t),e!==null)return e;e=null}else if(o===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ei=e,null}function ad(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(o0()){case sl:return 1;case Yc:return 4;case xi:case s0:return 16;case Xc:return 536870912;default:return 16}default:return 16}}var cr=null,pl=null,Pi=null;function ld(){if(Pi)return Pi;var e,t=pl,o=t.length,i,u="value"in cr?cr.value:cr.textContent,d=u.length;for(e=0;e<o&&t[e]===u[e];e++);var g=o-e;for(i=1;i<=g&&t[o-i]===u[d-i];i++);return Pi=u.slice(e,1<i?1-i:void 0)}function Ti(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ri(){return!0}function ud(){return!1}function Ot(e){function t(o,i,u,d,g){this._reactName=o,this._targetInst=u,this.type=i,this.nativeEvent=d,this.target=g,this.currentTarget=null;for(var S in e)e.hasOwnProperty(S)&&(o=e[S],this[S]=o?o(d):d[S]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Ri:ud,this.isPropagationStopped=ud,this}return q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var o=this.nativeEvent;o&&(o.preventDefault?o.preventDefault():typeof o.returnValue!="unknown"&&(o.returnValue=!1),this.isDefaultPrevented=Ri)},stopPropagation:function(){var o=this.nativeEvent;o&&(o.stopPropagation?o.stopPropagation():typeof o.cancelBubble!="unknown"&&(o.cancelBubble=!0),this.isPropagationStopped=Ri)},persist:function(){},isPersistent:Ri}),t}var bo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hl=Ot(bo),xs=q({},bo,{view:0,detail:0}),y0=Ot(xs),ml,gl,ws,_i=q({},xs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:yl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ws&&(ws&&e.type==="mousemove"?(ml=e.screenX-ws.screenX,gl=e.screenY-ws.screenY):gl=ml=0,ws=e),ml)},movementY:function(e){return"movementY"in e?e.movementY:gl}}),cd=Ot(_i),x0=q({},_i,{dataTransfer:0}),w0=Ot(x0),b0=q({},xs,{relatedTarget:0}),vl=Ot(b0),j0=q({},bo,{animationName:0,elapsedTime:0,pseudoElement:0}),k0=Ot(j0),N0=q({},bo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),S0=Ot(N0),C0=q({},bo,{data:0}),dd=Ot(C0),E0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},P0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},T0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function R0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=T0[e])?!!t[e]:!1}function yl(){return R0}var _0=q({},xs,{key:function(e){if(e.key){var t=E0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ti(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?P0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:yl,charCode:function(e){return e.type==="keypress"?Ti(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ti(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),A0=Ot(_0),O0=q({},_i,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),fd=Ot(O0),M0=q({},xs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:yl}),D0=Ot(M0),I0=q({},bo,{propertyName:0,elapsedTime:0,pseudoElement:0}),F0=Ot(I0),L0=q({},_i,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),z0=Ot(L0),U0=[9,13,27,32],xl=h&&"CompositionEvent"in window,bs=null;h&&"documentMode"in document&&(bs=document.documentMode);var $0=h&&"TextEvent"in window&&!bs,pd=h&&(!xl||bs&&8<bs&&11>=bs),hd=" ",md=!1;function gd(e,t){switch(e){case"keyup":return U0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jo=!1;function B0(e,t){switch(e){case"compositionend":return vd(t);case"keypress":return t.which!==32?null:(md=!0,hd);case"textInput":return e=t.data,e===hd&&md?null:e;default:return null}}function V0(e,t){if(jo)return e==="compositionend"||!xl&&gd(e,t)?(e=ld(),Pi=pl=cr=null,jo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return pd&&t.locale!=="ko"?null:t.data;default:return null}}var H0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!H0[e.type]:t==="textarea"}function xd(e,t,o,i){gi(i),t=Ii(t,"onChange"),0<t.length&&(o=new hl("onChange","change",null,o,i),e.push({event:o,listeners:t}))}var js=null,ks=null;function W0(e){Fd(e,0)}function Ai(e){var t=Eo(e);if(Fn(t))return e}function Q0(e,t){if(e==="change")return t}var wd=!1;if(h){var wl;if(h){var bl="oninput"in document;if(!bl){var bd=document.createElement("div");bd.setAttribute("oninput","return;"),bl=typeof bd.oninput=="function"}wl=bl}else wl=!1;wd=wl&&(!document.documentMode||9<document.documentMode)}function jd(){js&&(js.detachEvent("onpropertychange",kd),ks=js=null)}function kd(e){if(e.propertyName==="value"&&Ai(ks)){var t=[];xd(t,ks,e,yo(e)),jt(W0,t)}}function q0(e,t,o){e==="focusin"?(jd(),js=t,ks=o,js.attachEvent("onpropertychange",kd)):e==="focusout"&&jd()}function K0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ai(ks)}function G0(e,t){if(e==="click")return Ai(t)}function Y0(e,t){if(e==="input"||e==="change")return Ai(t)}function X0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var tn=typeof Object.is=="function"?Object.is:X0;function Ns(e,t){if(tn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;for(i=0;i<o.length;i++){var u=o[i];if(!m.call(t,u)||!tn(e[u],t[u]))return!1}return!0}function Nd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Sd(e,t){var o=Nd(e);e=0;for(var i;o;){if(o.nodeType===3){if(i=e+o.textContent.length,e<=t&&i>=t)return{node:o,offset:t-e};e=i}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=Nd(o)}}function Cd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Cd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ed(){for(var e=window,t=At();t instanceof e.HTMLIFrameElement;){try{var o=typeof t.contentWindow.location.href=="string"}catch{o=!1}if(o)e=t.contentWindow;else break;t=At(e.document)}return t}function jl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function J0(e){var t=Ed(),o=e.focusedElem,i=e.selectionRange;if(t!==o&&o&&o.ownerDocument&&Cd(o.ownerDocument.documentElement,o)){if(i!==null&&jl(o)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in o)o.selectionStart=t,o.selectionEnd=Math.min(e,o.value.length);else if(e=(t=o.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var u=o.textContent.length,d=Math.min(i.start,u);i=i.end===void 0?d:Math.min(i.end,u),!e.extend&&d>i&&(u=i,i=d,d=u),u=Sd(o,d);var g=Sd(o,i);u&&g&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==g.node||e.focusOffset!==g.offset)&&(t=t.createRange(),t.setStart(u.node,u.offset),e.removeAllRanges(),d>i?(e.addRange(t),e.extend(g.node,g.offset)):(t.setEnd(g.node,g.offset),e.addRange(t)))}}for(t=[],e=o;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<t.length;o++)e=t[o],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Z0=h&&"documentMode"in document&&11>=document.documentMode,ko=null,kl=null,Ss=null,Nl=!1;function Pd(e,t,o){var i=o.window===o?o.document:o.nodeType===9?o:o.ownerDocument;Nl||ko==null||ko!==At(i)||(i=ko,"selectionStart"in i&&jl(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ss&&Ns(Ss,i)||(Ss=i,i=Ii(kl,"onSelect"),0<i.length&&(t=new hl("onSelect","select",null,t,o),e.push({event:t,listeners:i}),t.target=ko)))}function Oi(e,t){var o={};return o[e.toLowerCase()]=t.toLowerCase(),o["Webkit"+e]="webkit"+t,o["Moz"+e]="moz"+t,o}var No={animationend:Oi("Animation","AnimationEnd"),animationiteration:Oi("Animation","AnimationIteration"),animationstart:Oi("Animation","AnimationStart"),transitionend:Oi("Transition","TransitionEnd")},Sl={},Td={};h&&(Td=document.createElement("div").style,"AnimationEvent"in window||(delete No.animationend.animation,delete No.animationiteration.animation,delete No.animationstart.animation),"TransitionEvent"in window||delete No.transitionend.transition);function Mi(e){if(Sl[e])return Sl[e];if(!No[e])return e;var t=No[e],o;for(o in t)if(t.hasOwnProperty(o)&&o in Td)return Sl[e]=t[o];return e}var Rd=Mi("animationend"),_d=Mi("animationiteration"),Ad=Mi("animationstart"),Od=Mi("transitionend"),Md=new Map,Dd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function dr(e,t){Md.set(e,t),f(t,[e])}for(var Cl=0;Cl<Dd.length;Cl++){var El=Dd[Cl],ev=El.toLowerCase(),tv=El[0].toUpperCase()+El.slice(1);dr(ev,"on"+tv)}dr(Rd,"onAnimationEnd"),dr(_d,"onAnimationIteration"),dr(Ad,"onAnimationStart"),dr("dblclick","onDoubleClick"),dr("focusin","onFocus"),dr("focusout","onBlur"),dr(Od,"onTransitionEnd"),p("onMouseEnter",["mouseout","mouseover"]),p("onMouseLeave",["mouseout","mouseover"]),p("onPointerEnter",["pointerout","pointerover"]),p("onPointerLeave",["pointerout","pointerover"]),f("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),f("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),f("onBeforeInput",["compositionend","keypress","textInput","paste"]),f("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),nv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Cs));function Id(e,t,o){var i=e.type||"unknown-event";e.currentTarget=o,e0(i,t,void 0,e),e.currentTarget=null}function Fd(e,t){t=(t&4)!==0;for(var o=0;o<e.length;o++){var i=e[o],u=i.event;i=i.listeners;e:{var d=void 0;if(t)for(var g=i.length-1;0<=g;g--){var S=i[g],C=S.instance,I=S.currentTarget;if(S=S.listener,C!==d&&u.isPropagationStopped())break e;Id(u,S,I),d=C}else for(g=0;g<i.length;g++){if(S=i[g],C=S.instance,I=S.currentTarget,S=S.listener,C!==d&&u.isPropagationStopped())break e;Id(u,S,I),d=C}}}if(yi)throw e=ol,yi=!1,ol=null,e}function Le(e,t){var o=t[Dl];o===void 0&&(o=t[Dl]=new Set);var i=e+"__bubble";o.has(i)||(Ld(t,e,2,!1),o.add(i))}function Pl(e,t,o){var i=0;t&&(i|=4),Ld(o,e,i,t)}var Di="_reactListening"+Math.random().toString(36).slice(2);function Es(e){if(!e[Di]){e[Di]=!0,a.forEach(function(o){o!=="selectionchange"&&(nv.has(o)||Pl(o,!1,e),Pl(o,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Di]||(t[Di]=!0,Pl("selectionchange",!1,t))}}function Ld(e,t,o,i){switch(ad(t)){case 1:var u=g0;break;case 4:u=v0;break;default:u=dl}o=u.bind(null,t,o,e),u=void 0,!Zt||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,o,{capture:!0,passive:u}):e.addEventListener(t,o,!0):u!==void 0?e.addEventListener(t,o,{passive:u}):e.addEventListener(t,o,!1)}function Tl(e,t,o,i,u){var d=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var g=i.tag;if(g===3||g===4){var S=i.stateNode.containerInfo;if(S===u||S.nodeType===8&&S.parentNode===u)break;if(g===4)for(g=i.return;g!==null;){var C=g.tag;if((C===3||C===4)&&(C=g.stateNode.containerInfo,C===u||C.nodeType===8&&C.parentNode===u))return;g=g.return}for(;S!==null;){if(g=Hr(S),g===null)return;if(C=g.tag,C===5||C===6){i=d=g;continue e}S=S.parentNode}}i=i.return}jt(function(){var I=d,W=yo(o),Q=[];e:{var H=Md.get(e);if(H!==void 0){var ne=hl,ie=e;switch(e){case"keypress":if(Ti(o)===0)break e;case"keydown":case"keyup":ne=A0;break;case"focusin":ie="focus",ne=vl;break;case"focusout":ie="blur",ne=vl;break;case"beforeblur":case"afterblur":ne=vl;break;case"click":if(o.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ne=cd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ne=w0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ne=D0;break;case Rd:case _d:case Ad:ne=k0;break;case Od:ne=F0;break;case"scroll":ne=y0;break;case"wheel":ne=z0;break;case"copy":case"cut":case"paste":ne=S0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ne=fd}var ae=(t&4)!==0,Xe=!ae&&e==="scroll",M=ae?H!==null?H+"Capture":null:H;ae=[];for(var T=I,D;T!==null;){D=T;var K=D.stateNode;if(D.tag===5&&K!==null&&(D=K,M!==null&&(K=kt(T,M),K!=null&&ae.push(Ps(T,K,D)))),Xe)break;T=T.return}0<ae.length&&(H=new ne(H,ie,null,o,W),Q.push({event:H,listeners:ae}))}}if((t&7)===0){e:{if(H=e==="mouseover"||e==="pointerover",ne=e==="mouseout"||e==="pointerout",H&&o!==cs&&(ie=o.relatedTarget||o.fromElement)&&(Hr(ie)||ie[zn]))break e;if((ne||H)&&(H=W.window===W?W:(H=W.ownerDocument)?H.defaultView||H.parentWindow:window,ne?(ie=o.relatedTarget||o.toElement,ne=I,ie=ie?Hr(ie):null,ie!==null&&(Xe=Vr(ie),ie!==Xe||ie.tag!==5&&ie.tag!==6)&&(ie=null)):(ne=null,ie=I),ne!==ie)){if(ae=cd,K="onMouseLeave",M="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(ae=fd,K="onPointerLeave",M="onPointerEnter",T="pointer"),Xe=ne==null?H:Eo(ne),D=ie==null?H:Eo(ie),H=new ae(K,T+"leave",ne,o,W),H.target=Xe,H.relatedTarget=D,K=null,Hr(W)===I&&(ae=new ae(M,T+"enter",ie,o,W),ae.target=D,ae.relatedTarget=Xe,K=ae),Xe=K,ne&&ie)t:{for(ae=ne,M=ie,T=0,D=ae;D;D=So(D))T++;for(D=0,K=M;K;K=So(K))D++;for(;0<T-D;)ae=So(ae),T--;for(;0<D-T;)M=So(M),D--;for(;T--;){if(ae===M||M!==null&&ae===M.alternate)break t;ae=So(ae),M=So(M)}ae=null}else ae=null;ne!==null&&zd(Q,H,ne,ae,!1),ie!==null&&Xe!==null&&zd(Q,Xe,ie,ae,!0)}}e:{if(H=I?Eo(I):window,ne=H.nodeName&&H.nodeName.toLowerCase(),ne==="select"||ne==="input"&&H.type==="file")var ue=Q0;else if(yd(H))if(wd)ue=Y0;else{ue=K0;var de=q0}else(ne=H.nodeName)&&ne.toLowerCase()==="input"&&(H.type==="checkbox"||H.type==="radio")&&(ue=G0);if(ue&&(ue=ue(e,I))){xd(Q,ue,o,W);break e}de&&de(e,H,I),e==="focusout"&&(de=H._wrapperState)&&de.controlled&&H.type==="number"&&vn(H,"number",H.value)}switch(de=I?Eo(I):window,e){case"focusin":(yd(de)||de.contentEditable==="true")&&(ko=de,kl=I,Ss=null);break;case"focusout":Ss=kl=ko=null;break;case"mousedown":Nl=!0;break;case"contextmenu":case"mouseup":case"dragend":Nl=!1,Pd(Q,o,W);break;case"selectionchange":if(Z0)break;case"keydown":case"keyup":Pd(Q,o,W)}var fe;if(xl)e:{switch(e){case"compositionstart":var ye="onCompositionStart";break e;case"compositionend":ye="onCompositionEnd";break e;case"compositionupdate":ye="onCompositionUpdate";break e}ye=void 0}else jo?gd(e,o)&&(ye="onCompositionEnd"):e==="keydown"&&o.keyCode===229&&(ye="onCompositionStart");ye&&(pd&&o.locale!=="ko"&&(jo||ye!=="onCompositionStart"?ye==="onCompositionEnd"&&jo&&(fe=ld()):(cr=W,pl="value"in cr?cr.value:cr.textContent,jo=!0)),de=Ii(I,ye),0<de.length&&(ye=new dd(ye,e,null,o,W),Q.push({event:ye,listeners:de}),fe?ye.data=fe:(fe=vd(o),fe!==null&&(ye.data=fe)))),(fe=$0?B0(e,o):V0(e,o))&&(I=Ii(I,"onBeforeInput"),0<I.length&&(W=new dd("onBeforeInput","beforeinput",null,o,W),Q.push({event:W,listeners:I}),W.data=fe))}Fd(Q,t)})}function Ps(e,t,o){return{instance:e,listener:t,currentTarget:o}}function Ii(e,t){for(var o=t+"Capture",i=[];e!==null;){var u=e,d=u.stateNode;u.tag===5&&d!==null&&(u=d,d=kt(e,o),d!=null&&i.unshift(Ps(e,d,u)),d=kt(e,t),d!=null&&i.push(Ps(e,d,u))),e=e.return}return i}function So(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function zd(e,t,o,i,u){for(var d=t._reactName,g=[];o!==null&&o!==i;){var S=o,C=S.alternate,I=S.stateNode;if(C!==null&&C===i)break;S.tag===5&&I!==null&&(S=I,u?(C=kt(o,d),C!=null&&g.unshift(Ps(o,C,S))):u||(C=kt(o,d),C!=null&&g.push(Ps(o,C,S)))),o=o.return}g.length!==0&&e.push({event:t,listeners:g})}var rv=/\r\n?/g,ov=/\u0000|\uFFFD/g;function Ud(e){return(typeof e=="string"?e:""+e).replace(rv,`
`).replace(ov,"")}function Fi(e,t,o){if(t=Ud(t),Ud(e)!==t&&o)throw Error(s(425))}function Li(){}var Rl=null,_l=null;function Al(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ol=typeof setTimeout=="function"?setTimeout:void 0,sv=typeof clearTimeout=="function"?clearTimeout:void 0,$d=typeof Promise=="function"?Promise:void 0,iv=typeof queueMicrotask=="function"?queueMicrotask:typeof $d<"u"?function(e){return $d.resolve(null).then(e).catch(av)}:Ol;function av(e){setTimeout(function(){throw e})}function Ml(e,t){var o=t,i=0;do{var u=o.nextSibling;if(e.removeChild(o),u&&u.nodeType===8)if(o=u.data,o==="/$"){if(i===0){e.removeChild(u),ys(t);return}i--}else o!=="$"&&o!=="$?"&&o!=="$!"||i++;o=u}while(o);ys(t)}function fr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Bd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var o=e.data;if(o==="$"||o==="$!"||o==="$?"){if(t===0)return e;t--}else o==="/$"&&t++}e=e.previousSibling}return null}var Co=Math.random().toString(36).slice(2),Sn="__reactFiber$"+Co,Ts="__reactProps$"+Co,zn="__reactContainer$"+Co,Dl="__reactEvents$"+Co,lv="__reactListeners$"+Co,uv="__reactHandles$"+Co;function Hr(e){var t=e[Sn];if(t)return t;for(var o=e.parentNode;o;){if(t=o[zn]||o[Sn]){if(o=t.alternate,t.child!==null||o!==null&&o.child!==null)for(e=Bd(e);e!==null;){if(o=e[Sn])return o;e=Bd(e)}return t}e=o,o=e.parentNode}return null}function Rs(e){return e=e[Sn]||e[zn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Eo(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function zi(e){return e[Ts]||null}var Il=[],Po=-1;function pr(e){return{current:e}}function ze(e){0>Po||(e.current=Il[Po],Il[Po]=null,Po--)}function Fe(e,t){Po++,Il[Po]=e.current,e.current=t}var hr={},ct=pr(hr),Nt=pr(!1),Wr=hr;function To(e,t){var o=e.type.contextTypes;if(!o)return hr;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var u={},d;for(d in o)u[d]=t[d];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=u),u}function St(e){return e=e.childContextTypes,e!=null}function Ui(){ze(Nt),ze(ct)}function Vd(e,t,o){if(ct.current!==hr)throw Error(s(168));Fe(ct,t),Fe(Nt,o)}function Hd(e,t,o){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return o;i=i.getChildContext();for(var u in i)if(!(u in t))throw Error(s(108,ce(e)||"Unknown",u));return q({},o,i)}function $i(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hr,Wr=ct.current,Fe(ct,e),Fe(Nt,Nt.current),!0}function Wd(e,t,o){var i=e.stateNode;if(!i)throw Error(s(169));o?(e=Hd(e,t,Wr),i.__reactInternalMemoizedMergedChildContext=e,ze(Nt),ze(ct),Fe(ct,e)):ze(Nt),Fe(Nt,o)}var Un=null,Bi=!1,Fl=!1;function Qd(e){Un===null?Un=[e]:Un.push(e)}function cv(e){Bi=!0,Qd(e)}function mr(){if(!Fl&&Un!==null){Fl=!0;var e=0,t=Me;try{var o=Un;for(Me=1;e<o.length;e++){var i=o[e];do i=i(!0);while(i!==null)}Un=null,Bi=!1}catch(u){throw Un!==null&&(Un=Un.slice(e+1)),Kc(sl,mr),u}finally{Me=t,Fl=!1}}return null}var Ro=[],_o=0,Vi=null,Hi=0,$t=[],Bt=0,Qr=null,$n=1,Bn="";function qr(e,t){Ro[_o++]=Hi,Ro[_o++]=Vi,Vi=e,Hi=t}function qd(e,t,o){$t[Bt++]=$n,$t[Bt++]=Bn,$t[Bt++]=Qr,Qr=e;var i=$n;e=Bn;var u=32-en(i)-1;i&=~(1<<u),o+=1;var d=32-en(t)+u;if(30<d){var g=u-u%5;d=(i&(1<<g)-1).toString(32),i>>=g,u-=g,$n=1<<32-en(t)+u|o<<u|i,Bn=d+e}else $n=1<<d|o<<u|i,Bn=e}function Ll(e){e.return!==null&&(qr(e,1),qd(e,1,0))}function zl(e){for(;e===Vi;)Vi=Ro[--_o],Ro[_o]=null,Hi=Ro[--_o],Ro[_o]=null;for(;e===Qr;)Qr=$t[--Bt],$t[Bt]=null,Bn=$t[--Bt],$t[Bt]=null,$n=$t[--Bt],$t[Bt]=null}var Mt=null,Dt=null,Be=!1,nn=null;function Kd(e,t){var o=Qt(5,null,null,0);o.elementType="DELETED",o.stateNode=t,o.return=e,t=e.deletions,t===null?(e.deletions=[o],e.flags|=16):t.push(o)}function Gd(e,t){switch(e.tag){case 5:var o=e.type;return t=t.nodeType!==1||o.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Mt=e,Dt=fr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Mt=e,Dt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(o=Qr!==null?{id:$n,overflow:Bn}:null,e.memoizedState={dehydrated:t,treeContext:o,retryLane:1073741824},o=Qt(18,null,null,0),o.stateNode=t,o.return=e,e.child=o,Mt=e,Dt=null,!0):!1;default:return!1}}function Ul(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $l(e){if(Be){var t=Dt;if(t){var o=t;if(!Gd(e,t)){if(Ul(e))throw Error(s(418));t=fr(o.nextSibling);var i=Mt;t&&Gd(e,t)?Kd(i,o):(e.flags=e.flags&-4097|2,Be=!1,Mt=e)}}else{if(Ul(e))throw Error(s(418));e.flags=e.flags&-4097|2,Be=!1,Mt=e}}}function Yd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Mt=e}function Wi(e){if(e!==Mt)return!1;if(!Be)return Yd(e),Be=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Al(e.type,e.memoizedProps)),t&&(t=Dt)){if(Ul(e))throw Xd(),Error(s(418));for(;t;)Kd(e,t),t=fr(t.nextSibling)}if(Yd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var o=e.data;if(o==="/$"){if(t===0){Dt=fr(e.nextSibling);break e}t--}else o!=="$"&&o!=="$!"&&o!=="$?"||t++}e=e.nextSibling}Dt=null}}else Dt=Mt?fr(e.stateNode.nextSibling):null;return!0}function Xd(){for(var e=Dt;e;)e=fr(e.nextSibling)}function Ao(){Dt=Mt=null,Be=!1}function Bl(e){nn===null?nn=[e]:nn.push(e)}var dv=z.ReactCurrentBatchConfig;function _s(e,t,o){if(e=o.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(o._owner){if(o=o._owner,o){if(o.tag!==1)throw Error(s(309));var i=o.stateNode}if(!i)throw Error(s(147,e));var u=i,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(g){var S=u.refs;g===null?delete S[d]:S[d]=g},t._stringRef=d,t)}if(typeof e!="string")throw Error(s(284));if(!o._owner)throw Error(s(290,e))}return e}function Qi(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Jd(e){var t=e._init;return t(e._payload)}function Zd(e){function t(M,T){if(e){var D=M.deletions;D===null?(M.deletions=[T],M.flags|=16):D.push(T)}}function o(M,T){if(!e)return null;for(;T!==null;)t(M,T),T=T.sibling;return null}function i(M,T){for(M=new Map;T!==null;)T.key!==null?M.set(T.key,T):M.set(T.index,T),T=T.sibling;return M}function u(M,T){return M=kr(M,T),M.index=0,M.sibling=null,M}function d(M,T,D){return M.index=D,e?(D=M.alternate,D!==null?(D=D.index,D<T?(M.flags|=2,T):D):(M.flags|=2,T)):(M.flags|=1048576,T)}function g(M){return e&&M.alternate===null&&(M.flags|=2),M}function S(M,T,D,K){return T===null||T.tag!==6?(T=Ou(D,M.mode,K),T.return=M,T):(T=u(T,D),T.return=M,T)}function C(M,T,D,K){var ue=D.type;return ue===$?W(M,T,D.props.children,K,D.key):T!==null&&(T.elementType===ue||typeof ue=="object"&&ue!==null&&ue.$$typeof===se&&Jd(ue)===T.type)?(K=u(T,D.props),K.ref=_s(M,T,D),K.return=M,K):(K=ga(D.type,D.key,D.props,null,M.mode,K),K.ref=_s(M,T,D),K.return=M,K)}function I(M,T,D,K){return T===null||T.tag!==4||T.stateNode.containerInfo!==D.containerInfo||T.stateNode.implementation!==D.implementation?(T=Mu(D,M.mode,K),T.return=M,T):(T=u(T,D.children||[]),T.return=M,T)}function W(M,T,D,K,ue){return T===null||T.tag!==7?(T=to(D,M.mode,K,ue),T.return=M,T):(T=u(T,D),T.return=M,T)}function Q(M,T,D){if(typeof T=="string"&&T!==""||typeof T=="number")return T=Ou(""+T,M.mode,D),T.return=M,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case O:return D=ga(T.type,T.key,T.props,null,M.mode,D),D.ref=_s(M,null,T),D.return=M,D;case F:return T=Mu(T,M.mode,D),T.return=M,T;case se:var K=T._init;return Q(M,K(T._payload),D)}if(or(T)||V(T))return T=to(T,M.mode,D,null),T.return=M,T;Qi(M,T)}return null}function H(M,T,D,K){var ue=T!==null?T.key:null;if(typeof D=="string"&&D!==""||typeof D=="number")return ue!==null?null:S(M,T,""+D,K);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case O:return D.key===ue?C(M,T,D,K):null;case F:return D.key===ue?I(M,T,D,K):null;case se:return ue=D._init,H(M,T,ue(D._payload),K)}if(or(D)||V(D))return ue!==null?null:W(M,T,D,K,null);Qi(M,D)}return null}function ne(M,T,D,K,ue){if(typeof K=="string"&&K!==""||typeof K=="number")return M=M.get(D)||null,S(T,M,""+K,ue);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case O:return M=M.get(K.key===null?D:K.key)||null,C(T,M,K,ue);case F:return M=M.get(K.key===null?D:K.key)||null,I(T,M,K,ue);case se:var de=K._init;return ne(M,T,D,de(K._payload),ue)}if(or(K)||V(K))return M=M.get(D)||null,W(T,M,K,ue,null);Qi(T,K)}return null}function ie(M,T,D,K){for(var ue=null,de=null,fe=T,ye=T=0,st=null;fe!==null&&ye<D.length;ye++){fe.index>ye?(st=fe,fe=null):st=fe.sibling;var Re=H(M,fe,D[ye],K);if(Re===null){fe===null&&(fe=st);break}e&&fe&&Re.alternate===null&&t(M,fe),T=d(Re,T,ye),de===null?ue=Re:de.sibling=Re,de=Re,fe=st}if(ye===D.length)return o(M,fe),Be&&qr(M,ye),ue;if(fe===null){for(;ye<D.length;ye++)fe=Q(M,D[ye],K),fe!==null&&(T=d(fe,T,ye),de===null?ue=fe:de.sibling=fe,de=fe);return Be&&qr(M,ye),ue}for(fe=i(M,fe);ye<D.length;ye++)st=ne(fe,M,ye,D[ye],K),st!==null&&(e&&st.alternate!==null&&fe.delete(st.key===null?ye:st.key),T=d(st,T,ye),de===null?ue=st:de.sibling=st,de=st);return e&&fe.forEach(function(Nr){return t(M,Nr)}),Be&&qr(M,ye),ue}function ae(M,T,D,K){var ue=V(D);if(typeof ue!="function")throw Error(s(150));if(D=ue.call(D),D==null)throw Error(s(151));for(var de=ue=null,fe=T,ye=T=0,st=null,Re=D.next();fe!==null&&!Re.done;ye++,Re=D.next()){fe.index>ye?(st=fe,fe=null):st=fe.sibling;var Nr=H(M,fe,Re.value,K);if(Nr===null){fe===null&&(fe=st);break}e&&fe&&Nr.alternate===null&&t(M,fe),T=d(Nr,T,ye),de===null?ue=Nr:de.sibling=Nr,de=Nr,fe=st}if(Re.done)return o(M,fe),Be&&qr(M,ye),ue;if(fe===null){for(;!Re.done;ye++,Re=D.next())Re=Q(M,Re.value,K),Re!==null&&(T=d(Re,T,ye),de===null?ue=Re:de.sibling=Re,de=Re);return Be&&qr(M,ye),ue}for(fe=i(M,fe);!Re.done;ye++,Re=D.next())Re=ne(fe,M,ye,Re.value,K),Re!==null&&(e&&Re.alternate!==null&&fe.delete(Re.key===null?ye:Re.key),T=d(Re,T,ye),de===null?ue=Re:de.sibling=Re,de=Re);return e&&fe.forEach(function(Hv){return t(M,Hv)}),Be&&qr(M,ye),ue}function Xe(M,T,D,K){if(typeof D=="object"&&D!==null&&D.type===$&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case O:e:{for(var ue=D.key,de=T;de!==null;){if(de.key===ue){if(ue=D.type,ue===$){if(de.tag===7){o(M,de.sibling),T=u(de,D.props.children),T.return=M,M=T;break e}}else if(de.elementType===ue||typeof ue=="object"&&ue!==null&&ue.$$typeof===se&&Jd(ue)===de.type){o(M,de.sibling),T=u(de,D.props),T.ref=_s(M,de,D),T.return=M,M=T;break e}o(M,de);break}else t(M,de);de=de.sibling}D.type===$?(T=to(D.props.children,M.mode,K,D.key),T.return=M,M=T):(K=ga(D.type,D.key,D.props,null,M.mode,K),K.ref=_s(M,T,D),K.return=M,M=K)}return g(M);case F:e:{for(de=D.key;T!==null;){if(T.key===de)if(T.tag===4&&T.stateNode.containerInfo===D.containerInfo&&T.stateNode.implementation===D.implementation){o(M,T.sibling),T=u(T,D.children||[]),T.return=M,M=T;break e}else{o(M,T);break}else t(M,T);T=T.sibling}T=Mu(D,M.mode,K),T.return=M,M=T}return g(M);case se:return de=D._init,Xe(M,T,de(D._payload),K)}if(or(D))return ie(M,T,D,K);if(V(D))return ae(M,T,D,K);Qi(M,D)}return typeof D=="string"&&D!==""||typeof D=="number"?(D=""+D,T!==null&&T.tag===6?(o(M,T.sibling),T=u(T,D),T.return=M,M=T):(o(M,T),T=Ou(D,M.mode,K),T.return=M,M=T),g(M)):o(M,T)}return Xe}var Oo=Zd(!0),ef=Zd(!1),qi=pr(null),Ki=null,Mo=null,Vl=null;function Hl(){Vl=Mo=Ki=null}function Wl(e){var t=qi.current;ze(qi),e._currentValue=t}function Ql(e,t,o){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===o)break;e=e.return}}function Do(e,t){Ki=e,Vl=Mo=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ct=!0),e.firstContext=null)}function Vt(e){var t=e._currentValue;if(Vl!==e)if(e={context:e,memoizedValue:t,next:null},Mo===null){if(Ki===null)throw Error(s(308));Mo=e,Ki.dependencies={lanes:0,firstContext:e}}else Mo=Mo.next=e;return t}var Kr=null;function ql(e){Kr===null?Kr=[e]:Kr.push(e)}function tf(e,t,o,i){var u=t.interleaved;return u===null?(o.next=o,ql(t)):(o.next=u.next,u.next=o),t.interleaved=o,Vn(e,i)}function Vn(e,t){e.lanes|=t;var o=e.alternate;for(o!==null&&(o.lanes|=t),o=e,e=e.return;e!==null;)e.childLanes|=t,o=e.alternate,o!==null&&(o.childLanes|=t),o=e,e=e.return;return o.tag===3?o.stateNode:null}var gr=!1;function Kl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function nf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Hn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function vr(e,t,o){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Te&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,Vn(e,o)}return u=i.interleaved,u===null?(t.next=t,ql(i)):(t.next=u.next,u.next=t),i.interleaved=t,Vn(e,o)}function Gi(e,t,o){if(t=t.updateQueue,t!==null&&(t=t.shared,(o&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,o|=i,t.lanes=o,ll(e,o)}}function rf(e,t){var o=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,o===i)){var u=null,d=null;if(o=o.firstBaseUpdate,o!==null){do{var g={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};d===null?u=d=g:d=d.next=g,o=o.next}while(o!==null);d===null?u=d=t:d=d.next=t}else u=d=t;o={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:i.shared,effects:i.effects},e.updateQueue=o;return}e=o.lastBaseUpdate,e===null?o.firstBaseUpdate=t:e.next=t,o.lastBaseUpdate=t}function Yi(e,t,o,i){var u=e.updateQueue;gr=!1;var d=u.firstBaseUpdate,g=u.lastBaseUpdate,S=u.shared.pending;if(S!==null){u.shared.pending=null;var C=S,I=C.next;C.next=null,g===null?d=I:g.next=I,g=C;var W=e.alternate;W!==null&&(W=W.updateQueue,S=W.lastBaseUpdate,S!==g&&(S===null?W.firstBaseUpdate=I:S.next=I,W.lastBaseUpdate=C))}if(d!==null){var Q=u.baseState;g=0,W=I=C=null,S=d;do{var H=S.lane,ne=S.eventTime;if((i&H)===H){W!==null&&(W=W.next={eventTime:ne,lane:0,tag:S.tag,payload:S.payload,callback:S.callback,next:null});e:{var ie=e,ae=S;switch(H=t,ne=o,ae.tag){case 1:if(ie=ae.payload,typeof ie=="function"){Q=ie.call(ne,Q,H);break e}Q=ie;break e;case 3:ie.flags=ie.flags&-65537|128;case 0:if(ie=ae.payload,H=typeof ie=="function"?ie.call(ne,Q,H):ie,H==null)break e;Q=q({},Q,H);break e;case 2:gr=!0}}S.callback!==null&&S.lane!==0&&(e.flags|=64,H=u.effects,H===null?u.effects=[S]:H.push(S))}else ne={eventTime:ne,lane:H,tag:S.tag,payload:S.payload,callback:S.callback,next:null},W===null?(I=W=ne,C=Q):W=W.next=ne,g|=H;if(S=S.next,S===null){if(S=u.shared.pending,S===null)break;H=S,S=H.next,H.next=null,u.lastBaseUpdate=H,u.shared.pending=null}}while(!0);if(W===null&&(C=Q),u.baseState=C,u.firstBaseUpdate=I,u.lastBaseUpdate=W,t=u.shared.interleaved,t!==null){u=t;do g|=u.lane,u=u.next;while(u!==t)}else d===null&&(u.shared.lanes=0);Xr|=g,e.lanes=g,e.memoizedState=Q}}function of(e,t,o){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],u=i.callback;if(u!==null){if(i.callback=null,i=o,typeof u!="function")throw Error(s(191,u));u.call(i)}}}var As={},Cn=pr(As),Os=pr(As),Ms=pr(As);function Gr(e){if(e===As)throw Error(s(174));return e}function Gl(e,t){switch(Fe(Ms,t),Fe(Os,e),Fe(Cn,As),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:xn(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=xn(t,e)}ze(Cn),Fe(Cn,t)}function Io(){ze(Cn),ze(Os),ze(Ms)}function sf(e){Gr(Ms.current);var t=Gr(Cn.current),o=xn(t,e.type);t!==o&&(Fe(Os,e),Fe(Cn,o))}function Yl(e){Os.current===e&&(ze(Cn),ze(Os))}var Ve=pr(0);function Xi(e){for(var t=e;t!==null;){if(t.tag===13){var o=t.memoizedState;if(o!==null&&(o=o.dehydrated,o===null||o.data==="$?"||o.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Xl=[];function Jl(){for(var e=0;e<Xl.length;e++)Xl[e]._workInProgressVersionPrimary=null;Xl.length=0}var Ji=z.ReactCurrentDispatcher,Zl=z.ReactCurrentBatchConfig,Yr=0,He=null,et=null,rt=null,Zi=!1,Ds=!1,Is=0,fv=0;function dt(){throw Error(s(321))}function eu(e,t){if(t===null)return!1;for(var o=0;o<t.length&&o<e.length;o++)if(!tn(e[o],t[o]))return!1;return!0}function tu(e,t,o,i,u,d){if(Yr=d,He=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ji.current=e===null||e.memoizedState===null?gv:vv,e=o(i,u),Ds){d=0;do{if(Ds=!1,Is=0,25<=d)throw Error(s(301));d+=1,rt=et=null,t.updateQueue=null,Ji.current=yv,e=o(i,u)}while(Ds)}if(Ji.current=na,t=et!==null&&et.next!==null,Yr=0,rt=et=He=null,Zi=!1,t)throw Error(s(300));return e}function nu(){var e=Is!==0;return Is=0,e}function En(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return rt===null?He.memoizedState=rt=e:rt=rt.next=e,rt}function Ht(){if(et===null){var e=He.alternate;e=e!==null?e.memoizedState:null}else e=et.next;var t=rt===null?He.memoizedState:rt.next;if(t!==null)rt=t,et=e;else{if(e===null)throw Error(s(310));et=e,e={memoizedState:et.memoizedState,baseState:et.baseState,baseQueue:et.baseQueue,queue:et.queue,next:null},rt===null?He.memoizedState=rt=e:rt=rt.next=e}return rt}function Fs(e,t){return typeof t=="function"?t(e):t}function ru(e){var t=Ht(),o=t.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=e;var i=et,u=i.baseQueue,d=o.pending;if(d!==null){if(u!==null){var g=u.next;u.next=d.next,d.next=g}i.baseQueue=u=d,o.pending=null}if(u!==null){d=u.next,i=i.baseState;var S=g=null,C=null,I=d;do{var W=I.lane;if((Yr&W)===W)C!==null&&(C=C.next={lane:0,action:I.action,hasEagerState:I.hasEagerState,eagerState:I.eagerState,next:null}),i=I.hasEagerState?I.eagerState:e(i,I.action);else{var Q={lane:W,action:I.action,hasEagerState:I.hasEagerState,eagerState:I.eagerState,next:null};C===null?(S=C=Q,g=i):C=C.next=Q,He.lanes|=W,Xr|=W}I=I.next}while(I!==null&&I!==d);C===null?g=i:C.next=S,tn(i,t.memoizedState)||(Ct=!0),t.memoizedState=i,t.baseState=g,t.baseQueue=C,o.lastRenderedState=i}if(e=o.interleaved,e!==null){u=e;do d=u.lane,He.lanes|=d,Xr|=d,u=u.next;while(u!==e)}else u===null&&(o.lanes=0);return[t.memoizedState,o.dispatch]}function ou(e){var t=Ht(),o=t.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=e;var i=o.dispatch,u=o.pending,d=t.memoizedState;if(u!==null){o.pending=null;var g=u=u.next;do d=e(d,g.action),g=g.next;while(g!==u);tn(d,t.memoizedState)||(Ct=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),o.lastRenderedState=d}return[d,i]}function af(){}function lf(e,t){var o=He,i=Ht(),u=t(),d=!tn(i.memoizedState,u);if(d&&(i.memoizedState=u,Ct=!0),i=i.queue,su(df.bind(null,o,i,e),[e]),i.getSnapshot!==t||d||rt!==null&&rt.memoizedState.tag&1){if(o.flags|=2048,Ls(9,cf.bind(null,o,i,u,t),void 0,null),ot===null)throw Error(s(349));(Yr&30)!==0||uf(o,t,u)}return u}function uf(e,t,o){e.flags|=16384,e={getSnapshot:t,value:o},t=He.updateQueue,t===null?(t={lastEffect:null,stores:null},He.updateQueue=t,t.stores=[e]):(o=t.stores,o===null?t.stores=[e]:o.push(e))}function cf(e,t,o,i){t.value=o,t.getSnapshot=i,ff(t)&&pf(e)}function df(e,t,o){return o(function(){ff(t)&&pf(e)})}function ff(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!tn(e,o)}catch{return!0}}function pf(e){var t=Vn(e,1);t!==null&&an(t,e,1,-1)}function hf(e){var t=En();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Fs,lastRenderedState:e},t.queue=e,e=e.dispatch=mv.bind(null,He,e),[t.memoizedState,e]}function Ls(e,t,o,i){return e={tag:e,create:t,destroy:o,deps:i,next:null},t=He.updateQueue,t===null?(t={lastEffect:null,stores:null},He.updateQueue=t,t.lastEffect=e.next=e):(o=t.lastEffect,o===null?t.lastEffect=e.next=e:(i=o.next,o.next=e,e.next=i,t.lastEffect=e)),e}function mf(){return Ht().memoizedState}function ea(e,t,o,i){var u=En();He.flags|=e,u.memoizedState=Ls(1|t,o,void 0,i===void 0?null:i)}function ta(e,t,o,i){var u=Ht();i=i===void 0?null:i;var d=void 0;if(et!==null){var g=et.memoizedState;if(d=g.destroy,i!==null&&eu(i,g.deps)){u.memoizedState=Ls(t,o,d,i);return}}He.flags|=e,u.memoizedState=Ls(1|t,o,d,i)}function gf(e,t){return ea(8390656,8,e,t)}function su(e,t){return ta(2048,8,e,t)}function vf(e,t){return ta(4,2,e,t)}function yf(e,t){return ta(4,4,e,t)}function xf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function wf(e,t,o){return o=o!=null?o.concat([e]):null,ta(4,4,xf.bind(null,t,e),o)}function iu(){}function bf(e,t){var o=Ht();t=t===void 0?null:t;var i=o.memoizedState;return i!==null&&t!==null&&eu(t,i[1])?i[0]:(o.memoizedState=[e,t],e)}function jf(e,t){var o=Ht();t=t===void 0?null:t;var i=o.memoizedState;return i!==null&&t!==null&&eu(t,i[1])?i[0]:(e=e(),o.memoizedState=[e,t],e)}function kf(e,t,o){return(Yr&21)===0?(e.baseState&&(e.baseState=!1,Ct=!0),e.memoizedState=o):(tn(o,t)||(o=Jc(),He.lanes|=o,Xr|=o,e.baseState=!0),t)}function pv(e,t){var o=Me;Me=o!==0&&4>o?o:4,e(!0);var i=Zl.transition;Zl.transition={};try{e(!1),t()}finally{Me=o,Zl.transition=i}}function Nf(){return Ht().memoizedState}function hv(e,t,o){var i=br(e);if(o={lane:i,action:o,hasEagerState:!1,eagerState:null,next:null},Sf(e))Cf(t,o);else if(o=tf(e,t,o,i),o!==null){var u=xt();an(o,e,i,u),Ef(o,t,i)}}function mv(e,t,o){var i=br(e),u={lane:i,action:o,hasEagerState:!1,eagerState:null,next:null};if(Sf(e))Cf(t,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var g=t.lastRenderedState,S=d(g,o);if(u.hasEagerState=!0,u.eagerState=S,tn(S,g)){var C=t.interleaved;C===null?(u.next=u,ql(t)):(u.next=C.next,C.next=u),t.interleaved=u;return}}catch{}finally{}o=tf(e,t,u,i),o!==null&&(u=xt(),an(o,e,i,u),Ef(o,t,i))}}function Sf(e){var t=e.alternate;return e===He||t!==null&&t===He}function Cf(e,t){Ds=Zi=!0;var o=e.pending;o===null?t.next=t:(t.next=o.next,o.next=t),e.pending=t}function Ef(e,t,o){if((o&4194240)!==0){var i=t.lanes;i&=e.pendingLanes,o|=i,t.lanes=o,ll(e,o)}}var na={readContext:Vt,useCallback:dt,useContext:dt,useEffect:dt,useImperativeHandle:dt,useInsertionEffect:dt,useLayoutEffect:dt,useMemo:dt,useReducer:dt,useRef:dt,useState:dt,useDebugValue:dt,useDeferredValue:dt,useTransition:dt,useMutableSource:dt,useSyncExternalStore:dt,useId:dt,unstable_isNewReconciler:!1},gv={readContext:Vt,useCallback:function(e,t){return En().memoizedState=[e,t===void 0?null:t],e},useContext:Vt,useEffect:gf,useImperativeHandle:function(e,t,o){return o=o!=null?o.concat([e]):null,ea(4194308,4,xf.bind(null,t,e),o)},useLayoutEffect:function(e,t){return ea(4194308,4,e,t)},useInsertionEffect:function(e,t){return ea(4,2,e,t)},useMemo:function(e,t){var o=En();return t=t===void 0?null:t,e=e(),o.memoizedState=[e,t],e},useReducer:function(e,t,o){var i=En();return t=o!==void 0?o(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=hv.bind(null,He,e),[i.memoizedState,e]},useRef:function(e){var t=En();return e={current:e},t.memoizedState=e},useState:hf,useDebugValue:iu,useDeferredValue:function(e){return En().memoizedState=e},useTransition:function(){var e=hf(!1),t=e[0];return e=pv.bind(null,e[1]),En().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,o){var i=He,u=En();if(Be){if(o===void 0)throw Error(s(407));o=o()}else{if(o=t(),ot===null)throw Error(s(349));(Yr&30)!==0||uf(i,t,o)}u.memoizedState=o;var d={value:o,getSnapshot:t};return u.queue=d,gf(df.bind(null,i,d,e),[e]),i.flags|=2048,Ls(9,cf.bind(null,i,d,o,t),void 0,null),o},useId:function(){var e=En(),t=ot.identifierPrefix;if(Be){var o=Bn,i=$n;o=(i&~(1<<32-en(i)-1)).toString(32)+o,t=":"+t+"R"+o,o=Is++,0<o&&(t+="H"+o.toString(32)),t+=":"}else o=fv++,t=":"+t+"r"+o.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},vv={readContext:Vt,useCallback:bf,useContext:Vt,useEffect:su,useImperativeHandle:wf,useInsertionEffect:vf,useLayoutEffect:yf,useMemo:jf,useReducer:ru,useRef:mf,useState:function(){return ru(Fs)},useDebugValue:iu,useDeferredValue:function(e){var t=Ht();return kf(t,et.memoizedState,e)},useTransition:function(){var e=ru(Fs)[0],t=Ht().memoizedState;return[e,t]},useMutableSource:af,useSyncExternalStore:lf,useId:Nf,unstable_isNewReconciler:!1},yv={readContext:Vt,useCallback:bf,useContext:Vt,useEffect:su,useImperativeHandle:wf,useInsertionEffect:vf,useLayoutEffect:yf,useMemo:jf,useReducer:ou,useRef:mf,useState:function(){return ou(Fs)},useDebugValue:iu,useDeferredValue:function(e){var t=Ht();return et===null?t.memoizedState=e:kf(t,et.memoizedState,e)},useTransition:function(){var e=ou(Fs)[0],t=Ht().memoizedState;return[e,t]},useMutableSource:af,useSyncExternalStore:lf,useId:Nf,unstable_isNewReconciler:!1};function rn(e,t){if(e&&e.defaultProps){t=q({},t),e=e.defaultProps;for(var o in e)t[o]===void 0&&(t[o]=e[o]);return t}return t}function au(e,t,o,i){t=e.memoizedState,o=o(i,t),o=o==null?t:q({},t,o),e.memoizedState=o,e.lanes===0&&(e.updateQueue.baseState=o)}var ra={isMounted:function(e){return(e=e._reactInternals)?Vr(e)===e:!1},enqueueSetState:function(e,t,o){e=e._reactInternals;var i=xt(),u=br(e),d=Hn(i,u);d.payload=t,o!=null&&(d.callback=o),t=vr(e,d,u),t!==null&&(an(t,e,u,i),Gi(t,e,u))},enqueueReplaceState:function(e,t,o){e=e._reactInternals;var i=xt(),u=br(e),d=Hn(i,u);d.tag=1,d.payload=t,o!=null&&(d.callback=o),t=vr(e,d,u),t!==null&&(an(t,e,u,i),Gi(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var o=xt(),i=br(e),u=Hn(o,i);u.tag=2,t!=null&&(u.callback=t),t=vr(e,u,i),t!==null&&(an(t,e,i,o),Gi(t,e,i))}};function Pf(e,t,o,i,u,d,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,d,g):t.prototype&&t.prototype.isPureReactComponent?!Ns(o,i)||!Ns(u,d):!0}function Tf(e,t,o){var i=!1,u=hr,d=t.contextType;return typeof d=="object"&&d!==null?d=Vt(d):(u=St(t)?Wr:ct.current,i=t.contextTypes,d=(i=i!=null)?To(e,u):hr),t=new t(o,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ra,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=d),t}function Rf(e,t,o,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(o,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(o,i),t.state!==e&&ra.enqueueReplaceState(t,t.state,null)}function lu(e,t,o,i){var u=e.stateNode;u.props=o,u.state=e.memoizedState,u.refs={},Kl(e);var d=t.contextType;typeof d=="object"&&d!==null?u.context=Vt(d):(d=St(t)?Wr:ct.current,u.context=To(e,d)),u.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(au(e,t,d,o),u.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(t=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),t!==u.state&&ra.enqueueReplaceState(u,u.state,null),Yi(e,o,u,i),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function Fo(e,t){try{var o="",i=t;do o+=he(i),i=i.return;while(i);var u=o}catch(d){u=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:u,digest:null}}function uu(e,t,o){return{value:e,source:null,stack:o??null,digest:t??null}}function cu(e,t){try{console.error(t.value)}catch(o){setTimeout(function(){throw o})}}var xv=typeof WeakMap=="function"?WeakMap:Map;function _f(e,t,o){o=Hn(-1,o),o.tag=3,o.payload={element:null};var i=t.value;return o.callback=function(){ca||(ca=!0,Su=i),cu(e,t)},o}function Af(e,t,o){o=Hn(-1,o),o.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var u=t.value;o.payload=function(){return i(u)},o.callback=function(){cu(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(o.callback=function(){cu(e,t),typeof i!="function"&&(xr===null?xr=new Set([this]):xr.add(this));var g=t.stack;this.componentDidCatch(t.value,{componentStack:g!==null?g:""})}),o}function Of(e,t,o){var i=e.pingCache;if(i===null){i=e.pingCache=new xv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(o)||(u.add(o),e=Ov.bind(null,e,t,o),t.then(e,e))}function Mf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Df(e,t,o,i,u){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,o.flags|=131072,o.flags&=-52805,o.tag===1&&(o.alternate===null?o.tag=17:(t=Hn(-1,1),t.tag=2,vr(o,t,1))),o.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var wv=z.ReactCurrentOwner,Ct=!1;function yt(e,t,o,i){t.child=e===null?ef(t,null,o,i):Oo(t,e.child,o,i)}function If(e,t,o,i,u){o=o.render;var d=t.ref;return Do(t,u),i=tu(e,t,o,i,d,u),o=nu(),e!==null&&!Ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,Wn(e,t,u)):(Be&&o&&Ll(t),t.flags|=1,yt(e,t,i,u),t.child)}function Ff(e,t,o,i,u){if(e===null){var d=o.type;return typeof d=="function"&&!Au(d)&&d.defaultProps===void 0&&o.compare===null&&o.defaultProps===void 0?(t.tag=15,t.type=d,Lf(e,t,d,i,u)):(e=ga(o.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&u)===0){var g=d.memoizedProps;if(o=o.compare,o=o!==null?o:Ns,o(g,i)&&e.ref===t.ref)return Wn(e,t,u)}return t.flags|=1,e=kr(d,i),e.ref=t.ref,e.return=t,t.child=e}function Lf(e,t,o,i,u){if(e!==null){var d=e.memoizedProps;if(Ns(d,i)&&e.ref===t.ref)if(Ct=!1,t.pendingProps=i=d,(e.lanes&u)!==0)(e.flags&131072)!==0&&(Ct=!0);else return t.lanes=e.lanes,Wn(e,t,u)}return du(e,t,o,i,u)}function zf(e,t,o){var i=t.pendingProps,u=i.children,d=e!==null?e.memoizedState:null;if(i.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Fe(zo,It),It|=o;else{if((o&1073741824)===0)return e=d!==null?d.baseLanes|o:o,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Fe(zo,It),It|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=d!==null?d.baseLanes:o,Fe(zo,It),It|=i}else d!==null?(i=d.baseLanes|o,t.memoizedState=null):i=o,Fe(zo,It),It|=i;return yt(e,t,u,o),t.child}function Uf(e,t){var o=t.ref;(e===null&&o!==null||e!==null&&e.ref!==o)&&(t.flags|=512,t.flags|=2097152)}function du(e,t,o,i,u){var d=St(o)?Wr:ct.current;return d=To(t,d),Do(t,u),o=tu(e,t,o,i,d,u),i=nu(),e!==null&&!Ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,Wn(e,t,u)):(Be&&i&&Ll(t),t.flags|=1,yt(e,t,o,u),t.child)}function $f(e,t,o,i,u){if(St(o)){var d=!0;$i(t)}else d=!1;if(Do(t,u),t.stateNode===null)sa(e,t),Tf(t,o,i),lu(t,o,i,u),i=!0;else if(e===null){var g=t.stateNode,S=t.memoizedProps;g.props=S;var C=g.context,I=o.contextType;typeof I=="object"&&I!==null?I=Vt(I):(I=St(o)?Wr:ct.current,I=To(t,I));var W=o.getDerivedStateFromProps,Q=typeof W=="function"||typeof g.getSnapshotBeforeUpdate=="function";Q||typeof g.UNSAFE_componentWillReceiveProps!="function"&&typeof g.componentWillReceiveProps!="function"||(S!==i||C!==I)&&Rf(t,g,i,I),gr=!1;var H=t.memoizedState;g.state=H,Yi(t,i,g,u),C=t.memoizedState,S!==i||H!==C||Nt.current||gr?(typeof W=="function"&&(au(t,o,W,i),C=t.memoizedState),(S=gr||Pf(t,o,S,i,H,C,I))?(Q||typeof g.UNSAFE_componentWillMount!="function"&&typeof g.componentWillMount!="function"||(typeof g.componentWillMount=="function"&&g.componentWillMount(),typeof g.UNSAFE_componentWillMount=="function"&&g.UNSAFE_componentWillMount()),typeof g.componentDidMount=="function"&&(t.flags|=4194308)):(typeof g.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=C),g.props=i,g.state=C,g.context=I,i=S):(typeof g.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{g=t.stateNode,nf(e,t),S=t.memoizedProps,I=t.type===t.elementType?S:rn(t.type,S),g.props=I,Q=t.pendingProps,H=g.context,C=o.contextType,typeof C=="object"&&C!==null?C=Vt(C):(C=St(o)?Wr:ct.current,C=To(t,C));var ne=o.getDerivedStateFromProps;(W=typeof ne=="function"||typeof g.getSnapshotBeforeUpdate=="function")||typeof g.UNSAFE_componentWillReceiveProps!="function"&&typeof g.componentWillReceiveProps!="function"||(S!==Q||H!==C)&&Rf(t,g,i,C),gr=!1,H=t.memoizedState,g.state=H,Yi(t,i,g,u);var ie=t.memoizedState;S!==Q||H!==ie||Nt.current||gr?(typeof ne=="function"&&(au(t,o,ne,i),ie=t.memoizedState),(I=gr||Pf(t,o,I,i,H,ie,C)||!1)?(W||typeof g.UNSAFE_componentWillUpdate!="function"&&typeof g.componentWillUpdate!="function"||(typeof g.componentWillUpdate=="function"&&g.componentWillUpdate(i,ie,C),typeof g.UNSAFE_componentWillUpdate=="function"&&g.UNSAFE_componentWillUpdate(i,ie,C)),typeof g.componentDidUpdate=="function"&&(t.flags|=4),typeof g.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof g.componentDidUpdate!="function"||S===e.memoizedProps&&H===e.memoizedState||(t.flags|=4),typeof g.getSnapshotBeforeUpdate!="function"||S===e.memoizedProps&&H===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=ie),g.props=i,g.state=ie,g.context=C,i=I):(typeof g.componentDidUpdate!="function"||S===e.memoizedProps&&H===e.memoizedState||(t.flags|=4),typeof g.getSnapshotBeforeUpdate!="function"||S===e.memoizedProps&&H===e.memoizedState||(t.flags|=1024),i=!1)}return fu(e,t,o,i,d,u)}function fu(e,t,o,i,u,d){Uf(e,t);var g=(t.flags&128)!==0;if(!i&&!g)return u&&Wd(t,o,!1),Wn(e,t,d);i=t.stateNode,wv.current=t;var S=g&&typeof o.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&g?(t.child=Oo(t,e.child,null,d),t.child=Oo(t,null,S,d)):yt(e,t,S,d),t.memoizedState=i.state,u&&Wd(t,o,!0),t.child}function Bf(e){var t=e.stateNode;t.pendingContext?Vd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Vd(e,t.context,!1),Gl(e,t.containerInfo)}function Vf(e,t,o,i,u){return Ao(),Bl(u),t.flags|=256,yt(e,t,o,i),t.child}var pu={dehydrated:null,treeContext:null,retryLane:0};function hu(e){return{baseLanes:e,cachePool:null,transitions:null}}function Hf(e,t,o){var i=t.pendingProps,u=Ve.current,d=!1,g=(t.flags&128)!==0,S;if((S=g)||(S=e!==null&&e.memoizedState===null?!1:(u&2)!==0),S?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),Fe(Ve,u&1),e===null)return $l(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(g=i.children,e=i.fallback,d?(i=t.mode,d=t.child,g={mode:"hidden",children:g},(i&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=g):d=va(g,i,0,null),e=to(e,i,o,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=hu(o),t.memoizedState=pu,e):mu(t,g));if(u=e.memoizedState,u!==null&&(S=u.dehydrated,S!==null))return bv(e,t,g,i,S,u,o);if(d){d=i.fallback,g=t.mode,u=e.child,S=u.sibling;var C={mode:"hidden",children:i.children};return(g&1)===0&&t.child!==u?(i=t.child,i.childLanes=0,i.pendingProps=C,t.deletions=null):(i=kr(u,C),i.subtreeFlags=u.subtreeFlags&14680064),S!==null?d=kr(S,d):(d=to(d,g,o,null),d.flags|=2),d.return=t,i.return=t,i.sibling=d,t.child=i,i=d,d=t.child,g=e.child.memoizedState,g=g===null?hu(o):{baseLanes:g.baseLanes|o,cachePool:null,transitions:g.transitions},d.memoizedState=g,d.childLanes=e.childLanes&~o,t.memoizedState=pu,i}return d=e.child,e=d.sibling,i=kr(d,{mode:"visible",children:i.children}),(t.mode&1)===0&&(i.lanes=o),i.return=t,i.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=i,t.memoizedState=null,i}function mu(e,t){return t=va({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function oa(e,t,o,i){return i!==null&&Bl(i),Oo(t,e.child,null,o),e=mu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function bv(e,t,o,i,u,d,g){if(o)return t.flags&256?(t.flags&=-257,i=uu(Error(s(422))),oa(e,t,g,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=i.fallback,u=t.mode,i=va({mode:"visible",children:i.children},u,0,null),d=to(d,u,g,null),d.flags|=2,i.return=t,d.return=t,i.sibling=d,t.child=i,(t.mode&1)!==0&&Oo(t,e.child,null,g),t.child.memoizedState=hu(g),t.memoizedState=pu,d);if((t.mode&1)===0)return oa(e,t,g,null);if(u.data==="$!"){if(i=u.nextSibling&&u.nextSibling.dataset,i)var S=i.dgst;return i=S,d=Error(s(419)),i=uu(d,i,void 0),oa(e,t,g,i)}if(S=(g&e.childLanes)!==0,Ct||S){if(i=ot,i!==null){switch(g&-g){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(i.suspendedLanes|g))!==0?0:u,u!==0&&u!==d.retryLane&&(d.retryLane=u,Vn(e,u),an(i,e,u,-1))}return _u(),i=uu(Error(s(421))),oa(e,t,g,i)}return u.data==="$?"?(t.flags|=128,t.child=e.child,t=Mv.bind(null,e),u._reactRetry=t,null):(e=d.treeContext,Dt=fr(u.nextSibling),Mt=t,Be=!0,nn=null,e!==null&&($t[Bt++]=$n,$t[Bt++]=Bn,$t[Bt++]=Qr,$n=e.id,Bn=e.overflow,Qr=t),t=mu(t,i.children),t.flags|=4096,t)}function Wf(e,t,o){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Ql(e.return,t,o)}function gu(e,t,o,i,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:o,tailMode:u}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=i,d.tail=o,d.tailMode=u)}function Qf(e,t,o){var i=t.pendingProps,u=i.revealOrder,d=i.tail;if(yt(e,t,i.children,o),i=Ve.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Wf(e,o,t);else if(e.tag===19)Wf(e,o,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(Fe(Ve,i),(t.mode&1)===0)t.memoizedState=null;else switch(u){case"forwards":for(o=t.child,u=null;o!==null;)e=o.alternate,e!==null&&Xi(e)===null&&(u=o),o=o.sibling;o=u,o===null?(u=t.child,t.child=null):(u=o.sibling,o.sibling=null),gu(t,!1,u,o,d);break;case"backwards":for(o=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Xi(e)===null){t.child=u;break}e=u.sibling,u.sibling=o,o=u,u=e}gu(t,!0,o,null,d);break;case"together":gu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function sa(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Wn(e,t,o){if(e!==null&&(t.dependencies=e.dependencies),Xr|=t.lanes,(o&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,o=kr(e,e.pendingProps),t.child=o,o.return=t;e.sibling!==null;)e=e.sibling,o=o.sibling=kr(e,e.pendingProps),o.return=t;o.sibling=null}return t.child}function jv(e,t,o){switch(t.tag){case 3:Bf(t),Ao();break;case 5:sf(t);break;case 1:St(t.type)&&$i(t);break;case 4:Gl(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,u=t.memoizedProps.value;Fe(qi,i._currentValue),i._currentValue=u;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(Fe(Ve,Ve.current&1),t.flags|=128,null):(o&t.child.childLanes)!==0?Hf(e,t,o):(Fe(Ve,Ve.current&1),e=Wn(e,t,o),e!==null?e.sibling:null);Fe(Ve,Ve.current&1);break;case 19:if(i=(o&t.childLanes)!==0,(e.flags&128)!==0){if(i)return Qf(e,t,o);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Fe(Ve,Ve.current),i)break;return null;case 22:case 23:return t.lanes=0,zf(e,t,o)}return Wn(e,t,o)}var qf,vu,Kf,Gf;qf=function(e,t){for(var o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break;for(;o.sibling===null;){if(o.return===null||o.return===t)return;o=o.return}o.sibling.return=o.return,o=o.sibling}},vu=function(){},Kf=function(e,t,o,i){var u=e.memoizedProps;if(u!==i){e=t.stateNode,Gr(Cn.current);var d=null;switch(o){case"input":u=nr(e,u),i=nr(e,i),d=[];break;case"select":u=q({},u,{value:void 0}),i=q({},i,{value:void 0}),d=[];break;case"textarea":u=mo(e,u),i=mo(e,i),d=[];break;default:typeof u.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=Li)}bn(o,i);var g;o=null;for(I in u)if(!i.hasOwnProperty(I)&&u.hasOwnProperty(I)&&u[I]!=null)if(I==="style"){var S=u[I];for(g in S)S.hasOwnProperty(g)&&(o||(o={}),o[g]="")}else I!=="dangerouslySetInnerHTML"&&I!=="children"&&I!=="suppressContentEditableWarning"&&I!=="suppressHydrationWarning"&&I!=="autoFocus"&&(c.hasOwnProperty(I)?d||(d=[]):(d=d||[]).push(I,null));for(I in i){var C=i[I];if(S=u!=null?u[I]:void 0,i.hasOwnProperty(I)&&C!==S&&(C!=null||S!=null))if(I==="style")if(S){for(g in S)!S.hasOwnProperty(g)||C&&C.hasOwnProperty(g)||(o||(o={}),o[g]="");for(g in C)C.hasOwnProperty(g)&&S[g]!==C[g]&&(o||(o={}),o[g]=C[g])}else o||(d||(d=[]),d.push(I,o)),o=C;else I==="dangerouslySetInnerHTML"?(C=C?C.__html:void 0,S=S?S.__html:void 0,C!=null&&S!==C&&(d=d||[]).push(I,C)):I==="children"?typeof C!="string"&&typeof C!="number"||(d=d||[]).push(I,""+C):I!=="suppressContentEditableWarning"&&I!=="suppressHydrationWarning"&&(c.hasOwnProperty(I)?(C!=null&&I==="onScroll"&&Le("scroll",e),d||S===C||(d=[])):(d=d||[]).push(I,C))}o&&(d=d||[]).push("style",o);var I=d;(t.updateQueue=I)&&(t.flags|=4)}},Gf=function(e,t,o,i){o!==i&&(t.flags|=4)};function zs(e,t){if(!Be)switch(e.tailMode){case"hidden":t=e.tail;for(var o=null;t!==null;)t.alternate!==null&&(o=t),t=t.sibling;o===null?e.tail=null:o.sibling=null;break;case"collapsed":o=e.tail;for(var i=null;o!==null;)o.alternate!==null&&(i=o),o=o.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function ft(e){var t=e.alternate!==null&&e.alternate.child===e.child,o=0,i=0;if(t)for(var u=e.child;u!==null;)o|=u.lanes|u.childLanes,i|=u.subtreeFlags&14680064,i|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)o|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=o,t}function kv(e,t,o){var i=t.pendingProps;switch(zl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ft(t),null;case 1:return St(t.type)&&Ui(),ft(t),null;case 3:return i=t.stateNode,Io(),ze(Nt),ze(ct),Jl(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&(Wi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,nn!==null&&(Pu(nn),nn=null))),vu(e,t),ft(t),null;case 5:Yl(t);var u=Gr(Ms.current);if(o=t.type,e!==null&&t.stateNode!=null)Kf(e,t,o,i,u),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(s(166));return ft(t),null}if(e=Gr(Cn.current),Wi(t)){i=t.stateNode,o=t.type;var d=t.memoizedProps;switch(i[Sn]=t,i[Ts]=d,e=(t.mode&1)!==0,o){case"dialog":Le("cancel",i),Le("close",i);break;case"iframe":case"object":case"embed":Le("load",i);break;case"video":case"audio":for(u=0;u<Cs.length;u++)Le(Cs[u],i);break;case"source":Le("error",i);break;case"img":case"image":case"link":Le("error",i),Le("load",i);break;case"details":Le("toggle",i);break;case"input":ho(i,d),Le("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!d.multiple},Le("invalid",i);break;case"textarea":yn(i,d),Le("invalid",i)}bn(o,d),u=null;for(var g in d)if(d.hasOwnProperty(g)){var S=d[g];g==="children"?typeof S=="string"?i.textContent!==S&&(d.suppressHydrationWarning!==!0&&Fi(i.textContent,S,e),u=["children",S]):typeof S=="number"&&i.textContent!==""+S&&(d.suppressHydrationWarning!==!0&&Fi(i.textContent,S,e),u=["children",""+S]):c.hasOwnProperty(g)&&S!=null&&g==="onScroll"&&Le("scroll",i)}switch(o){case"input":_t(i),rr(i,d,!0);break;case"textarea":_t(i),fi(i);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(i.onclick=Li)}i=u,t.updateQueue=i,i!==null&&(t.flags|=4)}else{g=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gt(o)),e==="http://www.w3.org/1999/xhtml"?o==="script"?(e=g.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=g.createElement(o,{is:i.is}):(e=g.createElement(o),o==="select"&&(g=e,i.multiple?g.multiple=!0:i.size&&(g.size=i.size))):e=g.createElementNS(e,o),e[Sn]=t,e[Ts]=i,qf(e,t,!1,!1),t.stateNode=e;e:{switch(g=us(o,i),o){case"dialog":Le("cancel",e),Le("close",e),u=i;break;case"iframe":case"object":case"embed":Le("load",e),u=i;break;case"video":case"audio":for(u=0;u<Cs.length;u++)Le(Cs[u],e);u=i;break;case"source":Le("error",e),u=i;break;case"img":case"image":case"link":Le("error",e),Le("load",e),u=i;break;case"details":Le("toggle",e),u=i;break;case"input":ho(e,i),u=nr(e,i),Le("invalid",e);break;case"option":u=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},u=q({},i,{value:void 0}),Le("invalid",e);break;case"textarea":yn(e,i),u=mo(e,i),Le("invalid",e);break;default:u=i}bn(o,u),S=u;for(d in S)if(S.hasOwnProperty(d)){var C=S[d];d==="style"?Ln(e,C):d==="dangerouslySetInnerHTML"?(C=C?C.__html:void 0,C!=null&&pi(e,C)):d==="children"?typeof C=="string"?(o!=="textarea"||C!=="")&&wn(e,C):typeof C=="number"&&wn(e,""+C):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(c.hasOwnProperty(d)?C!=null&&d==="onScroll"&&Le("scroll",e):C!=null&&A(e,d,C,g))}switch(o){case"input":_t(e),rr(e,i,!1);break;case"textarea":_t(e),fi(e);break;case"option":i.value!=null&&e.setAttribute("value",""+Ce(i.value));break;case"select":e.multiple=!!i.multiple,d=i.value,d!=null?Ut(e,!!i.multiple,d,!1):i.defaultValue!=null&&Ut(e,!!i.multiple,i.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=Li)}switch(o){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ft(t),null;case 6:if(e&&t.stateNode!=null)Gf(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(s(166));if(o=Gr(Ms.current),Gr(Cn.current),Wi(t)){if(i=t.stateNode,o=t.memoizedProps,i[Sn]=t,(d=i.nodeValue!==o)&&(e=Mt,e!==null))switch(e.tag){case 3:Fi(i.nodeValue,o,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Fi(i.nodeValue,o,(e.mode&1)!==0)}d&&(t.flags|=4)}else i=(o.nodeType===9?o:o.ownerDocument).createTextNode(i),i[Sn]=t,t.stateNode=i}return ft(t),null;case 13:if(ze(Ve),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Be&&Dt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Xd(),Ao(),t.flags|=98560,d=!1;else if(d=Wi(t),i!==null&&i.dehydrated!==null){if(e===null){if(!d)throw Error(s(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(s(317));d[Sn]=t}else Ao(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ft(t),d=!1}else nn!==null&&(Pu(nn),nn=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=o,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Ve.current&1)!==0?tt===0&&(tt=3):_u())),t.updateQueue!==null&&(t.flags|=4),ft(t),null);case 4:return Io(),vu(e,t),e===null&&Es(t.stateNode.containerInfo),ft(t),null;case 10:return Wl(t.type._context),ft(t),null;case 17:return St(t.type)&&Ui(),ft(t),null;case 19:if(ze(Ve),d=t.memoizedState,d===null)return ft(t),null;if(i=(t.flags&128)!==0,g=d.rendering,g===null)if(i)zs(d,!1);else{if(tt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(g=Xi(e),g!==null){for(t.flags|=128,zs(d,!1),i=g.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=o,o=t.child;o!==null;)d=o,e=i,d.flags&=14680066,g=d.alternate,g===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=g.childLanes,d.lanes=g.lanes,d.child=g.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=g.memoizedProps,d.memoizedState=g.memoizedState,d.updateQueue=g.updateQueue,d.type=g.type,e=g.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),o=o.sibling;return Fe(Ve,Ve.current&1|2),t.child}e=e.sibling}d.tail!==null&&Ye()>Uo&&(t.flags|=128,i=!0,zs(d,!1),t.lanes=4194304)}else{if(!i)if(e=Xi(g),e!==null){if(t.flags|=128,i=!0,o=e.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),zs(d,!0),d.tail===null&&d.tailMode==="hidden"&&!g.alternate&&!Be)return ft(t),null}else 2*Ye()-d.renderingStartTime>Uo&&o!==1073741824&&(t.flags|=128,i=!0,zs(d,!1),t.lanes=4194304);d.isBackwards?(g.sibling=t.child,t.child=g):(o=d.last,o!==null?o.sibling=g:t.child=g,d.last=g)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=Ye(),t.sibling=null,o=Ve.current,Fe(Ve,i?o&1|2:o&1),t):(ft(t),null);case 22:case 23:return Ru(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&(t.mode&1)!==0?(It&1073741824)!==0&&(ft(t),t.subtreeFlags&6&&(t.flags|=8192)):ft(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function Nv(e,t){switch(zl(t),t.tag){case 1:return St(t.type)&&Ui(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Io(),ze(Nt),ze(ct),Jl(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Yl(t),null;case 13:if(ze(Ve),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Ao()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ze(Ve),null;case 4:return Io(),null;case 10:return Wl(t.type._context),null;case 22:case 23:return Ru(),null;case 24:return null;default:return null}}var ia=!1,pt=!1,Sv=typeof WeakSet=="function"?WeakSet:Set,oe=null;function Lo(e,t){var o=e.ref;if(o!==null)if(typeof o=="function")try{o(null)}catch(i){We(e,t,i)}else o.current=null}function yu(e,t,o){try{o()}catch(i){We(e,t,i)}}var Yf=!1;function Cv(e,t){if(Rl=Ci,e=Ed(),jl(e)){if("selectionStart"in e)var o={start:e.selectionStart,end:e.selectionEnd};else e:{o=(o=e.ownerDocument)&&o.defaultView||window;var i=o.getSelection&&o.getSelection();if(i&&i.rangeCount!==0){o=i.anchorNode;var u=i.anchorOffset,d=i.focusNode;i=i.focusOffset;try{o.nodeType,d.nodeType}catch{o=null;break e}var g=0,S=-1,C=-1,I=0,W=0,Q=e,H=null;t:for(;;){for(var ne;Q!==o||u!==0&&Q.nodeType!==3||(S=g+u),Q!==d||i!==0&&Q.nodeType!==3||(C=g+i),Q.nodeType===3&&(g+=Q.nodeValue.length),(ne=Q.firstChild)!==null;)H=Q,Q=ne;for(;;){if(Q===e)break t;if(H===o&&++I===u&&(S=g),H===d&&++W===i&&(C=g),(ne=Q.nextSibling)!==null)break;Q=H,H=Q.parentNode}Q=ne}o=S===-1||C===-1?null:{start:S,end:C}}else o=null}o=o||{start:0,end:0}}else o=null;for(_l={focusedElem:e,selectionRange:o},Ci=!1,oe=t;oe!==null;)if(t=oe,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,oe=e;else for(;oe!==null;){t=oe;try{var ie=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ie!==null){var ae=ie.memoizedProps,Xe=ie.memoizedState,M=t.stateNode,T=M.getSnapshotBeforeUpdate(t.elementType===t.type?ae:rn(t.type,ae),Xe);M.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var D=t.stateNode.containerInfo;D.nodeType===1?D.textContent="":D.nodeType===9&&D.documentElement&&D.removeChild(D.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(K){We(t,t.return,K)}if(e=t.sibling,e!==null){e.return=t.return,oe=e;break}oe=t.return}return ie=Yf,Yf=!1,ie}function Us(e,t,o){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var u=i=i.next;do{if((u.tag&e)===e){var d=u.destroy;u.destroy=void 0,d!==void 0&&yu(t,o,d)}u=u.next}while(u!==i)}}function aa(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var o=t=t.next;do{if((o.tag&e)===e){var i=o.create;o.destroy=i()}o=o.next}while(o!==t)}}function xu(e){var t=e.ref;if(t!==null){var o=e.stateNode;switch(e.tag){case 5:e=o;break;default:e=o}typeof t=="function"?t(e):t.current=e}}function Xf(e){var t=e.alternate;t!==null&&(e.alternate=null,Xf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Sn],delete t[Ts],delete t[Dl],delete t[lv],delete t[uv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Jf(e){return e.tag===5||e.tag===3||e.tag===4}function Zf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Jf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function wu(e,t,o){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?o.nodeType===8?o.parentNode.insertBefore(e,t):o.insertBefore(e,t):(o.nodeType===8?(t=o.parentNode,t.insertBefore(e,o)):(t=o,t.appendChild(e)),o=o._reactRootContainer,o!=null||t.onclick!==null||(t.onclick=Li));else if(i!==4&&(e=e.child,e!==null))for(wu(e,t,o),e=e.sibling;e!==null;)wu(e,t,o),e=e.sibling}function bu(e,t,o){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?o.insertBefore(e,t):o.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(bu(e,t,o),e=e.sibling;e!==null;)bu(e,t,o),e=e.sibling}var at=null,on=!1;function yr(e,t,o){for(o=o.child;o!==null;)ep(e,t,o),o=o.sibling}function ep(e,t,o){if(Nn&&typeof Nn.onCommitFiberUnmount=="function")try{Nn.onCommitFiberUnmount(wi,o)}catch{}switch(o.tag){case 5:pt||Lo(o,t);case 6:var i=at,u=on;at=null,yr(e,t,o),at=i,on=u,at!==null&&(on?(e=at,o=o.stateNode,e.nodeType===8?e.parentNode.removeChild(o):e.removeChild(o)):at.removeChild(o.stateNode));break;case 18:at!==null&&(on?(e=at,o=o.stateNode,e.nodeType===8?Ml(e.parentNode,o):e.nodeType===1&&Ml(e,o),ys(e)):Ml(at,o.stateNode));break;case 4:i=at,u=on,at=o.stateNode.containerInfo,on=!0,yr(e,t,o),at=i,on=u;break;case 0:case 11:case 14:case 15:if(!pt&&(i=o.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){u=i=i.next;do{var d=u,g=d.destroy;d=d.tag,g!==void 0&&((d&2)!==0||(d&4)!==0)&&yu(o,t,g),u=u.next}while(u!==i)}yr(e,t,o);break;case 1:if(!pt&&(Lo(o,t),i=o.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=o.memoizedProps,i.state=o.memoizedState,i.componentWillUnmount()}catch(S){We(o,t,S)}yr(e,t,o);break;case 21:yr(e,t,o);break;case 22:o.mode&1?(pt=(i=pt)||o.memoizedState!==null,yr(e,t,o),pt=i):yr(e,t,o);break;default:yr(e,t,o)}}function tp(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var o=e.stateNode;o===null&&(o=e.stateNode=new Sv),t.forEach(function(i){var u=Dv.bind(null,e,i);o.has(i)||(o.add(i),i.then(u,u))})}}function sn(e,t){var o=t.deletions;if(o!==null)for(var i=0;i<o.length;i++){var u=o[i];try{var d=e,g=t,S=g;e:for(;S!==null;){switch(S.tag){case 5:at=S.stateNode,on=!1;break e;case 3:at=S.stateNode.containerInfo,on=!0;break e;case 4:at=S.stateNode.containerInfo,on=!0;break e}S=S.return}if(at===null)throw Error(s(160));ep(d,g,u),at=null,on=!1;var C=u.alternate;C!==null&&(C.return=null),u.return=null}catch(I){We(u,t,I)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)np(t,e),t=t.sibling}function np(e,t){var o=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(sn(t,e),Pn(e),i&4){try{Us(3,e,e.return),aa(3,e)}catch(ae){We(e,e.return,ae)}try{Us(5,e,e.return)}catch(ae){We(e,e.return,ae)}}break;case 1:sn(t,e),Pn(e),i&512&&o!==null&&Lo(o,o.return);break;case 5:if(sn(t,e),Pn(e),i&512&&o!==null&&Lo(o,o.return),e.flags&32){var u=e.stateNode;try{wn(u,"")}catch(ae){We(e,e.return,ae)}}if(i&4&&(u=e.stateNode,u!=null)){var d=e.memoizedProps,g=o!==null?o.memoizedProps:d,S=e.type,C=e.updateQueue;if(e.updateQueue=null,C!==null)try{S==="input"&&d.type==="radio"&&d.name!=null&&Ur(u,d),us(S,g);var I=us(S,d);for(g=0;g<C.length;g+=2){var W=C[g],Q=C[g+1];W==="style"?Ln(u,Q):W==="dangerouslySetInnerHTML"?pi(u,Q):W==="children"?wn(u,Q):A(u,W,Q,I)}switch(S){case"input":$r(u,d);break;case"textarea":di(u,d);break;case"select":var H=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!d.multiple;var ne=d.value;ne!=null?Ut(u,!!d.multiple,ne,!1):H!==!!d.multiple&&(d.defaultValue!=null?Ut(u,!!d.multiple,d.defaultValue,!0):Ut(u,!!d.multiple,d.multiple?[]:"",!1))}u[Ts]=d}catch(ae){We(e,e.return,ae)}}break;case 6:if(sn(t,e),Pn(e),i&4){if(e.stateNode===null)throw Error(s(162));u=e.stateNode,d=e.memoizedProps;try{u.nodeValue=d}catch(ae){We(e,e.return,ae)}}break;case 3:if(sn(t,e),Pn(e),i&4&&o!==null&&o.memoizedState.isDehydrated)try{ys(t.containerInfo)}catch(ae){We(e,e.return,ae)}break;case 4:sn(t,e),Pn(e);break;case 13:sn(t,e),Pn(e),u=e.child,u.flags&8192&&(d=u.memoizedState!==null,u.stateNode.isHidden=d,!d||u.alternate!==null&&u.alternate.memoizedState!==null||(Nu=Ye())),i&4&&tp(e);break;case 22:if(W=o!==null&&o.memoizedState!==null,e.mode&1?(pt=(I=pt)||W,sn(t,e),pt=I):sn(t,e),Pn(e),i&8192){if(I=e.memoizedState!==null,(e.stateNode.isHidden=I)&&!W&&(e.mode&1)!==0)for(oe=e,W=e.child;W!==null;){for(Q=oe=W;oe!==null;){switch(H=oe,ne=H.child,H.tag){case 0:case 11:case 14:case 15:Us(4,H,H.return);break;case 1:Lo(H,H.return);var ie=H.stateNode;if(typeof ie.componentWillUnmount=="function"){i=H,o=H.return;try{t=i,ie.props=t.memoizedProps,ie.state=t.memoizedState,ie.componentWillUnmount()}catch(ae){We(i,o,ae)}}break;case 5:Lo(H,H.return);break;case 22:if(H.memoizedState!==null){sp(Q);continue}}ne!==null?(ne.return=H,oe=ne):sp(Q)}W=W.sibling}e:for(W=null,Q=e;;){if(Q.tag===5){if(W===null){W=Q;try{u=Q.stateNode,I?(d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(S=Q.stateNode,C=Q.memoizedProps.style,g=C!=null&&C.hasOwnProperty("display")?C.display:null,S.style.display=vo("display",g))}catch(ae){We(e,e.return,ae)}}}else if(Q.tag===6){if(W===null)try{Q.stateNode.nodeValue=I?"":Q.memoizedProps}catch(ae){We(e,e.return,ae)}}else if((Q.tag!==22&&Q.tag!==23||Q.memoizedState===null||Q===e)&&Q.child!==null){Q.child.return=Q,Q=Q.child;continue}if(Q===e)break e;for(;Q.sibling===null;){if(Q.return===null||Q.return===e)break e;W===Q&&(W=null),Q=Q.return}W===Q&&(W=null),Q.sibling.return=Q.return,Q=Q.sibling}}break;case 19:sn(t,e),Pn(e),i&4&&tp(e);break;case 21:break;default:sn(t,e),Pn(e)}}function Pn(e){var t=e.flags;if(t&2){try{e:{for(var o=e.return;o!==null;){if(Jf(o)){var i=o;break e}o=o.return}throw Error(s(160))}switch(i.tag){case 5:var u=i.stateNode;i.flags&32&&(wn(u,""),i.flags&=-33);var d=Zf(e);bu(e,d,u);break;case 3:case 4:var g=i.stateNode.containerInfo,S=Zf(e);wu(e,S,g);break;default:throw Error(s(161))}}catch(C){We(e,e.return,C)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ev(e,t,o){oe=e,rp(e)}function rp(e,t,o){for(var i=(e.mode&1)!==0;oe!==null;){var u=oe,d=u.child;if(u.tag===22&&i){var g=u.memoizedState!==null||ia;if(!g){var S=u.alternate,C=S!==null&&S.memoizedState!==null||pt;S=ia;var I=pt;if(ia=g,(pt=C)&&!I)for(oe=u;oe!==null;)g=oe,C=g.child,g.tag===22&&g.memoizedState!==null?ip(u):C!==null?(C.return=g,oe=C):ip(u);for(;d!==null;)oe=d,rp(d),d=d.sibling;oe=u,ia=S,pt=I}op(e)}else(u.subtreeFlags&8772)!==0&&d!==null?(d.return=u,oe=d):op(e)}}function op(e){for(;oe!==null;){var t=oe;if((t.flags&8772)!==0){var o=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:pt||aa(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!pt)if(o===null)i.componentDidMount();else{var u=t.elementType===t.type?o.memoizedProps:rn(t.type,o.memoizedProps);i.componentDidUpdate(u,o.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&of(t,d,i);break;case 3:var g=t.updateQueue;if(g!==null){if(o=null,t.child!==null)switch(t.child.tag){case 5:o=t.child.stateNode;break;case 1:o=t.child.stateNode}of(t,g,o)}break;case 5:var S=t.stateNode;if(o===null&&t.flags&4){o=S;var C=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":C.autoFocus&&o.focus();break;case"img":C.src&&(o.src=C.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var I=t.alternate;if(I!==null){var W=I.memoizedState;if(W!==null){var Q=W.dehydrated;Q!==null&&ys(Q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}pt||t.flags&512&&xu(t)}catch(H){We(t,t.return,H)}}if(t===e){oe=null;break}if(o=t.sibling,o!==null){o.return=t.return,oe=o;break}oe=t.return}}function sp(e){for(;oe!==null;){var t=oe;if(t===e){oe=null;break}var o=t.sibling;if(o!==null){o.return=t.return,oe=o;break}oe=t.return}}function ip(e){for(;oe!==null;){var t=oe;try{switch(t.tag){case 0:case 11:case 15:var o=t.return;try{aa(4,t)}catch(C){We(t,o,C)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var u=t.return;try{i.componentDidMount()}catch(C){We(t,u,C)}}var d=t.return;try{xu(t)}catch(C){We(t,d,C)}break;case 5:var g=t.return;try{xu(t)}catch(C){We(t,g,C)}}}catch(C){We(t,t.return,C)}if(t===e){oe=null;break}var S=t.sibling;if(S!==null){S.return=t.return,oe=S;break}oe=t.return}}var Pv=Math.ceil,la=z.ReactCurrentDispatcher,ju=z.ReactCurrentOwner,Wt=z.ReactCurrentBatchConfig,Te=0,ot=null,Je=null,lt=0,It=0,zo=pr(0),tt=0,$s=null,Xr=0,ua=0,ku=0,Bs=null,Et=null,Nu=0,Uo=1/0,Qn=null,ca=!1,Su=null,xr=null,da=!1,wr=null,fa=0,Vs=0,Cu=null,pa=-1,ha=0;function xt(){return(Te&6)!==0?Ye():pa!==-1?pa:pa=Ye()}function br(e){return(e.mode&1)===0?1:(Te&2)!==0&&lt!==0?lt&-lt:dv.transition!==null?(ha===0&&(ha=Jc()),ha):(e=Me,e!==0||(e=window.event,e=e===void 0?16:ad(e.type)),e)}function an(e,t,o,i){if(50<Vs)throw Vs=0,Cu=null,Error(s(185));ps(e,o,i),((Te&2)===0||e!==ot)&&(e===ot&&((Te&2)===0&&(ua|=o),tt===4&&jr(e,lt)),Pt(e,i),o===1&&Te===0&&(t.mode&1)===0&&(Uo=Ye()+500,Bi&&mr()))}function Pt(e,t){var o=e.callbackNode;d0(e,t);var i=ki(e,e===ot?lt:0);if(i===0)o!==null&&Gc(o),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(o!=null&&Gc(o),t===1)e.tag===0?cv(lp.bind(null,e)):Qd(lp.bind(null,e)),iv(function(){(Te&6)===0&&mr()}),o=null;else{switch(Zc(i)){case 1:o=sl;break;case 4:o=Yc;break;case 16:o=xi;break;case 536870912:o=Xc;break;default:o=xi}o=gp(o,ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=o}}function ap(e,t){if(pa=-1,ha=0,(Te&6)!==0)throw Error(s(327));var o=e.callbackNode;if($o()&&e.callbackNode!==o)return null;var i=ki(e,e===ot?lt:0);if(i===0)return null;if((i&30)!==0||(i&e.expiredLanes)!==0||t)t=ma(e,i);else{t=i;var u=Te;Te|=2;var d=cp();(ot!==e||lt!==t)&&(Qn=null,Uo=Ye()+500,Zr(e,t));do try{_v();break}catch(S){up(e,S)}while(!0);Hl(),la.current=d,Te=u,Je!==null?t=0:(ot=null,lt=0,t=tt)}if(t!==0){if(t===2&&(u=il(e),u!==0&&(i=u,t=Eu(e,u))),t===1)throw o=$s,Zr(e,0),jr(e,i),Pt(e,Ye()),o;if(t===6)jr(e,i);else{if(u=e.current.alternate,(i&30)===0&&!Tv(u)&&(t=ma(e,i),t===2&&(d=il(e),d!==0&&(i=d,t=Eu(e,d))),t===1))throw o=$s,Zr(e,0),jr(e,i),Pt(e,Ye()),o;switch(e.finishedWork=u,e.finishedLanes=i,t){case 0:case 1:throw Error(s(345));case 2:eo(e,Et,Qn);break;case 3:if(jr(e,i),(i&130023424)===i&&(t=Nu+500-Ye(),10<t)){if(ki(e,0)!==0)break;if(u=e.suspendedLanes,(u&i)!==i){xt(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=Ol(eo.bind(null,e,Et,Qn),t);break}eo(e,Et,Qn);break;case 4:if(jr(e,i),(i&4194240)===i)break;for(t=e.eventTimes,u=-1;0<i;){var g=31-en(i);d=1<<g,g=t[g],g>u&&(u=g),i&=~d}if(i=u,i=Ye()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Pv(i/1960))-i,10<i){e.timeoutHandle=Ol(eo.bind(null,e,Et,Qn),i);break}eo(e,Et,Qn);break;case 5:eo(e,Et,Qn);break;default:throw Error(s(329))}}}return Pt(e,Ye()),e.callbackNode===o?ap.bind(null,e):null}function Eu(e,t){var o=Bs;return e.current.memoizedState.isDehydrated&&(Zr(e,t).flags|=256),e=ma(e,t),e!==2&&(t=Et,Et=o,t!==null&&Pu(t)),e}function Pu(e){Et===null?Et=e:Et.push.apply(Et,e)}function Tv(e){for(var t=e;;){if(t.flags&16384){var o=t.updateQueue;if(o!==null&&(o=o.stores,o!==null))for(var i=0;i<o.length;i++){var u=o[i],d=u.getSnapshot;u=u.value;try{if(!tn(d(),u))return!1}catch{return!1}}}if(o=t.child,t.subtreeFlags&16384&&o!==null)o.return=t,t=o;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function jr(e,t){for(t&=~ku,t&=~ua,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var o=31-en(t),i=1<<o;e[o]=-1,t&=~i}}function lp(e){if((Te&6)!==0)throw Error(s(327));$o();var t=ki(e,0);if((t&1)===0)return Pt(e,Ye()),null;var o=ma(e,t);if(e.tag!==0&&o===2){var i=il(e);i!==0&&(t=i,o=Eu(e,i))}if(o===1)throw o=$s,Zr(e,0),jr(e,t),Pt(e,Ye()),o;if(o===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,eo(e,Et,Qn),Pt(e,Ye()),null}function Tu(e,t){var o=Te;Te|=1;try{return e(t)}finally{Te=o,Te===0&&(Uo=Ye()+500,Bi&&mr())}}function Jr(e){wr!==null&&wr.tag===0&&(Te&6)===0&&$o();var t=Te;Te|=1;var o=Wt.transition,i=Me;try{if(Wt.transition=null,Me=1,e)return e()}finally{Me=i,Wt.transition=o,Te=t,(Te&6)===0&&mr()}}function Ru(){It=zo.current,ze(zo)}function Zr(e,t){e.finishedWork=null,e.finishedLanes=0;var o=e.timeoutHandle;if(o!==-1&&(e.timeoutHandle=-1,sv(o)),Je!==null)for(o=Je.return;o!==null;){var i=o;switch(zl(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Ui();break;case 3:Io(),ze(Nt),ze(ct),Jl();break;case 5:Yl(i);break;case 4:Io();break;case 13:ze(Ve);break;case 19:ze(Ve);break;case 10:Wl(i.type._context);break;case 22:case 23:Ru()}o=o.return}if(ot=e,Je=e=kr(e.current,null),lt=It=t,tt=0,$s=null,ku=ua=Xr=0,Et=Bs=null,Kr!==null){for(t=0;t<Kr.length;t++)if(o=Kr[t],i=o.interleaved,i!==null){o.interleaved=null;var u=i.next,d=o.pending;if(d!==null){var g=d.next;d.next=u,i.next=g}o.pending=i}Kr=null}return e}function up(e,t){do{var o=Je;try{if(Hl(),Ji.current=na,Zi){for(var i=He.memoizedState;i!==null;){var u=i.queue;u!==null&&(u.pending=null),i=i.next}Zi=!1}if(Yr=0,rt=et=He=null,Ds=!1,Is=0,ju.current=null,o===null||o.return===null){tt=1,$s=t,Je=null;break}e:{var d=e,g=o.return,S=o,C=t;if(t=lt,S.flags|=32768,C!==null&&typeof C=="object"&&typeof C.then=="function"){var I=C,W=S,Q=W.tag;if((W.mode&1)===0&&(Q===0||Q===11||Q===15)){var H=W.alternate;H?(W.updateQueue=H.updateQueue,W.memoizedState=H.memoizedState,W.lanes=H.lanes):(W.updateQueue=null,W.memoizedState=null)}var ne=Mf(g);if(ne!==null){ne.flags&=-257,Df(ne,g,S,d,t),ne.mode&1&&Of(d,I,t),t=ne,C=I;var ie=t.updateQueue;if(ie===null){var ae=new Set;ae.add(C),t.updateQueue=ae}else ie.add(C);break e}else{if((t&1)===0){Of(d,I,t),_u();break e}C=Error(s(426))}}else if(Be&&S.mode&1){var Xe=Mf(g);if(Xe!==null){(Xe.flags&65536)===0&&(Xe.flags|=256),Df(Xe,g,S,d,t),Bl(Fo(C,S));break e}}d=C=Fo(C,S),tt!==4&&(tt=2),Bs===null?Bs=[d]:Bs.push(d),d=g;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var M=_f(d,C,t);rf(d,M);break e;case 1:S=C;var T=d.type,D=d.stateNode;if((d.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||D!==null&&typeof D.componentDidCatch=="function"&&(xr===null||!xr.has(D)))){d.flags|=65536,t&=-t,d.lanes|=t;var K=Af(d,S,t);rf(d,K);break e}}d=d.return}while(d!==null)}fp(o)}catch(ue){t=ue,Je===o&&o!==null&&(Je=o=o.return);continue}break}while(!0)}function cp(){var e=la.current;return la.current=na,e===null?na:e}function _u(){(tt===0||tt===3||tt===2)&&(tt=4),ot===null||(Xr&268435455)===0&&(ua&268435455)===0||jr(ot,lt)}function ma(e,t){var o=Te;Te|=2;var i=cp();(ot!==e||lt!==t)&&(Qn=null,Zr(e,t));do try{Rv();break}catch(u){up(e,u)}while(!0);if(Hl(),Te=o,la.current=i,Je!==null)throw Error(s(261));return ot=null,lt=0,tt}function Rv(){for(;Je!==null;)dp(Je)}function _v(){for(;Je!==null&&!n0();)dp(Je)}function dp(e){var t=mp(e.alternate,e,It);e.memoizedProps=e.pendingProps,t===null?fp(e):Je=t,ju.current=null}function fp(e){var t=e;do{var o=t.alternate;if(e=t.return,(t.flags&32768)===0){if(o=kv(o,t,It),o!==null){Je=o;return}}else{if(o=Nv(o,t),o!==null){o.flags&=32767,Je=o;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{tt=6,Je=null;return}}if(t=t.sibling,t!==null){Je=t;return}Je=t=e}while(t!==null);tt===0&&(tt=5)}function eo(e,t,o){var i=Me,u=Wt.transition;try{Wt.transition=null,Me=1,Av(e,t,o,i)}finally{Wt.transition=u,Me=i}return null}function Av(e,t,o,i){do $o();while(wr!==null);if((Te&6)!==0)throw Error(s(327));o=e.finishedWork;var u=e.finishedLanes;if(o===null)return null;if(e.finishedWork=null,e.finishedLanes=0,o===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var d=o.lanes|o.childLanes;if(f0(e,d),e===ot&&(Je=ot=null,lt=0),(o.subtreeFlags&2064)===0&&(o.flags&2064)===0||da||(da=!0,gp(xi,function(){return $o(),null})),d=(o.flags&15990)!==0,(o.subtreeFlags&15990)!==0||d){d=Wt.transition,Wt.transition=null;var g=Me;Me=1;var S=Te;Te|=4,ju.current=null,Cv(e,o),np(o,e),J0(_l),Ci=!!Rl,_l=Rl=null,e.current=o,Ev(o),r0(),Te=S,Me=g,Wt.transition=d}else e.current=o;if(da&&(da=!1,wr=e,fa=u),d=e.pendingLanes,d===0&&(xr=null),i0(o.stateNode),Pt(e,Ye()),t!==null)for(i=e.onRecoverableError,o=0;o<t.length;o++)u=t[o],i(u.value,{componentStack:u.stack,digest:u.digest});if(ca)throw ca=!1,e=Su,Su=null,e;return(fa&1)!==0&&e.tag!==0&&$o(),d=e.pendingLanes,(d&1)!==0?e===Cu?Vs++:(Vs=0,Cu=e):Vs=0,mr(),null}function $o(){if(wr!==null){var e=Zc(fa),t=Wt.transition,o=Me;try{if(Wt.transition=null,Me=16>e?16:e,wr===null)var i=!1;else{if(e=wr,wr=null,fa=0,(Te&6)!==0)throw Error(s(331));var u=Te;for(Te|=4,oe=e.current;oe!==null;){var d=oe,g=d.child;if((oe.flags&16)!==0){var S=d.deletions;if(S!==null){for(var C=0;C<S.length;C++){var I=S[C];for(oe=I;oe!==null;){var W=oe;switch(W.tag){case 0:case 11:case 15:Us(8,W,d)}var Q=W.child;if(Q!==null)Q.return=W,oe=Q;else for(;oe!==null;){W=oe;var H=W.sibling,ne=W.return;if(Xf(W),W===I){oe=null;break}if(H!==null){H.return=ne,oe=H;break}oe=ne}}}var ie=d.alternate;if(ie!==null){var ae=ie.child;if(ae!==null){ie.child=null;do{var Xe=ae.sibling;ae.sibling=null,ae=Xe}while(ae!==null)}}oe=d}}if((d.subtreeFlags&2064)!==0&&g!==null)g.return=d,oe=g;else e:for(;oe!==null;){if(d=oe,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:Us(9,d,d.return)}var M=d.sibling;if(M!==null){M.return=d.return,oe=M;break e}oe=d.return}}var T=e.current;for(oe=T;oe!==null;){g=oe;var D=g.child;if((g.subtreeFlags&2064)!==0&&D!==null)D.return=g,oe=D;else e:for(g=T;oe!==null;){if(S=oe,(S.flags&2048)!==0)try{switch(S.tag){case 0:case 11:case 15:aa(9,S)}}catch(ue){We(S,S.return,ue)}if(S===g){oe=null;break e}var K=S.sibling;if(K!==null){K.return=S.return,oe=K;break e}oe=S.return}}if(Te=u,mr(),Nn&&typeof Nn.onPostCommitFiberRoot=="function")try{Nn.onPostCommitFiberRoot(wi,e)}catch{}i=!0}return i}finally{Me=o,Wt.transition=t}}return!1}function pp(e,t,o){t=Fo(o,t),t=_f(e,t,1),e=vr(e,t,1),t=xt(),e!==null&&(ps(e,1,t),Pt(e,t))}function We(e,t,o){if(e.tag===3)pp(e,e,o);else for(;t!==null;){if(t.tag===3){pp(t,e,o);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(xr===null||!xr.has(i))){e=Fo(o,e),e=Af(t,e,1),t=vr(t,e,1),e=xt(),t!==null&&(ps(t,1,e),Pt(t,e));break}}t=t.return}}function Ov(e,t,o){var i=e.pingCache;i!==null&&i.delete(t),t=xt(),e.pingedLanes|=e.suspendedLanes&o,ot===e&&(lt&o)===o&&(tt===4||tt===3&&(lt&130023424)===lt&&500>Ye()-Nu?Zr(e,0):ku|=o),Pt(e,t)}function hp(e,t){t===0&&((e.mode&1)===0?t=1:(t=ji,ji<<=1,(ji&130023424)===0&&(ji=4194304)));var o=xt();e=Vn(e,t),e!==null&&(ps(e,t,o),Pt(e,o))}function Mv(e){var t=e.memoizedState,o=0;t!==null&&(o=t.retryLane),hp(e,o)}function Dv(e,t){var o=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(o=u.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(s(314))}i!==null&&i.delete(t),hp(e,o)}var mp;mp=function(e,t,o){if(e!==null)if(e.memoizedProps!==t.pendingProps||Nt.current)Ct=!0;else{if((e.lanes&o)===0&&(t.flags&128)===0)return Ct=!1,jv(e,t,o);Ct=(e.flags&131072)!==0}else Ct=!1,Be&&(t.flags&1048576)!==0&&qd(t,Hi,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;sa(e,t),e=t.pendingProps;var u=To(t,ct.current);Do(t,o),u=tu(null,t,i,e,u,o);var d=nu();return t.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,St(i)?(d=!0,$i(t)):d=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,Kl(t),u.updater=ra,t.stateNode=u,u._reactInternals=t,lu(t,i,e,o),t=fu(null,t,i,!0,d,o)):(t.tag=0,Be&&d&&Ll(t),yt(null,t,u,o),t=t.child),t;case 16:i=t.elementType;e:{switch(sa(e,t),e=t.pendingProps,u=i._init,i=u(i._payload),t.type=i,u=t.tag=Fv(i),e=rn(i,e),u){case 0:t=du(null,t,i,e,o);break e;case 1:t=$f(null,t,i,e,o);break e;case 11:t=If(null,t,i,e,o);break e;case 14:t=Ff(null,t,i,rn(i.type,e),o);break e}throw Error(s(306,i,""))}return t;case 0:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),du(e,t,i,u,o);case 1:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),$f(e,t,i,u,o);case 3:e:{if(Bf(t),e===null)throw Error(s(387));i=t.pendingProps,d=t.memoizedState,u=d.element,nf(e,t),Yi(t,i,null,o);var g=t.memoizedState;if(i=g.element,d.isDehydrated)if(d={element:i,isDehydrated:!1,cache:g.cache,pendingSuspenseBoundaries:g.pendingSuspenseBoundaries,transitions:g.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){u=Fo(Error(s(423)),t),t=Vf(e,t,i,o,u);break e}else if(i!==u){u=Fo(Error(s(424)),t),t=Vf(e,t,i,o,u);break e}else for(Dt=fr(t.stateNode.containerInfo.firstChild),Mt=t,Be=!0,nn=null,o=ef(t,null,i,o),t.child=o;o;)o.flags=o.flags&-3|4096,o=o.sibling;else{if(Ao(),i===u){t=Wn(e,t,o);break e}yt(e,t,i,o)}t=t.child}return t;case 5:return sf(t),e===null&&$l(t),i=t.type,u=t.pendingProps,d=e!==null?e.memoizedProps:null,g=u.children,Al(i,u)?g=null:d!==null&&Al(i,d)&&(t.flags|=32),Uf(e,t),yt(e,t,g,o),t.child;case 6:return e===null&&$l(t),null;case 13:return Hf(e,t,o);case 4:return Gl(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Oo(t,null,i,o):yt(e,t,i,o),t.child;case 11:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),If(e,t,i,u,o);case 7:return yt(e,t,t.pendingProps,o),t.child;case 8:return yt(e,t,t.pendingProps.children,o),t.child;case 12:return yt(e,t,t.pendingProps.children,o),t.child;case 10:e:{if(i=t.type._context,u=t.pendingProps,d=t.memoizedProps,g=u.value,Fe(qi,i._currentValue),i._currentValue=g,d!==null)if(tn(d.value,g)){if(d.children===u.children&&!Nt.current){t=Wn(e,t,o);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var S=d.dependencies;if(S!==null){g=d.child;for(var C=S.firstContext;C!==null;){if(C.context===i){if(d.tag===1){C=Hn(-1,o&-o),C.tag=2;var I=d.updateQueue;if(I!==null){I=I.shared;var W=I.pending;W===null?C.next=C:(C.next=W.next,W.next=C),I.pending=C}}d.lanes|=o,C=d.alternate,C!==null&&(C.lanes|=o),Ql(d.return,o,t),S.lanes|=o;break}C=C.next}}else if(d.tag===10)g=d.type===t.type?null:d.child;else if(d.tag===18){if(g=d.return,g===null)throw Error(s(341));g.lanes|=o,S=g.alternate,S!==null&&(S.lanes|=o),Ql(g,o,t),g=d.sibling}else g=d.child;if(g!==null)g.return=d;else for(g=d;g!==null;){if(g===t){g=null;break}if(d=g.sibling,d!==null){d.return=g.return,g=d;break}g=g.return}d=g}yt(e,t,u.children,o),t=t.child}return t;case 9:return u=t.type,i=t.pendingProps.children,Do(t,o),u=Vt(u),i=i(u),t.flags|=1,yt(e,t,i,o),t.child;case 14:return i=t.type,u=rn(i,t.pendingProps),u=rn(i.type,u),Ff(e,t,i,u,o);case 15:return Lf(e,t,t.type,t.pendingProps,o);case 17:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),sa(e,t),t.tag=1,St(i)?(e=!0,$i(t)):e=!1,Do(t,o),Tf(t,i,u),lu(t,i,u,o),fu(null,t,i,!0,e,o);case 19:return Qf(e,t,o);case 22:return zf(e,t,o)}throw Error(s(156,t.tag))};function gp(e,t){return Kc(e,t)}function Iv(e,t,o,i){this.tag=e,this.key=o,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qt(e,t,o,i){return new Iv(e,t,o,i)}function Au(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Fv(e){if(typeof e=="function")return Au(e)?1:0;if(e!=null){if(e=e.$$typeof,e===te)return 11;if(e===ve)return 14}return 2}function kr(e,t){var o=e.alternate;return o===null?(o=Qt(e.tag,t,e.key,e.mode),o.elementType=e.elementType,o.type=e.type,o.stateNode=e.stateNode,o.alternate=e,e.alternate=o):(o.pendingProps=t,o.type=e.type,o.flags=0,o.subtreeFlags=0,o.deletions=null),o.flags=e.flags&14680064,o.childLanes=e.childLanes,o.lanes=e.lanes,o.child=e.child,o.memoizedProps=e.memoizedProps,o.memoizedState=e.memoizedState,o.updateQueue=e.updateQueue,t=e.dependencies,o.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},o.sibling=e.sibling,o.index=e.index,o.ref=e.ref,o}function ga(e,t,o,i,u,d){var g=2;if(i=e,typeof e=="function")Au(e)&&(g=1);else if(typeof e=="string")g=5;else e:switch(e){case $:return to(o.children,u,d,t);case G:g=8,u|=8;break;case J:return e=Qt(12,o,t,u|2),e.elementType=J,e.lanes=d,e;case ge:return e=Qt(13,o,t,u),e.elementType=ge,e.lanes=d,e;case X:return e=Qt(19,o,t,u),e.elementType=X,e.lanes=d,e;case le:return va(o,u,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Z:g=10;break e;case pe:g=9;break e;case te:g=11;break e;case ve:g=14;break e;case se:g=16,i=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=Qt(g,o,t,u),t.elementType=e,t.type=i,t.lanes=d,t}function to(e,t,o,i){return e=Qt(7,e,i,t),e.lanes=o,e}function va(e,t,o,i){return e=Qt(22,e,i,t),e.elementType=le,e.lanes=o,e.stateNode={isHidden:!1},e}function Ou(e,t,o){return e=Qt(6,e,null,t),e.lanes=o,e}function Mu(e,t,o){return t=Qt(4,e.children!==null?e.children:[],e.key,t),t.lanes=o,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Lv(e,t,o,i,u){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=al(0),this.expirationTimes=al(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=al(0),this.identifierPrefix=i,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function Du(e,t,o,i,u,d,g,S,C){return e=new Lv(e,t,o,S,C),t===1?(t=1,d===!0&&(t|=8)):t=0,d=Qt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:i,isDehydrated:o,cache:null,transitions:null,pendingSuspenseBoundaries:null},Kl(d),e}function zv(e,t,o){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:F,key:i==null?null:""+i,children:e,containerInfo:t,implementation:o}}function vp(e){if(!e)return hr;e=e._reactInternals;e:{if(Vr(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(St(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var o=e.type;if(St(o))return Hd(e,o,t)}return t}function yp(e,t,o,i,u,d,g,S,C){return e=Du(o,i,!0,e,u,d,g,S,C),e.context=vp(null),o=e.current,i=xt(),u=br(o),d=Hn(i,u),d.callback=t??null,vr(o,d,u),e.current.lanes=u,ps(e,u,i),Pt(e,i),e}function ya(e,t,o,i){var u=t.current,d=xt(),g=br(u);return o=vp(o),t.context===null?t.context=o:t.pendingContext=o,t=Hn(d,g),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=vr(u,t,g),e!==null&&(an(e,u,g,d),Gi(e,u,g)),g}function xa(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function xp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var o=e.retryLane;e.retryLane=o!==0&&o<t?o:t}}function Iu(e,t){xp(e,t),(e=e.alternate)&&xp(e,t)}function Uv(){return null}var wp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fu(e){this._internalRoot=e}wa.prototype.render=Fu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));ya(e,t,null,null)},wa.prototype.unmount=Fu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Jr(function(){ya(null,e,null,null)}),t[zn]=null}};function wa(e){this._internalRoot=e}wa.prototype.unstable_scheduleHydration=function(e){if(e){var t=nd();e={blockedOn:null,target:e,priority:t};for(var o=0;o<ur.length&&t!==0&&t<ur[o].priority;o++);ur.splice(o,0,e),o===0&&sd(e)}};function Lu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ba(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function bp(){}function $v(e,t,o,i,u){if(u){if(typeof i=="function"){var d=i;i=function(){var I=xa(g);d.call(I)}}var g=yp(t,i,e,0,null,!1,!1,"",bp);return e._reactRootContainer=g,e[zn]=g.current,Es(e.nodeType===8?e.parentNode:e),Jr(),g}for(;u=e.lastChild;)e.removeChild(u);if(typeof i=="function"){var S=i;i=function(){var I=xa(C);S.call(I)}}var C=Du(e,0,!1,null,null,!1,!1,"",bp);return e._reactRootContainer=C,e[zn]=C.current,Es(e.nodeType===8?e.parentNode:e),Jr(function(){ya(t,C,o,i)}),C}function ja(e,t,o,i,u){var d=o._reactRootContainer;if(d){var g=d;if(typeof u=="function"){var S=u;u=function(){var C=xa(g);S.call(C)}}ya(t,g,e,u)}else g=$v(o,t,e,u,i);return xa(g)}ed=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var o=fs(t.pendingLanes);o!==0&&(ll(t,o|1),Pt(t,Ye()),(Te&6)===0&&(Uo=Ye()+500,mr()))}break;case 13:Jr(function(){var i=Vn(e,1);if(i!==null){var u=xt();an(i,e,1,u)}}),Iu(e,1)}},ul=function(e){if(e.tag===13){var t=Vn(e,134217728);if(t!==null){var o=xt();an(t,e,134217728,o)}Iu(e,134217728)}},td=function(e){if(e.tag===13){var t=br(e),o=Vn(e,t);if(o!==null){var i=xt();an(o,e,t,i)}Iu(e,t)}},nd=function(){return Me},rd=function(e,t){var o=Me;try{return Me=e,t()}finally{Me=o}},xo=function(e,t,o){switch(t){case"input":if($r(e,o),t=o.name,o.type==="radio"&&t!=null){for(o=e;o.parentNode;)o=o.parentNode;for(o=o.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<o.length;t++){var i=o[t];if(i!==e&&i.form===e.form){var u=zi(i);if(!u)throw Error(s(90));Fn(i),$r(i,u)}}}break;case"textarea":di(e,o);break;case"select":t=o.value,t!=null&&Ut(e,!!o.multiple,t,!1)}},Ae=Tu,Ie=Jr;var Bv={usingClientEntryPoint:!1,Events:[Rs,Eo,zi,gi,xe,Tu]},Hs={findFiberByHostInstance:Hr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Vv={bundleType:Hs.bundleType,version:Hs.version,rendererPackageName:Hs.rendererPackageName,rendererConfig:Hs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:z.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Qc(e),e===null?null:e.stateNode},findFiberByHostInstance:Hs.findFiberByHostInstance||Uv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ka=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ka.isDisabled&&ka.supportsFiber)try{wi=ka.inject(Vv),Nn=ka}catch{}}return Tt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Bv,Tt.createPortal=function(e,t){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Lu(t))throw Error(s(200));return zv(e,t,null,o)},Tt.createRoot=function(e,t){if(!Lu(e))throw Error(s(299));var o=!1,i="",u=wp;return t!=null&&(t.unstable_strictMode===!0&&(o=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(u=t.onRecoverableError)),t=Du(e,1,!1,null,null,o,!1,i,u),e[zn]=t.current,Es(e.nodeType===8?e.parentNode:e),new Fu(t)},Tt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=Qc(t),e=e===null?null:e.stateNode,e},Tt.flushSync=function(e){return Jr(e)},Tt.hydrate=function(e,t,o){if(!ba(t))throw Error(s(200));return ja(null,e,t,!0,o)},Tt.hydrateRoot=function(e,t,o){if(!Lu(e))throw Error(s(405));var i=o!=null&&o.hydratedSources||null,u=!1,d="",g=wp;if(o!=null&&(o.unstable_strictMode===!0&&(u=!0),o.identifierPrefix!==void 0&&(d=o.identifierPrefix),o.onRecoverableError!==void 0&&(g=o.onRecoverableError)),t=yp(t,null,e,1,o??null,u,!1,d,g),e[zn]=t.current,Es(e),i)for(e=0;e<i.length;e++)o=i[e],u=o._getVersion,u=u(o._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[o,u]:t.mutableSourceEagerHydrationData.push(o,u);return new wa(t)},Tt.render=function(e,t,o){if(!ba(t))throw Error(s(200));return ja(null,e,t,!1,o)},Tt.unmountComponentAtNode=function(e){if(!ba(e))throw Error(s(40));return e._reactRootContainer?(Jr(function(){ja(null,null,e,!1,function(){e._reactRootContainer=null,e[zn]=null})}),!0):!1},Tt.unstable_batchedUpdates=Tu,Tt.unstable_renderSubtreeIntoContainer=function(e,t,o,i){if(!ba(o))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return ja(e,t,o,!1,i)},Tt.version="18.3.1-next-f1338f8080-20240426",Tt}var Rp;function Rh(){if(Rp)return Bu.exports;Rp=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Bu.exports=Xv(),Bu.exports}var _p;function Jv(){if(_p)return Sa;_p=1;var n=Rh();return Sa.createRoot=n.createRoot,Sa.hydrateRoot=n.hydrateRoot,Sa}var Zv=Jv(),x=vc();const Y=Th(x),yc=Wv({__proto__:null,default:Y},[x]),ey=1,ty=1e6;let Wu=0;function ny(){return Wu=(Wu+1)%Number.MAX_SAFE_INTEGER,Wu.toString()}const Qu=new Map,Ap=n=>{if(Qu.has(n))return;const r=setTimeout(()=>{Qu.delete(n),Xs({type:"REMOVE_TOAST",toastId:n})},ty);Qu.set(n,r)},ry=(n,r)=>{switch(r.type){case"ADD_TOAST":return{...n,toasts:[r.toast,...n.toasts].slice(0,ey)};case"UPDATE_TOAST":return{...n,toasts:n.toasts.map(s=>s.id===r.toast.id?{...s,...r.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=r;return s?Ap(s):n.toasts.forEach(a=>{Ap(a.id)}),{...n,toasts:n.toasts.map(a=>a.id===s||s===void 0?{...a,open:!1}:a)}}case"REMOVE_TOAST":return r.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(s=>s.id!==r.toastId)}}},Aa=[];let Oa={toasts:[]};function Xs(n){Oa=ry(Oa,n),Aa.forEach(r=>{r(Oa)})}function Ze({...n}){const r=ny(),s=c=>Xs({type:"UPDATE_TOAST",toast:{...c,id:r}}),a=()=>Xs({type:"DISMISS_TOAST",toastId:r});return Xs({type:"ADD_TOAST",toast:{...n,id:r,open:!0,onOpenChange:c=>{c||a()}}}),{id:r,dismiss:a,update:s}}function oy(){const[n,r]=x.useState(Oa);return x.useEffect(()=>(Aa.push(r),()=>{const s=Aa.indexOf(r);s>-1&&Aa.splice(s,1)}),[n]),{...n,toast:Ze,dismiss:s=>Xs({type:"DISMISS_TOAST",toastId:s})}}var $a=Rh();const _h=Th($a);function Oe(n,r,{checkForDefaultPrevented:s=!0}={}){return function(c){if(n==null||n(c),s===!1||!c.defaultPrevented)return r==null?void 0:r(c)}}function Op(n,r){if(typeof n=="function")return n(r);n!=null&&(n.current=r)}function Ah(...n){return r=>{let s=!1;const a=n.map(c=>{const f=Op(c,r);return!s&&typeof f=="function"&&(s=!0),f});if(s)return()=>{for(let c=0;c<a.length;c++){const f=a[c];typeof f=="function"?f():Op(n[c],null)}}}}function ut(...n){return x.useCallback(Ah(...n),n)}function zr(n,r=[]){let s=[];function a(f,p){const h=x.createContext(p),m=s.length;s=[...s,p];const v=b=>{var _;const{scope:N,children:y,...P}=b,k=((_=N==null?void 0:N[n])==null?void 0:_[m])||h,j=x.useMemo(()=>P,Object.values(P));return l.jsx(k.Provider,{value:j,children:y})};v.displayName=f+"Provider";function w(b,N){var k;const y=((k=N==null?void 0:N[n])==null?void 0:k[m])||h,P=x.useContext(y);if(P)return P;if(p!==void 0)return p;throw new Error(`\`${b}\` must be used within \`${f}\``)}return[v,w]}const c=()=>{const f=s.map(p=>x.createContext(p));return function(h){const m=(h==null?void 0:h[n])||f;return x.useMemo(()=>({[`__scope${n}`]:{...h,[n]:m}}),[h,m])}};return c.scopeName=n,[a,sy(c,...r)]}function sy(...n){const r=n[0];if(n.length===1)return r;const s=()=>{const a=n.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(f){const p=a.reduce((h,{useScope:m,scopeName:v})=>{const b=m(f)[`__scope${v}`];return{...h,...b}},{});return x.useMemo(()=>({[`__scope${r.scopeName}`]:p}),[p])}};return s.scopeName=r.scopeName,s}function Da(n){const r=ay(n),s=x.forwardRef((a,c)=>{const{children:f,...p}=a,h=x.Children.toArray(f),m=h.find(uy);if(m){const v=m.props.children,w=h.map(b=>b===m?x.Children.count(v)>1?x.Children.only(null):x.isValidElement(v)?v.props.children:null:b);return l.jsx(r,{...p,ref:c,children:x.isValidElement(v)?x.cloneElement(v,void 0,w):null})}return l.jsx(r,{...p,ref:c,children:f})});return s.displayName=`${n}.Slot`,s}var iy=Da("Slot");function ay(n){const r=x.forwardRef((s,a)=>{const{children:c,...f}=s;if(x.isValidElement(c)){const p=dy(c),h=cy(f,c.props);return c.type!==x.Fragment&&(h.ref=a?Ah(a,p):p),x.cloneElement(c,h)}return x.Children.count(c)>1?x.Children.only(null):null});return r.displayName=`${n}.SlotClone`,r}var Oh=Symbol("radix.slottable");function ly(n){const r=({children:s})=>l.jsx(l.Fragment,{children:s});return r.displayName=`${n}.Slottable`,r.__radixId=Oh,r}function uy(n){return x.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===Oh}function cy(n,r){const s={...r};for(const a in r){const c=n[a],f=r[a];/^on[A-Z]/.test(a)?c&&f?s[a]=(...h)=>{const m=f(...h);return c(...h),m}:c&&(s[a]=c):a==="style"?s[a]={...c,...f}:a==="className"&&(s[a]=[c,f].filter(Boolean).join(" "))}return{...n,...s}}function dy(n){var a,c;let r=(a=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:a.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?n.ref:(r=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Mh(n){const r=n+"CollectionProvider",[s,a]=zr(r),[c,f]=s(r,{collectionRef:{current:null},itemMap:new Map}),p=k=>{const{scope:j,children:_}=k,E=Y.useRef(null),A=Y.useRef(new Map).current;return l.jsx(c,{scope:j,itemMap:A,collectionRef:E,children:_})};p.displayName=r;const h=n+"CollectionSlot",m=Da(h),v=Y.forwardRef((k,j)=>{const{scope:_,children:E}=k,A=f(h,_),z=ut(j,A.collectionRef);return l.jsx(m,{ref:z,children:E})});v.displayName=h;const w=n+"CollectionItemSlot",b="data-radix-collection-item",N=Da(w),y=Y.forwardRef((k,j)=>{const{scope:_,children:E,...A}=k,z=Y.useRef(null),O=ut(j,z),F=f(w,_);return Y.useEffect(()=>(F.itemMap.set(z,{ref:z,...A}),()=>void F.itemMap.delete(z))),l.jsx(N,{[b]:"",ref:O,children:E})});y.displayName=w;function P(k){const j=f(n+"CollectionConsumer",k);return Y.useCallback(()=>{const E=j.collectionRef.current;if(!E)return[];const A=Array.from(E.querySelectorAll(`[${b}]`));return Array.from(j.itemMap.values()).sort((F,$)=>A.indexOf(F.ref.current)-A.indexOf($.ref.current))},[j.collectionRef,j.itemMap])}return[{Provider:p,Slot:v,ItemSlot:y},P,a]}var fy=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],$e=fy.reduce((n,r)=>{const s=Da(`Primitive.${r}`),a=x.forwardRef((c,f)=>{const{asChild:p,...h}=c,m=p?s:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(m,{...h,ref:f})});return a.displayName=`Primitive.${r}`,{...n,[r]:a}},{});function Dh(n,r){n&&$a.flushSync(()=>n.dispatchEvent(r))}function Jn(n){const r=x.useRef(n);return x.useEffect(()=>{r.current=n}),x.useMemo(()=>(...s)=>{var a;return(a=r.current)==null?void 0:a.call(r,...s)},[])}function py(n,r=globalThis==null?void 0:globalThis.document){const s=Jn(n);x.useEffect(()=>{const a=c=>{c.key==="Escape"&&s(c)};return r.addEventListener("keydown",a,{capture:!0}),()=>r.removeEventListener("keydown",a,{capture:!0})},[s,r])}var hy="DismissableLayer",tc="dismissableLayer.update",my="dismissableLayer.pointerDownOutside",gy="dismissableLayer.focusOutside",Mp,Ih=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),xc=x.forwardRef((n,r)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:h,...m}=n,v=x.useContext(Ih),[w,b]=x.useState(null),N=(w==null?void 0:w.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=x.useState({}),P=ut(r,$=>b($)),k=Array.from(v.layers),[j]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),_=k.indexOf(j),E=w?k.indexOf(w):-1,A=v.layersWithOutsidePointerEventsDisabled.size>0,z=E>=_,O=yy($=>{const G=$.target,J=[...v.branches].some(Z=>Z.contains(G));!z||J||(c==null||c($),p==null||p($),$.defaultPrevented||h==null||h())},N),F=xy($=>{const G=$.target;[...v.branches].some(Z=>Z.contains(G))||(f==null||f($),p==null||p($),$.defaultPrevented||h==null||h())},N);return py($=>{E===v.layers.size-1&&(a==null||a($),!$.defaultPrevented&&h&&($.preventDefault(),h()))},N),x.useEffect(()=>{if(w)return s&&(v.layersWithOutsidePointerEventsDisabled.size===0&&(Mp=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(w)),v.layers.add(w),Dp(),()=>{s&&v.layersWithOutsidePointerEventsDisabled.size===1&&(N.body.style.pointerEvents=Mp)}},[w,N,s,v]),x.useEffect(()=>()=>{w&&(v.layers.delete(w),v.layersWithOutsidePointerEventsDisabled.delete(w),Dp())},[w,v]),x.useEffect(()=>{const $=()=>y({});return document.addEventListener(tc,$),()=>document.removeEventListener(tc,$)},[]),l.jsx($e.div,{...m,ref:P,style:{pointerEvents:A?z?"auto":"none":void 0,...n.style},onFocusCapture:Oe(n.onFocusCapture,F.onFocusCapture),onBlurCapture:Oe(n.onBlurCapture,F.onBlurCapture),onPointerDownCapture:Oe(n.onPointerDownCapture,O.onPointerDownCapture)})});xc.displayName=hy;var vy="DismissableLayerBranch",Fh=x.forwardRef((n,r)=>{const s=x.useContext(Ih),a=x.useRef(null),c=ut(r,a);return x.useEffect(()=>{const f=a.current;if(f)return s.branches.add(f),()=>{s.branches.delete(f)}},[s.branches]),l.jsx($e.div,{...n,ref:c})});Fh.displayName=vy;function yy(n,r=globalThis==null?void 0:globalThis.document){const s=Jn(n),a=x.useRef(!1),c=x.useRef(()=>{});return x.useEffect(()=>{const f=h=>{if(h.target&&!a.current){let m=function(){Lh(my,s,v,{discrete:!0})};const v={originalEvent:h};h.pointerType==="touch"?(r.removeEventListener("click",c.current),c.current=m,r.addEventListener("click",c.current,{once:!0})):m()}else r.removeEventListener("click",c.current);a.current=!1},p=window.setTimeout(()=>{r.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(p),r.removeEventListener("pointerdown",f),r.removeEventListener("click",c.current)}},[r,s]),{onPointerDownCapture:()=>a.current=!0}}function xy(n,r=globalThis==null?void 0:globalThis.document){const s=Jn(n),a=x.useRef(!1);return x.useEffect(()=>{const c=f=>{f.target&&!a.current&&Lh(gy,s,{originalEvent:f},{discrete:!1})};return r.addEventListener("focusin",c),()=>r.removeEventListener("focusin",c)},[r,s]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}function Dp(){const n=new CustomEvent(tc);document.dispatchEvent(n)}function Lh(n,r,s,{discrete:a}){const c=s.originalEvent.target,f=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});r&&c.addEventListener(n,r,{once:!0}),a?Dh(c,f):c.dispatchEvent(f)}var wy=xc,by=Fh,Zn=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},jy="Portal",zh=x.forwardRef((n,r)=>{var h;const{container:s,...a}=n,[c,f]=x.useState(!1);Zn(()=>f(!0),[]);const p=s||c&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return p?_h.createPortal(l.jsx($e.div,{...a,ref:r}),p):null});zh.displayName=jy;function ky(n,r){return x.useReducer((s,a)=>r[s][a]??s,n)}var ii=n=>{const{present:r,children:s}=n,a=Ny(r),c=typeof s=="function"?s({present:a.isPresent}):x.Children.only(s),f=ut(a.ref,Sy(c));return typeof s=="function"||a.isPresent?x.cloneElement(c,{ref:f}):null};ii.displayName="Presence";function Ny(n){const[r,s]=x.useState(),a=x.useRef(null),c=x.useRef(n),f=x.useRef("none"),p=n?"mounted":"unmounted",[h,m]=ky(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const v=Ca(a.current);f.current=h==="mounted"?v:"none"},[h]),Zn(()=>{const v=a.current,w=c.current;if(w!==n){const N=f.current,y=Ca(v);n?m("MOUNT"):y==="none"||(v==null?void 0:v.display)==="none"?m("UNMOUNT"):m(w&&N!==y?"ANIMATION_OUT":"UNMOUNT"),c.current=n}},[n,m]),Zn(()=>{if(r){let v;const w=r.ownerDocument.defaultView??window,b=y=>{const k=Ca(a.current).includes(y.animationName);if(y.target===r&&k&&(m("ANIMATION_END"),!c.current)){const j=r.style.animationFillMode;r.style.animationFillMode="forwards",v=w.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=j)})}},N=y=>{y.target===r&&(f.current=Ca(a.current))};return r.addEventListener("animationstart",N),r.addEventListener("animationcancel",b),r.addEventListener("animationend",b),()=>{w.clearTimeout(v),r.removeEventListener("animationstart",N),r.removeEventListener("animationcancel",b),r.removeEventListener("animationend",b)}}else m("ANIMATION_END")},[r,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:x.useCallback(v=>{a.current=v?getComputedStyle(v):null,s(v)},[])}}function Ca(n){return(n==null?void 0:n.animationName)||"none"}function Sy(n){var a,c;let r=(a=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:a.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?n.ref:(r=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var Cy=yc[" useInsertionEffect ".trim().toString()]||Zn;function Ba({prop:n,defaultProp:r,onChange:s=()=>{},caller:a}){const[c,f,p]=Ey({defaultProp:r,onChange:s}),h=n!==void 0,m=h?n:c;{const w=x.useRef(n!==void 0);x.useEffect(()=>{const b=w.current;b!==h&&console.warn(`${a} is changing from ${b?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),w.current=h},[h,a])}const v=x.useCallback(w=>{var b;if(h){const N=Py(w)?w(n):w;N!==n&&((b=p.current)==null||b.call(p,N))}else f(w)},[h,n,f,p]);return[m,v]}function Ey({defaultProp:n,onChange:r}){const[s,a]=x.useState(n),c=x.useRef(s),f=x.useRef(r);return Cy(()=>{f.current=r},[r]),x.useEffect(()=>{var p;c.current!==s&&((p=f.current)==null||p.call(f,s),c.current=s)},[s,c]),[s,a,f]}function Py(n){return typeof n=="function"}var Ty=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Ry="VisuallyHidden",Va=x.forwardRef((n,r)=>l.jsx($e.span,{...n,ref:r,style:{...Ty,...n.style}}));Va.displayName=Ry;var _y=Va,wc="ToastProvider",[bc,Ay,Oy]=Mh("Toast"),[Uh,J2]=zr("Toast",[Oy]),[My,Ha]=Uh(wc),$h=n=>{const{__scopeToast:r,label:s="Notification",duration:a=5e3,swipeDirection:c="right",swipeThreshold:f=50,children:p}=n,[h,m]=x.useState(null),[v,w]=x.useState(0),b=x.useRef(!1),N=x.useRef(!1);return s.trim()||console.error(`Invalid prop \`label\` supplied to \`${wc}\`. Expected non-empty \`string\`.`),l.jsx(bc.Provider,{scope:r,children:l.jsx(My,{scope:r,label:s,duration:a,swipeDirection:c,swipeThreshold:f,toastCount:v,viewport:h,onViewportChange:m,onToastAdd:x.useCallback(()=>w(y=>y+1),[]),onToastRemove:x.useCallback(()=>w(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:b,isClosePausedRef:N,children:p})})};$h.displayName=wc;var Bh="ToastViewport",Dy=["F8"],nc="toast.viewportPause",rc="toast.viewportResume",Vh=x.forwardRef((n,r)=>{const{__scopeToast:s,hotkey:a=Dy,label:c="Notifications ({hotkey})",...f}=n,p=Ha(Bh,s),h=Ay(s),m=x.useRef(null),v=x.useRef(null),w=x.useRef(null),b=x.useRef(null),N=ut(r,b,p.onViewportChange),y=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),P=p.toastCount>0;x.useEffect(()=>{const j=_=>{var A;a.length!==0&&a.every(z=>_[z]||_.code===z)&&((A=b.current)==null||A.focus())};return document.addEventListener("keydown",j),()=>document.removeEventListener("keydown",j)},[a]),x.useEffect(()=>{const j=m.current,_=b.current;if(P&&j&&_){const E=()=>{if(!p.isClosePausedRef.current){const F=new CustomEvent(nc);_.dispatchEvent(F),p.isClosePausedRef.current=!0}},A=()=>{if(p.isClosePausedRef.current){const F=new CustomEvent(rc);_.dispatchEvent(F),p.isClosePausedRef.current=!1}},z=F=>{!j.contains(F.relatedTarget)&&A()},O=()=>{j.contains(document.activeElement)||A()};return j.addEventListener("focusin",E),j.addEventListener("focusout",z),j.addEventListener("pointermove",E),j.addEventListener("pointerleave",O),window.addEventListener("blur",E),window.addEventListener("focus",A),()=>{j.removeEventListener("focusin",E),j.removeEventListener("focusout",z),j.removeEventListener("pointermove",E),j.removeEventListener("pointerleave",O),window.removeEventListener("blur",E),window.removeEventListener("focus",A)}}},[P,p.isClosePausedRef]);const k=x.useCallback(({tabbingDirection:j})=>{const E=h().map(A=>{const z=A.ref.current,O=[z,...Ky(z)];return j==="forwards"?O:O.reverse()});return(j==="forwards"?E.reverse():E).flat()},[h]);return x.useEffect(()=>{const j=b.current;if(j){const _=E=>{var O,F,$;const A=E.altKey||E.ctrlKey||E.metaKey;if(E.key==="Tab"&&!A){const G=document.activeElement,J=E.shiftKey;if(E.target===j&&J){(O=v.current)==null||O.focus();return}const te=k({tabbingDirection:J?"backwards":"forwards"}),ge=te.findIndex(X=>X===G);qu(te.slice(ge+1))?E.preventDefault():J?(F=v.current)==null||F.focus():($=w.current)==null||$.focus()}};return j.addEventListener("keydown",_),()=>j.removeEventListener("keydown",_)}},[h,k]),l.jsxs(by,{ref:m,role:"region","aria-label":c.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:P?void 0:"none"},children:[P&&l.jsx(oc,{ref:v,onFocusFromOutsideViewport:()=>{const j=k({tabbingDirection:"forwards"});qu(j)}}),l.jsx(bc.Slot,{scope:s,children:l.jsx($e.ol,{tabIndex:-1,...f,ref:N})}),P&&l.jsx(oc,{ref:w,onFocusFromOutsideViewport:()=>{const j=k({tabbingDirection:"backwards"});qu(j)}})]})});Vh.displayName=Bh;var Hh="ToastFocusProxy",oc=x.forwardRef((n,r)=>{const{__scopeToast:s,onFocusFromOutsideViewport:a,...c}=n,f=Ha(Hh,s);return l.jsx(Va,{"aria-hidden":!0,tabIndex:0,...c,ref:r,style:{position:"fixed"},onFocus:p=>{var v;const h=p.relatedTarget;!((v=f.viewport)!=null&&v.contains(h))&&a()}})});oc.displayName=Hh;var ai="Toast",Iy="toast.swipeStart",Fy="toast.swipeMove",Ly="toast.swipeCancel",zy="toast.swipeEnd",Wh=x.forwardRef((n,r)=>{const{forceMount:s,open:a,defaultOpen:c,onOpenChange:f,...p}=n,[h,m]=Ba({prop:a,defaultProp:c??!0,onChange:f,caller:ai});return l.jsx(ii,{present:s||h,children:l.jsx(By,{open:h,...p,ref:r,onClose:()=>m(!1),onPause:Jn(n.onPause),onResume:Jn(n.onResume),onSwipeStart:Oe(n.onSwipeStart,v=>{v.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Oe(n.onSwipeMove,v=>{const{x:w,y:b}=v.detail.delta;v.currentTarget.setAttribute("data-swipe","move"),v.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${w}px`),v.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${b}px`)}),onSwipeCancel:Oe(n.onSwipeCancel,v=>{v.currentTarget.setAttribute("data-swipe","cancel"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),v.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Oe(n.onSwipeEnd,v=>{const{x:w,y:b}=v.detail.delta;v.currentTarget.setAttribute("data-swipe","end"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),v.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${w}px`),v.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${b}px`),m(!1)})})})});Wh.displayName=ai;var[Uy,$y]=Uh(ai,{onClose(){}}),By=x.forwardRef((n,r)=>{const{__scopeToast:s,type:a="foreground",duration:c,open:f,onClose:p,onEscapeKeyDown:h,onPause:m,onResume:v,onSwipeStart:w,onSwipeMove:b,onSwipeCancel:N,onSwipeEnd:y,...P}=n,k=Ha(ai,s),[j,_]=x.useState(null),E=ut(r,X=>_(X)),A=x.useRef(null),z=x.useRef(null),O=c||k.duration,F=x.useRef(0),$=x.useRef(O),G=x.useRef(0),{onToastAdd:J,onToastRemove:Z}=k,pe=Jn(()=>{var ve;(j==null?void 0:j.contains(document.activeElement))&&((ve=k.viewport)==null||ve.focus()),p()}),te=x.useCallback(X=>{!X||X===1/0||(window.clearTimeout(G.current),F.current=new Date().getTime(),G.current=window.setTimeout(pe,X))},[pe]);x.useEffect(()=>{const X=k.viewport;if(X){const ve=()=>{te($.current),v==null||v()},se=()=>{const le=new Date().getTime()-F.current;$.current=$.current-le,window.clearTimeout(G.current),m==null||m()};return X.addEventListener(nc,se),X.addEventListener(rc,ve),()=>{X.removeEventListener(nc,se),X.removeEventListener(rc,ve)}}},[k.viewport,O,m,v,te]),x.useEffect(()=>{f&&!k.isClosePausedRef.current&&te(O)},[f,O,k.isClosePausedRef,te]),x.useEffect(()=>(J(),()=>Z()),[J,Z]);const ge=x.useMemo(()=>j?Jh(j):null,[j]);return k.viewport?l.jsxs(l.Fragment,{children:[ge&&l.jsx(Vy,{__scopeToast:s,role:"status","aria-live":a==="foreground"?"assertive":"polite","aria-atomic":!0,children:ge}),l.jsx(Uy,{scope:s,onClose:pe,children:$a.createPortal(l.jsx(bc.ItemSlot,{scope:s,children:l.jsx(wy,{asChild:!0,onEscapeKeyDown:Oe(h,()=>{k.isFocusedToastEscapeKeyDownRef.current||pe(),k.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx($e.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":f?"open":"closed","data-swipe-direction":k.swipeDirection,...P,ref:E,style:{userSelect:"none",touchAction:"none",...n.style},onKeyDown:Oe(n.onKeyDown,X=>{X.key==="Escape"&&(h==null||h(X.nativeEvent),X.nativeEvent.defaultPrevented||(k.isFocusedToastEscapeKeyDownRef.current=!0,pe()))}),onPointerDown:Oe(n.onPointerDown,X=>{X.button===0&&(A.current={x:X.clientX,y:X.clientY})}),onPointerMove:Oe(n.onPointerMove,X=>{if(!A.current)return;const ve=X.clientX-A.current.x,se=X.clientY-A.current.y,le=!!z.current,U=["left","right"].includes(k.swipeDirection),V=["left","up"].includes(k.swipeDirection)?Math.min:Math.max,q=U?V(0,ve):0,R=U?0:V(0,se),L=X.pointerType==="touch"?10:2,ee={x:q,y:R},re={originalEvent:X,delta:ee};le?(z.current=ee,Ea(Fy,b,re,{discrete:!1})):Ip(ee,k.swipeDirection,L)?(z.current=ee,Ea(Iy,w,re,{discrete:!1}),X.target.setPointerCapture(X.pointerId)):(Math.abs(ve)>L||Math.abs(se)>L)&&(A.current=null)}),onPointerUp:Oe(n.onPointerUp,X=>{const ve=z.current,se=X.target;if(se.hasPointerCapture(X.pointerId)&&se.releasePointerCapture(X.pointerId),z.current=null,A.current=null,ve){const le=X.currentTarget,U={originalEvent:X,delta:ve};Ip(ve,k.swipeDirection,k.swipeThreshold)?Ea(zy,y,U,{discrete:!0}):Ea(Ly,N,U,{discrete:!0}),le.addEventListener("click",V=>V.preventDefault(),{once:!0})}})})})}),k.viewport)})]}):null}),Vy=n=>{const{__scopeToast:r,children:s,...a}=n,c=Ha(ai,r),[f,p]=x.useState(!1),[h,m]=x.useState(!1);return Qy(()=>p(!0)),x.useEffect(()=>{const v=window.setTimeout(()=>m(!0),1e3);return()=>window.clearTimeout(v)},[]),h?null:l.jsx(zh,{asChild:!0,children:l.jsx(Va,{...a,children:f&&l.jsxs(l.Fragment,{children:[c.label," ",s]})})})},Hy="ToastTitle",Qh=x.forwardRef((n,r)=>{const{__scopeToast:s,...a}=n;return l.jsx($e.div,{...a,ref:r})});Qh.displayName=Hy;var Wy="ToastDescription",qh=x.forwardRef((n,r)=>{const{__scopeToast:s,...a}=n;return l.jsx($e.div,{...a,ref:r})});qh.displayName=Wy;var Kh="ToastAction",Gh=x.forwardRef((n,r)=>{const{altText:s,...a}=n;return s.trim()?l.jsx(Xh,{altText:s,asChild:!0,children:l.jsx(jc,{...a,ref:r})}):(console.error(`Invalid prop \`altText\` supplied to \`${Kh}\`. Expected non-empty \`string\`.`),null)});Gh.displayName=Kh;var Yh="ToastClose",jc=x.forwardRef((n,r)=>{const{__scopeToast:s,...a}=n,c=$y(Yh,s);return l.jsx(Xh,{asChild:!0,children:l.jsx($e.button,{type:"button",...a,ref:r,onClick:Oe(n.onClick,c.onClose)})})});jc.displayName=Yh;var Xh=x.forwardRef((n,r)=>{const{__scopeToast:s,altText:a,...c}=n;return l.jsx($e.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":a||void 0,...c,ref:r})});function Jh(n){const r=[];return Array.from(n.childNodes).forEach(a=>{if(a.nodeType===a.TEXT_NODE&&a.textContent&&r.push(a.textContent),qy(a)){const c=a.ariaHidden||a.hidden||a.style.display==="none",f=a.dataset.radixToastAnnounceExclude==="";if(!c)if(f){const p=a.dataset.radixToastAnnounceAlt;p&&r.push(p)}else r.push(...Jh(a))}}),r}function Ea(n,r,s,{discrete:a}){const c=s.originalEvent.currentTarget,f=new CustomEvent(n,{bubbles:!0,cancelable:!0,detail:s});r&&c.addEventListener(n,r,{once:!0}),a?Dh(c,f):c.dispatchEvent(f)}var Ip=(n,r,s=0)=>{const a=Math.abs(n.x),c=Math.abs(n.y),f=a>c;return r==="left"||r==="right"?f&&a>s:!f&&c>s};function Qy(n=()=>{}){const r=Jn(n);Zn(()=>{let s=0,a=0;return s=window.requestAnimationFrame(()=>a=window.requestAnimationFrame(r)),()=>{window.cancelAnimationFrame(s),window.cancelAnimationFrame(a)}},[r])}function qy(n){return n.nodeType===n.ELEMENT_NODE}function Ky(n){const r=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{const c=a.tagName==="INPUT"&&a.type==="hidden";return a.disabled||a.hidden||c?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)r.push(s.currentNode);return r}function qu(n){const r=document.activeElement;return n.some(s=>s===r?!0:(s.focus(),document.activeElement!==r))}var Gy=$h,Zh=Vh,em=Wh,tm=Qh,nm=qh,rm=Gh,om=jc;function sm(n){var r,s,a="";if(typeof n=="string"||typeof n=="number")a+=n;else if(typeof n=="object")if(Array.isArray(n)){var c=n.length;for(r=0;r<c;r++)n[r]&&(s=sm(n[r]))&&(a&&(a+=" "),a+=s)}else for(s in n)n[s]&&(a&&(a+=" "),a+=s);return a}function im(){for(var n,r,s=0,a="",c=arguments.length;s<c;s++)(n=arguments[s])&&(r=sm(n))&&(a&&(a+=" "),a+=r);return a}const Fp=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,Lp=im,kc=(n,r)=>s=>{var a;if((r==null?void 0:r.variants)==null)return Lp(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:c,defaultVariants:f}=r,p=Object.keys(c).map(v=>{const w=s==null?void 0:s[v],b=f==null?void 0:f[v];if(w===null)return null;const N=Fp(w)||Fp(b);return c[v][N]}),h=s&&Object.entries(s).reduce((v,w)=>{let[b,N]=w;return N===void 0||(v[b]=N),v},{}),m=r==null||(a=r.compoundVariants)===null||a===void 0?void 0:a.reduce((v,w)=>{let{class:b,className:N,...y}=w;return Object.entries(y).every(P=>{let[k,j]=P;return Array.isArray(j)?j.includes({...f,...h}[k]):{...f,...h}[k]===j})?[...v,b,N]:v},[]);return Lp(n,p,m,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),am=(...n)=>n.filter((r,s,a)=>!!r&&r.trim()!==""&&a.indexOf(r)===s).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Xy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=x.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:c="",children:f,iconNode:p,...h},m)=>x.createElement("svg",{ref:m,...Xy,width:r,height:r,stroke:n,strokeWidth:a?Number(s)*24/Number(r):s,className:am("lucide",c),...h},[...p.map(([v,w])=>x.createElement(v,w)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=(n,r)=>{const s=x.forwardRef(({className:a,...c},f)=>x.createElement(Jy,{ref:f,iconNode:r,className:am(`lucide-${Yy(n)}`,a),...c}));return s.displayName=`${n}`,s};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=De("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=De("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=De("Box",[["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nx=De("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rx=De("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=De("ChartColumnIncreasing",[["path",{d:"M13 17V9",key:"1fwyjl"}],["path",{d:"M18 17V5",key:"sfb6ij"}],["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sx=De("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=De("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lm=De("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nc=De("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ax=De("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lx=De("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ux=De("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cx=De("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const um=De("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cm=De("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dx=De("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=De("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const px=De("Sheet",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"3",x2:"21",y1:"9",y2:"9",key:"1vqk6q"}],["line",{x1:"3",x2:"21",y1:"15",y2:"15",key:"o2sbyz"}],["line",{x1:"9",x2:"9",y1:"9",y2:"21",key:"1ib60c"}],["line",{x1:"15",x2:"15",y1:"9",y2:"21",key:"1n26ft"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=De("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=De("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=De("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=De("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yx=De("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xx=De("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dm=De("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wx=De("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=De("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=De("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Sc="-",kx=n=>{const r=Sx(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:a}=n;return{getClassGroupId:p=>{const h=p.split(Sc);return h[0]===""&&h.length!==1&&h.shift(),fm(h,r)||Nx(p)},getConflictingClassGroupIds:(p,h)=>{const m=s[p]||[];return h&&a[p]?[...m,...a[p]]:m}}},fm=(n,r)=>{var p;if(n.length===0)return r.classGroupId;const s=n[0],a=r.nextPart.get(s),c=a?fm(n.slice(1),a):void 0;if(c)return c;if(r.validators.length===0)return;const f=n.join(Sc);return(p=r.validators.find(({validator:h})=>h(f)))==null?void 0:p.classGroupId},zp=/^\[(.+)\]$/,Nx=n=>{if(zp.test(n)){const r=zp.exec(n)[1],s=r==null?void 0:r.substring(0,r.indexOf(":"));if(s)return"arbitrary.."+s}},Sx=n=>{const{theme:r,prefix:s}=n,a={nextPart:new Map,validators:[]};return Ex(Object.entries(n.classGroups),s).forEach(([f,p])=>{sc(p,a,f,r)}),a},sc=(n,r,s,a)=>{n.forEach(c=>{if(typeof c=="string"){const f=c===""?r:Up(r,c);f.classGroupId=s;return}if(typeof c=="function"){if(Cx(c)){sc(c(a),r,s,a);return}r.validators.push({validator:c,classGroupId:s});return}Object.entries(c).forEach(([f,p])=>{sc(p,Up(r,f),s,a)})})},Up=(n,r)=>{let s=n;return r.split(Sc).forEach(a=>{s.nextPart.has(a)||s.nextPart.set(a,{nextPart:new Map,validators:[]}),s=s.nextPart.get(a)}),s},Cx=n=>n.isThemeGetter,Ex=(n,r)=>r?n.map(([s,a])=>{const c=a.map(f=>typeof f=="string"?r+f:typeof f=="object"?Object.fromEntries(Object.entries(f).map(([p,h])=>[r+p,h])):f);return[s,c]}):n,Px=n=>{if(n<1)return{get:()=>{},set:()=>{}};let r=0,s=new Map,a=new Map;const c=(f,p)=>{s.set(f,p),r++,r>n&&(r=0,a=s,s=new Map)};return{get(f){let p=s.get(f);if(p!==void 0)return p;if((p=a.get(f))!==void 0)return c(f,p),p},set(f,p){s.has(f)?s.set(f,p):c(f,p)}}},pm="!",Tx=n=>{const{separator:r,experimentalParseClassName:s}=n,a=r.length===1,c=r[0],f=r.length,p=h=>{const m=[];let v=0,w=0,b;for(let j=0;j<h.length;j++){let _=h[j];if(v===0){if(_===c&&(a||h.slice(j,j+f)===r)){m.push(h.slice(w,j)),w=j+f;continue}if(_==="/"){b=j;continue}}_==="["?v++:_==="]"&&v--}const N=m.length===0?h:h.substring(w),y=N.startsWith(pm),P=y?N.substring(1):N,k=b&&b>w?b-w:void 0;return{modifiers:m,hasImportantModifier:y,baseClassName:P,maybePostfixModifierPosition:k}};return s?h=>s({className:h,parseClassName:p}):p},Rx=n=>{if(n.length<=1)return n;const r=[];let s=[];return n.forEach(a=>{a[0]==="["?(r.push(...s.sort(),a),s=[]):s.push(a)}),r.push(...s.sort()),r},_x=n=>({cache:Px(n.cacheSize),parseClassName:Tx(n),...kx(n)}),Ax=/\s+/,Ox=(n,r)=>{const{parseClassName:s,getClassGroupId:a,getConflictingClassGroupIds:c}=r,f=[],p=n.trim().split(Ax);let h="";for(let m=p.length-1;m>=0;m-=1){const v=p[m],{modifiers:w,hasImportantModifier:b,baseClassName:N,maybePostfixModifierPosition:y}=s(v);let P=!!y,k=a(P?N.substring(0,y):N);if(!k){if(!P){h=v+(h.length>0?" "+h:h);continue}if(k=a(N),!k){h=v+(h.length>0?" "+h:h);continue}P=!1}const j=Rx(w).join(":"),_=b?j+pm:j,E=_+k;if(f.includes(E))continue;f.push(E);const A=c(k,P);for(let z=0;z<A.length;++z){const O=A[z];f.push(_+O)}h=v+(h.length>0?" "+h:h)}return h};function Mx(){let n=0,r,s,a="";for(;n<arguments.length;)(r=arguments[n++])&&(s=hm(r))&&(a&&(a+=" "),a+=s);return a}const hm=n=>{if(typeof n=="string")return n;let r,s="";for(let a=0;a<n.length;a++)n[a]&&(r=hm(n[a]))&&(s&&(s+=" "),s+=r);return s};function Dx(n,...r){let s,a,c,f=p;function p(m){const v=r.reduce((w,b)=>b(w),n());return s=_x(v),a=s.cache.get,c=s.cache.set,f=h,h(m)}function h(m){const v=a(m);if(v)return v;const w=Ox(m,s);return c(m,w),w}return function(){return f(Mx.apply(null,arguments))}}const Ue=n=>{const r=s=>s[n]||[];return r.isThemeGetter=!0,r},mm=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ix=/^\d+\/\d+$/,Fx=new Set(["px","full","screen"]),Lx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,zx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ux=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,$x=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Bx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,qn=n=>Vo(n)||Fx.has(n)||Ix.test(n),Sr=n=>ss(n,"length",Yx),Vo=n=>!!n&&!Number.isNaN(Number(n)),Ku=n=>ss(n,"number",Vo),Qs=n=>!!n&&Number.isInteger(Number(n)),Vx=n=>n.endsWith("%")&&Vo(n.slice(0,-1)),je=n=>mm.test(n),Cr=n=>Lx.test(n),Hx=new Set(["length","size","percentage"]),Wx=n=>ss(n,Hx,gm),Qx=n=>ss(n,"position",gm),qx=new Set(["image","url"]),Kx=n=>ss(n,qx,Jx),Gx=n=>ss(n,"",Xx),qs=()=>!0,ss=(n,r,s)=>{const a=mm.exec(n);return a?a[1]?typeof r=="string"?a[1]===r:r.has(a[1]):s(a[2]):!1},Yx=n=>zx.test(n)&&!Ux.test(n),gm=()=>!1,Xx=n=>$x.test(n),Jx=n=>Bx.test(n),Zx=()=>{const n=Ue("colors"),r=Ue("spacing"),s=Ue("blur"),a=Ue("brightness"),c=Ue("borderColor"),f=Ue("borderRadius"),p=Ue("borderSpacing"),h=Ue("borderWidth"),m=Ue("contrast"),v=Ue("grayscale"),w=Ue("hueRotate"),b=Ue("invert"),N=Ue("gap"),y=Ue("gradientColorStops"),P=Ue("gradientColorStopPositions"),k=Ue("inset"),j=Ue("margin"),_=Ue("opacity"),E=Ue("padding"),A=Ue("saturate"),z=Ue("scale"),O=Ue("sepia"),F=Ue("skew"),$=Ue("space"),G=Ue("translate"),J=()=>["auto","contain","none"],Z=()=>["auto","hidden","clip","visible","scroll"],pe=()=>["auto",je,r],te=()=>[je,r],ge=()=>["",qn,Sr],X=()=>["auto",Vo,je],ve=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],se=()=>["solid","dashed","dotted","double","none"],le=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],U=()=>["start","end","center","between","around","evenly","stretch"],V=()=>["","0",je],q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>[Vo,je];return{cacheSize:500,separator:":",theme:{colors:[qs],spacing:[qn,Sr],blur:["none","",Cr,je],brightness:R(),borderColor:[n],borderRadius:["none","","full",Cr,je],borderSpacing:te(),borderWidth:ge(),contrast:R(),grayscale:V(),hueRotate:R(),invert:V(),gap:te(),gradientColorStops:[n],gradientColorStopPositions:[Vx,Sr],inset:pe(),margin:pe(),opacity:R(),padding:te(),saturate:R(),scale:R(),sepia:V(),skew:R(),space:te(),translate:te()},classGroups:{aspect:[{aspect:["auto","square","video",je]}],container:["container"],columns:[{columns:[Cr]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ve(),je]}],overflow:[{overflow:Z()}],"overflow-x":[{"overflow-x":Z()}],"overflow-y":[{"overflow-y":Z()}],overscroll:[{overscroll:J()}],"overscroll-x":[{"overscroll-x":J()}],"overscroll-y":[{"overscroll-y":J()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[k]}],"inset-x":[{"inset-x":[k]}],"inset-y":[{"inset-y":[k]}],start:[{start:[k]}],end:[{end:[k]}],top:[{top:[k]}],right:[{right:[k]}],bottom:[{bottom:[k]}],left:[{left:[k]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Qs,je]}],basis:[{basis:pe()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",je]}],grow:[{grow:V()}],shrink:[{shrink:V()}],order:[{order:["first","last","none",Qs,je]}],"grid-cols":[{"grid-cols":[qs]}],"col-start-end":[{col:["auto",{span:["full",Qs,je]},je]}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":[qs]}],"row-start-end":[{row:["auto",{span:[Qs,je]},je]}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",je]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",je]}],gap:[{gap:[N]}],"gap-x":[{"gap-x":[N]}],"gap-y":[{"gap-y":[N]}],"justify-content":[{justify:["normal",...U()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...U(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...U(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[E]}],px:[{px:[E]}],py:[{py:[E]}],ps:[{ps:[E]}],pe:[{pe:[E]}],pt:[{pt:[E]}],pr:[{pr:[E]}],pb:[{pb:[E]}],pl:[{pl:[E]}],m:[{m:[j]}],mx:[{mx:[j]}],my:[{my:[j]}],ms:[{ms:[j]}],me:[{me:[j]}],mt:[{mt:[j]}],mr:[{mr:[j]}],mb:[{mb:[j]}],ml:[{ml:[j]}],"space-x":[{"space-x":[$]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[$]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",je,r]}],"min-w":[{"min-w":[je,r,"min","max","fit"]}],"max-w":[{"max-w":[je,r,"none","full","min","max","fit","prose",{screen:[Cr]},Cr]}],h:[{h:[je,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[je,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[je,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[je,r,"auto","min","max","fit"]}],"font-size":[{text:["base",Cr,Sr]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ku]}],"font-family":[{font:[qs]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",je]}],"line-clamp":[{"line-clamp":["none",Vo,Ku]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",qn,je]}],"list-image":[{"list-image":["none",je]}],"list-style-type":[{list:["none","disc","decimal",je]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[_]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[_]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...se(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",qn,Sr]}],"underline-offset":[{"underline-offset":["auto",qn,je]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:te()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",je]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",je]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[_]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ve(),Qx]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Wx]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Kx]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[P]}],"gradient-via-pos":[{via:[P]}],"gradient-to-pos":[{to:[P]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[f]}],"rounded-s":[{"rounded-s":[f]}],"rounded-e":[{"rounded-e":[f]}],"rounded-t":[{"rounded-t":[f]}],"rounded-r":[{"rounded-r":[f]}],"rounded-b":[{"rounded-b":[f]}],"rounded-l":[{"rounded-l":[f]}],"rounded-ss":[{"rounded-ss":[f]}],"rounded-se":[{"rounded-se":[f]}],"rounded-ee":[{"rounded-ee":[f]}],"rounded-es":[{"rounded-es":[f]}],"rounded-tl":[{"rounded-tl":[f]}],"rounded-tr":[{"rounded-tr":[f]}],"rounded-br":[{"rounded-br":[f]}],"rounded-bl":[{"rounded-bl":[f]}],"border-w":[{border:[h]}],"border-w-x":[{"border-x":[h]}],"border-w-y":[{"border-y":[h]}],"border-w-s":[{"border-s":[h]}],"border-w-e":[{"border-e":[h]}],"border-w-t":[{"border-t":[h]}],"border-w-r":[{"border-r":[h]}],"border-w-b":[{"border-b":[h]}],"border-w-l":[{"border-l":[h]}],"border-opacity":[{"border-opacity":[_]}],"border-style":[{border:[...se(),"hidden"]}],"divide-x":[{"divide-x":[h]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[h]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[_]}],"divide-style":[{divide:se()}],"border-color":[{border:[c]}],"border-color-x":[{"border-x":[c]}],"border-color-y":[{"border-y":[c]}],"border-color-s":[{"border-s":[c]}],"border-color-e":[{"border-e":[c]}],"border-color-t":[{"border-t":[c]}],"border-color-r":[{"border-r":[c]}],"border-color-b":[{"border-b":[c]}],"border-color-l":[{"border-l":[c]}],"divide-color":[{divide:[c]}],"outline-style":[{outline:["",...se()]}],"outline-offset":[{"outline-offset":[qn,je]}],"outline-w":[{outline:[qn,Sr]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:ge()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[_]}],"ring-offset-w":[{"ring-offset":[qn,Sr]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",Cr,Gx]}],"shadow-color":[{shadow:[qs]}],opacity:[{opacity:[_]}],"mix-blend":[{"mix-blend":[...le(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":le()}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[a]}],contrast:[{contrast:[m]}],"drop-shadow":[{"drop-shadow":["","none",Cr,je]}],grayscale:[{grayscale:[v]}],"hue-rotate":[{"hue-rotate":[w]}],invert:[{invert:[b]}],saturate:[{saturate:[A]}],sepia:[{sepia:[O]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[a]}],"backdrop-contrast":[{"backdrop-contrast":[m]}],"backdrop-grayscale":[{"backdrop-grayscale":[v]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w]}],"backdrop-invert":[{"backdrop-invert":[b]}],"backdrop-opacity":[{"backdrop-opacity":[_]}],"backdrop-saturate":[{"backdrop-saturate":[A]}],"backdrop-sepia":[{"backdrop-sepia":[O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[p]}],"border-spacing-x":[{"border-spacing-x":[p]}],"border-spacing-y":[{"border-spacing-y":[p]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",je]}],duration:[{duration:R()}],ease:[{ease:["linear","in","out","in-out",je]}],delay:[{delay:R()}],animate:[{animate:["none","spin","ping","pulse","bounce",je]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[z]}],"scale-x":[{"scale-x":[z]}],"scale-y":[{"scale-y":[z]}],rotate:[{rotate:[Qs,je]}],"translate-x":[{"translate-x":[G]}],"translate-y":[{"translate-y":[G]}],"skew-x":[{"skew-x":[F]}],"skew-y":[{"skew-y":[F]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",je]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",je]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":te()}],"scroll-mx":[{"scroll-mx":te()}],"scroll-my":[{"scroll-my":te()}],"scroll-ms":[{"scroll-ms":te()}],"scroll-me":[{"scroll-me":te()}],"scroll-mt":[{"scroll-mt":te()}],"scroll-mr":[{"scroll-mr":te()}],"scroll-mb":[{"scroll-mb":te()}],"scroll-ml":[{"scroll-ml":te()}],"scroll-p":[{"scroll-p":te()}],"scroll-px":[{"scroll-px":te()}],"scroll-py":[{"scroll-py":te()}],"scroll-ps":[{"scroll-ps":te()}],"scroll-pe":[{"scroll-pe":te()}],"scroll-pt":[{"scroll-pt":te()}],"scroll-pr":[{"scroll-pr":te()}],"scroll-pb":[{"scroll-pb":te()}],"scroll-pl":[{"scroll-pl":te()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",je]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[qn,Sr,Ku]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},ew=Dx(Zx);function Se(...n){return ew(im(n))}const tw=Gy,vm=x.forwardRef(({className:n,...r},s)=>l.jsx(Zh,{ref:s,className:Se("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n),...r}));vm.displayName=Zh.displayName;const nw=kc("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),ym=x.forwardRef(({className:n,variant:r,...s},a)=>l.jsx(em,{ref:a,className:Se(nw({variant:r}),n),...s}));ym.displayName=em.displayName;const rw=x.forwardRef(({className:n,...r},s)=>l.jsx(rm,{ref:s,className:Se("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),...r}));rw.displayName=rm.displayName;const xm=x.forwardRef(({className:n,...r},s)=>l.jsx(om,{ref:s,className:Se("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...r,children:l.jsx(jx,{className:"h-4 w-4"})}));xm.displayName=om.displayName;const wm=x.forwardRef(({className:n,...r},s)=>l.jsx(tm,{ref:s,className:Se("text-sm font-semibold",n),...r}));wm.displayName=tm.displayName;const bm=x.forwardRef(({className:n,...r},s)=>l.jsx(nm,{ref:s,className:Se("text-sm opacity-90",n),...r}));bm.displayName=nm.displayName;function ow(){const{toasts:n}=oy();return l.jsxs(tw,{children:[n.map(function({id:r,title:s,description:a,action:c,...f}){return l.jsxs(ym,{...f,children:[l.jsxs("div",{className:"grid gap-1",children:[s&&l.jsx(wm,{children:s}),a&&l.jsx(bm,{children:a})]}),c,l.jsx(xm,{})]},r)}),l.jsx(vm,{})]})}var $p=["light","dark"],sw="(prefers-color-scheme: dark)",iw=x.createContext(void 0),aw={setTheme:n=>{},themes:[]},lw=()=>{var n;return(n=x.useContext(iw))!=null?n:aw};x.memo(({forcedTheme:n,storageKey:r,attribute:s,enableSystem:a,enableColorScheme:c,defaultTheme:f,value:p,attrs:h,nonce:m})=>{let v=f==="system",w=s==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${h.map(P=>`'${P}'`).join(",")})`};`:`var d=document.documentElement,n='${s}',s='setAttribute';`,b=c?$p.includes(f)&&f?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${f}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",N=(P,k=!1,j=!0)=>{let _=p?p[P]:P,E=k?P+"|| ''":`'${_}'`,A="";return c&&j&&!k&&$p.includes(P)&&(A+=`d.style.colorScheme = '${P}';`),s==="class"?k||_?A+=`c.add(${E})`:A+="null":_&&(A+=`d[s](n,${E})`),A},y=n?`!function(){${w}${N(n)}}()`:a?`!function(){try{${w}var e=localStorage.getItem('${r}');if('system'===e||(!e&&${v})){var t='${sw}',m=window.matchMedia(t);if(m.media!==t||m.matches){${N("dark")}}else{${N("light")}}}else if(e){${p?`var x=${JSON.stringify(p)};`:""}${N(p?"x[e]":"e",!0)}}${v?"":"else{"+N(f,!1,!1)+"}"}${b}}catch(e){}}()`:`!function(){try{${w}var e=localStorage.getItem('${r}');if(e){${p?`var x=${JSON.stringify(p)};`:""}${N(p?"x[e]":"e",!0)}}else{${N(f,!1,!1)};}${b}}catch(t){}}();`;return x.createElement("script",{nonce:m,dangerouslySetInnerHTML:{__html:y}})});var uw=n=>{switch(n){case"success":return fw;case"info":return hw;case"warning":return pw;case"error":return mw;default:return null}},cw=Array(12).fill(0),dw=({visible:n,className:r})=>Y.createElement("div",{className:["sonner-loading-wrapper",r].filter(Boolean).join(" "),"data-visible":n},Y.createElement("div",{className:"sonner-spinner"},cw.map((s,a)=>Y.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${a}`})))),fw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),pw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),hw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),mw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),gw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},Y.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),Y.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),vw=()=>{let[n,r]=Y.useState(document.hidden);return Y.useEffect(()=>{let s=()=>{r(document.hidden)};return document.addEventListener("visibilitychange",s),()=>window.removeEventListener("visibilitychange",s)},[]),n},ic=1,yw=class{constructor(){this.subscribe=n=>(this.subscribers.push(n),()=>{let r=this.subscribers.indexOf(n);this.subscribers.splice(r,1)}),this.publish=n=>{this.subscribers.forEach(r=>r(n))},this.addToast=n=>{this.publish(n),this.toasts=[...this.toasts,n]},this.create=n=>{var r;let{message:s,...a}=n,c=typeof(n==null?void 0:n.id)=="number"||((r=n.id)==null?void 0:r.length)>0?n.id:ic++,f=this.toasts.find(h=>h.id===c),p=n.dismissible===void 0?!0:n.dismissible;return this.dismissedToasts.has(c)&&this.dismissedToasts.delete(c),f?this.toasts=this.toasts.map(h=>h.id===c?(this.publish({...h,...n,id:c,title:s}),{...h,...n,id:c,dismissible:p,title:s}):h):this.addToast({title:s,...a,dismissible:p,id:c}),c},this.dismiss=n=>(this.dismissedToasts.add(n),n||this.toasts.forEach(r=>{this.subscribers.forEach(s=>s({id:r.id,dismiss:!0}))}),this.subscribers.forEach(r=>r({id:n,dismiss:!0})),n),this.message=(n,r)=>this.create({...r,message:n}),this.error=(n,r)=>this.create({...r,message:n,type:"error"}),this.success=(n,r)=>this.create({...r,type:"success",message:n}),this.info=(n,r)=>this.create({...r,type:"info",message:n}),this.warning=(n,r)=>this.create({...r,type:"warning",message:n}),this.loading=(n,r)=>this.create({...r,type:"loading",message:n}),this.promise=(n,r)=>{if(!r)return;let s;r.loading!==void 0&&(s=this.create({...r,promise:n,type:"loading",message:r.loading,description:typeof r.description!="function"?r.description:void 0}));let a=n instanceof Promise?n:n(),c=s!==void 0,f,p=a.then(async m=>{if(f=["resolve",m],Y.isValidElement(m))c=!1,this.create({id:s,type:"default",message:m});else if(ww(m)&&!m.ok){c=!1;let v=typeof r.error=="function"?await r.error(`HTTP error! status: ${m.status}`):r.error,w=typeof r.description=="function"?await r.description(`HTTP error! status: ${m.status}`):r.description;this.create({id:s,type:"error",message:v,description:w})}else if(r.success!==void 0){c=!1;let v=typeof r.success=="function"?await r.success(m):r.success,w=typeof r.description=="function"?await r.description(m):r.description;this.create({id:s,type:"success",message:v,description:w})}}).catch(async m=>{if(f=["reject",m],r.error!==void 0){c=!1;let v=typeof r.error=="function"?await r.error(m):r.error,w=typeof r.description=="function"?await r.description(m):r.description;this.create({id:s,type:"error",message:v,description:w})}}).finally(()=>{var m;c&&(this.dismiss(s),s=void 0),(m=r.finally)==null||m.call(r)}),h=()=>new Promise((m,v)=>p.then(()=>f[0]==="reject"?v(f[1]):m(f[1])).catch(v));return typeof s!="string"&&typeof s!="number"?{unwrap:h}:Object.assign(s,{unwrap:h})},this.custom=(n,r)=>{let s=(r==null?void 0:r.id)||ic++;return this.create({jsx:n(s),id:s,...r}),s},this.getActiveToasts=()=>this.toasts.filter(n=>!this.dismissedToasts.has(n.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Rt=new yw,xw=(n,r)=>{let s=(r==null?void 0:r.id)||ic++;return Rt.addToast({title:n,...r,id:s}),s},ww=n=>n&&typeof n=="object"&&"ok"in n&&typeof n.ok=="boolean"&&"status"in n&&typeof n.status=="number",bw=xw,jw=()=>Rt.toasts,kw=()=>Rt.getActiveToasts();Object.assign(bw,{success:Rt.success,info:Rt.info,warning:Rt.warning,error:Rt.error,custom:Rt.custom,message:Rt.message,promise:Rt.promise,dismiss:Rt.dismiss,loading:Rt.loading},{getHistory:jw,getToasts:kw});function Nw(n,{insertAt:r}={}){if(typeof document>"u")return;let s=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",r==="top"&&s.firstChild?s.insertBefore(a,s.firstChild):s.appendChild(a),a.styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n))}Nw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Pa(n){return n.label!==void 0}var Sw=3,Cw="32px",Ew="16px",Bp=4e3,Pw=356,Tw=14,Rw=20,_w=200;function ln(...n){return n.filter(Boolean).join(" ")}function Aw(n){let[r,s]=n.split("-"),a=[];return r&&a.push(r),s&&a.push(s),a}var Ow=n=>{var r,s,a,c,f,p,h,m,v,w,b;let{invert:N,toast:y,unstyled:P,interacting:k,setHeights:j,visibleToasts:_,heights:E,index:A,toasts:z,expanded:O,removeToast:F,defaultRichColors:$,closeButton:G,style:J,cancelButtonStyle:Z,actionButtonStyle:pe,className:te="",descriptionClassName:ge="",duration:X,position:ve,gap:se,loadingIcon:le,expandByDefault:U,classNames:V,icons:q,closeButtonAriaLabel:R="Close toast",pauseWhenPageIsHidden:L}=n,[ee,re]=Y.useState(null),[he,be]=Y.useState(null),[ce,Ce]=Y.useState(!1),[Pe,Ge]=Y.useState(!1),[_t,Fn]=Y.useState(!1),[At,nr]=Y.useState(!1),[ho,Ur]=Y.useState(!1),[$r,rr]=Y.useState(0),[vn,or]=Y.useState(0),Ut=Y.useRef(y.duration||X||Bp),mo=Y.useRef(null),yn=Y.useRef(null),di=A===0,fi=A+1<=_,gt=y.type,xn=y.dismissible!==!1,go=y.className||"",pi=y.descriptionClassName||"",wn=Y.useMemo(()=>E.findIndex(xe=>xe.toastId===y.id)||0,[E,y.id]),Br=Y.useMemo(()=>{var xe;return(xe=y.closeButton)!=null?xe:G},[y.closeButton,G]),hi=Y.useMemo(()=>y.duration||X||Bp,[y.duration,X]),vo=Y.useRef(0),Ln=Y.useRef(0),mi=Y.useRef(0),bn=Y.useRef(null),[us,cs]=ve.split("-"),yo=Y.useMemo(()=>E.reduce((xe,Ae,Ie)=>Ie>=wn?xe:xe+Ae.height,0),[E,wn]),xo=vw(),sr=y.invert||N,jn=gt==="loading";Ln.current=Y.useMemo(()=>wn*se+yo,[wn,yo]),Y.useEffect(()=>{Ut.current=hi},[hi]),Y.useEffect(()=>{Ce(!0)},[]),Y.useEffect(()=>{let xe=yn.current;if(xe){let Ae=xe.getBoundingClientRect().height;return or(Ae),j(Ie=>[{toastId:y.id,height:Ae,position:y.position},...Ie]),()=>j(Ie=>Ie.filter(vt=>vt.toastId!==y.id))}},[j,y.id]),Y.useLayoutEffect(()=>{if(!ce)return;let xe=yn.current,Ae=xe.style.height;xe.style.height="auto";let Ie=xe.getBoundingClientRect().height;xe.style.height=Ae,or(Ie),j(vt=>vt.find(jt=>jt.toastId===y.id)?vt.map(jt=>jt.toastId===y.id?{...jt,height:Ie}:jt):[{toastId:y.id,height:Ie,position:y.position},...vt])},[ce,y.title,y.description,j,y.id]);let Jt=Y.useCallback(()=>{Ge(!0),rr(Ln.current),j(xe=>xe.filter(Ae=>Ae.toastId!==y.id)),setTimeout(()=>{F(y)},_w)},[y,F,j,Ln]);Y.useEffect(()=>{if(y.promise&&gt==="loading"||y.duration===1/0||y.type==="loading")return;let xe;return O||k||L&&xo?(()=>{if(mi.current<vo.current){let Ae=new Date().getTime()-vo.current;Ut.current=Ut.current-Ae}mi.current=new Date().getTime()})():Ut.current!==1/0&&(vo.current=new Date().getTime(),xe=setTimeout(()=>{var Ae;(Ae=y.onAutoClose)==null||Ae.call(y,y),Jt()},Ut.current)),()=>clearTimeout(xe)},[O,k,y,gt,L,xo,Jt]),Y.useEffect(()=>{y.delete&&Jt()},[Jt,y.delete]);function gi(){var xe,Ae,Ie;return q!=null&&q.loading?Y.createElement("div",{className:ln(V==null?void 0:V.loader,(xe=y==null?void 0:y.classNames)==null?void 0:xe.loader,"sonner-loader"),"data-visible":gt==="loading"},q.loading):le?Y.createElement("div",{className:ln(V==null?void 0:V.loader,(Ae=y==null?void 0:y.classNames)==null?void 0:Ae.loader,"sonner-loader"),"data-visible":gt==="loading"},le):Y.createElement(dw,{className:ln(V==null?void 0:V.loader,(Ie=y==null?void 0:y.classNames)==null?void 0:Ie.loader),visible:gt==="loading"})}return Y.createElement("li",{tabIndex:0,ref:yn,className:ln(te,go,V==null?void 0:V.toast,(r=y==null?void 0:y.classNames)==null?void 0:r.toast,V==null?void 0:V.default,V==null?void 0:V[gt],(s=y==null?void 0:y.classNames)==null?void 0:s[gt]),"data-sonner-toast":"","data-rich-colors":(a=y.richColors)!=null?a:$,"data-styled":!(y.jsx||y.unstyled||P),"data-mounted":ce,"data-promise":!!y.promise,"data-swiped":ho,"data-removed":Pe,"data-visible":fi,"data-y-position":us,"data-x-position":cs,"data-index":A,"data-front":di,"data-swiping":_t,"data-dismissible":xn,"data-type":gt,"data-invert":sr,"data-swipe-out":At,"data-swipe-direction":he,"data-expanded":!!(O||U&&ce),style:{"--index":A,"--toasts-before":A,"--z-index":z.length-A,"--offset":`${Pe?$r:Ln.current}px`,"--initial-height":U?"auto":`${vn}px`,...J,...y.style},onDragEnd:()=>{Fn(!1),re(null),bn.current=null},onPointerDown:xe=>{jn||!xn||(mo.current=new Date,rr(Ln.current),xe.target.setPointerCapture(xe.pointerId),xe.target.tagName!=="BUTTON"&&(Fn(!0),bn.current={x:xe.clientX,y:xe.clientY}))},onPointerUp:()=>{var xe,Ae,Ie,vt;if(At||!xn)return;bn.current=null;let jt=Number(((xe=yn.current)==null?void 0:xe.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),kt=Number(((Ae=yn.current)==null?void 0:Ae.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Zt=new Date().getTime()-((Ie=mo.current)==null?void 0:Ie.getTime()),it=ee==="x"?jt:kt,kn=Math.abs(it)/Zt;if(Math.abs(it)>=Rw||kn>.11){rr(Ln.current),(vt=y.onDismiss)==null||vt.call(y,y),be(ee==="x"?jt>0?"right":"left":kt>0?"down":"up"),Jt(),nr(!0),Ur(!1);return}Fn(!1),re(null)},onPointerMove:xe=>{var Ae,Ie,vt,jt;if(!bn.current||!xn||((Ae=window.getSelection())==null?void 0:Ae.toString().length)>0)return;let kt=xe.clientY-bn.current.y,Zt=xe.clientX-bn.current.x,it=(Ie=n.swipeDirections)!=null?Ie:Aw(ve);!ee&&(Math.abs(Zt)>1||Math.abs(kt)>1)&&re(Math.abs(Zt)>Math.abs(kt)?"x":"y");let kn={x:0,y:0};ee==="y"?(it.includes("top")||it.includes("bottom"))&&(it.includes("top")&&kt<0||it.includes("bottom")&&kt>0)&&(kn.y=kt):ee==="x"&&(it.includes("left")||it.includes("right"))&&(it.includes("left")&&Zt<0||it.includes("right")&&Zt>0)&&(kn.x=Zt),(Math.abs(kn.x)>0||Math.abs(kn.y)>0)&&Ur(!0),(vt=yn.current)==null||vt.style.setProperty("--swipe-amount-x",`${kn.x}px`),(jt=yn.current)==null||jt.style.setProperty("--swipe-amount-y",`${kn.y}px`)}},Br&&!y.jsx?Y.createElement("button",{"aria-label":R,"data-disabled":jn,"data-close-button":!0,onClick:jn||!xn?()=>{}:()=>{var xe;Jt(),(xe=y.onDismiss)==null||xe.call(y,y)},className:ln(V==null?void 0:V.closeButton,(c=y==null?void 0:y.classNames)==null?void 0:c.closeButton)},(f=q==null?void 0:q.close)!=null?f:gw):null,y.jsx||x.isValidElement(y.title)?y.jsx?y.jsx:typeof y.title=="function"?y.title():y.title:Y.createElement(Y.Fragment,null,gt||y.icon||y.promise?Y.createElement("div",{"data-icon":"",className:ln(V==null?void 0:V.icon,(p=y==null?void 0:y.classNames)==null?void 0:p.icon)},y.promise||y.type==="loading"&&!y.icon?y.icon||gi():null,y.type!=="loading"?y.icon||(q==null?void 0:q[gt])||uw(gt):null):null,Y.createElement("div",{"data-content":"",className:ln(V==null?void 0:V.content,(h=y==null?void 0:y.classNames)==null?void 0:h.content)},Y.createElement("div",{"data-title":"",className:ln(V==null?void 0:V.title,(m=y==null?void 0:y.classNames)==null?void 0:m.title)},typeof y.title=="function"?y.title():y.title),y.description?Y.createElement("div",{"data-description":"",className:ln(ge,pi,V==null?void 0:V.description,(v=y==null?void 0:y.classNames)==null?void 0:v.description)},typeof y.description=="function"?y.description():y.description):null),x.isValidElement(y.cancel)?y.cancel:y.cancel&&Pa(y.cancel)?Y.createElement("button",{"data-button":!0,"data-cancel":!0,style:y.cancelButtonStyle||Z,onClick:xe=>{var Ae,Ie;Pa(y.cancel)&&xn&&((Ie=(Ae=y.cancel).onClick)==null||Ie.call(Ae,xe),Jt())},className:ln(V==null?void 0:V.cancelButton,(w=y==null?void 0:y.classNames)==null?void 0:w.cancelButton)},y.cancel.label):null,x.isValidElement(y.action)?y.action:y.action&&Pa(y.action)?Y.createElement("button",{"data-button":!0,"data-action":!0,style:y.actionButtonStyle||pe,onClick:xe=>{var Ae,Ie;Pa(y.action)&&((Ie=(Ae=y.action).onClick)==null||Ie.call(Ae,xe),!xe.defaultPrevented&&Jt())},className:ln(V==null?void 0:V.actionButton,(b=y==null?void 0:y.classNames)==null?void 0:b.actionButton)},y.action.label):null))};function Vp(){if(typeof window>"u"||typeof document>"u")return"ltr";let n=document.documentElement.getAttribute("dir");return n==="auto"||!n?window.getComputedStyle(document.documentElement).direction:n}function Mw(n,r){let s={};return[n,r].forEach((a,c)=>{let f=c===1,p=f?"--mobile-offset":"--offset",h=f?Ew:Cw;function m(v){["top","right","bottom","left"].forEach(w=>{s[`${p}-${w}`]=typeof v=="number"?`${v}px`:v})}typeof a=="number"||typeof a=="string"?m(a):typeof a=="object"?["top","right","bottom","left"].forEach(v=>{a[v]===void 0?s[`${p}-${v}`]=h:s[`${p}-${v}`]=typeof a[v]=="number"?`${a[v]}px`:a[v]}):m(h)}),s}var Dw=x.forwardRef(function(n,r){let{invert:s,position:a="bottom-right",hotkey:c=["altKey","KeyT"],expand:f,closeButton:p,className:h,offset:m,mobileOffset:v,theme:w="light",richColors:b,duration:N,style:y,visibleToasts:P=Sw,toastOptions:k,dir:j=Vp(),gap:_=Tw,loadingIcon:E,icons:A,containerAriaLabel:z="Notifications",pauseWhenPageIsHidden:O}=n,[F,$]=Y.useState([]),G=Y.useMemo(()=>Array.from(new Set([a].concat(F.filter(L=>L.position).map(L=>L.position)))),[F,a]),[J,Z]=Y.useState([]),[pe,te]=Y.useState(!1),[ge,X]=Y.useState(!1),[ve,se]=Y.useState(w!=="system"?w:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),le=Y.useRef(null),U=c.join("+").replace(/Key/g,"").replace(/Digit/g,""),V=Y.useRef(null),q=Y.useRef(!1),R=Y.useCallback(L=>{$(ee=>{var re;return(re=ee.find(he=>he.id===L.id))!=null&&re.delete||Rt.dismiss(L.id),ee.filter(({id:he})=>he!==L.id)})},[]);return Y.useEffect(()=>Rt.subscribe(L=>{if(L.dismiss){$(ee=>ee.map(re=>re.id===L.id?{...re,delete:!0}:re));return}setTimeout(()=>{_h.flushSync(()=>{$(ee=>{let re=ee.findIndex(he=>he.id===L.id);return re!==-1?[...ee.slice(0,re),{...ee[re],...L},...ee.slice(re+1)]:[L,...ee]})})})}),[]),Y.useEffect(()=>{if(w!=="system"){se(w);return}if(w==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?se("dark"):se("light")),typeof window>"u")return;let L=window.matchMedia("(prefers-color-scheme: dark)");try{L.addEventListener("change",({matches:ee})=>{se(ee?"dark":"light")})}catch{L.addListener(({matches:re})=>{try{se(re?"dark":"light")}catch(he){console.error(he)}})}},[w]),Y.useEffect(()=>{F.length<=1&&te(!1)},[F]),Y.useEffect(()=>{let L=ee=>{var re,he;c.every(be=>ee[be]||ee.code===be)&&(te(!0),(re=le.current)==null||re.focus()),ee.code==="Escape"&&(document.activeElement===le.current||(he=le.current)!=null&&he.contains(document.activeElement))&&te(!1)};return document.addEventListener("keydown",L),()=>document.removeEventListener("keydown",L)},[c]),Y.useEffect(()=>{if(le.current)return()=>{V.current&&(V.current.focus({preventScroll:!0}),V.current=null,q.current=!1)}},[le.current]),Y.createElement("section",{ref:r,"aria-label":`${z} ${U}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},G.map((L,ee)=>{var re;let[he,be]=L.split("-");return F.length?Y.createElement("ol",{key:L,dir:j==="auto"?Vp():j,tabIndex:-1,ref:le,className:h,"data-sonner-toaster":!0,"data-theme":ve,"data-y-position":he,"data-lifted":pe&&F.length>1&&!f,"data-x-position":be,style:{"--front-toast-height":`${((re=J[0])==null?void 0:re.height)||0}px`,"--width":`${Pw}px`,"--gap":`${_}px`,...y,...Mw(m,v)},onBlur:ce=>{q.current&&!ce.currentTarget.contains(ce.relatedTarget)&&(q.current=!1,V.current&&(V.current.focus({preventScroll:!0}),V.current=null))},onFocus:ce=>{ce.target instanceof HTMLElement&&ce.target.dataset.dismissible==="false"||q.current||(q.current=!0,V.current=ce.relatedTarget)},onMouseEnter:()=>te(!0),onMouseMove:()=>te(!0),onMouseLeave:()=>{ge||te(!1)},onDragEnd:()=>te(!1),onPointerDown:ce=>{ce.target instanceof HTMLElement&&ce.target.dataset.dismissible==="false"||X(!0)},onPointerUp:()=>X(!1)},F.filter(ce=>!ce.position&&ee===0||ce.position===L).map((ce,Ce)=>{var Pe,Ge;return Y.createElement(Ow,{key:ce.id,icons:A,index:Ce,toast:ce,defaultRichColors:b,duration:(Pe=k==null?void 0:k.duration)!=null?Pe:N,className:k==null?void 0:k.className,descriptionClassName:k==null?void 0:k.descriptionClassName,invert:s,visibleToasts:P,closeButton:(Ge=k==null?void 0:k.closeButton)!=null?Ge:p,interacting:ge,position:L,style:k==null?void 0:k.style,unstyled:k==null?void 0:k.unstyled,classNames:k==null?void 0:k.classNames,cancelButtonStyle:k==null?void 0:k.cancelButtonStyle,actionButtonStyle:k==null?void 0:k.actionButtonStyle,removeToast:R,toasts:F.filter(_t=>_t.position==ce.position),heights:J.filter(_t=>_t.position==ce.position),setHeights:Z,expandByDefault:f,gap:_,loadingIcon:E,expanded:pe,pauseWhenPageIsHidden:O,swipeDirections:n.swipeDirections})})):null}))});const Iw=({...n})=>{const{theme:r="system"}=lw();return l.jsx(Dw,{theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...n})};var Fw=yc[" useId ".trim().toString()]||(()=>{}),Lw=0;function zw(n){const[r,s]=x.useState(Fw());return Zn(()=>{s(a=>a??String(Lw++))},[n]),r?`radix-${r}`:""}const Uw=["top","right","bottom","left"],Fr=Math.min,Lt=Math.max,Ia=Math.round,Ta=Math.floor,Mn=n=>({x:n,y:n}),$w={left:"right",right:"left",bottom:"top",top:"bottom"},Bw={start:"end",end:"start"};function ac(n,r,s){return Lt(n,Fr(r,s))}function er(n,r){return typeof n=="function"?n(r):n}function tr(n){return n.split("-")[0]}function is(n){return n.split("-")[1]}function Cc(n){return n==="x"?"y":"x"}function Ec(n){return n==="y"?"height":"width"}const Vw=new Set(["top","bottom"]);function On(n){return Vw.has(tr(n))?"y":"x"}function Pc(n){return Cc(On(n))}function Hw(n,r,s){s===void 0&&(s=!1);const a=is(n),c=Pc(n),f=Ec(c);let p=c==="x"?a===(s?"end":"start")?"right":"left":a==="start"?"bottom":"top";return r.reference[f]>r.floating[f]&&(p=Fa(p)),[p,Fa(p)]}function Ww(n){const r=Fa(n);return[lc(n),r,lc(r)]}function lc(n){return n.replace(/start|end/g,r=>Bw[r])}const Hp=["left","right"],Wp=["right","left"],Qw=["top","bottom"],qw=["bottom","top"];function Kw(n,r,s){switch(n){case"top":case"bottom":return s?r?Wp:Hp:r?Hp:Wp;case"left":case"right":return r?Qw:qw;default:return[]}}function Gw(n,r,s,a){const c=is(n);let f=Kw(tr(n),s==="start",a);return c&&(f=f.map(p=>p+"-"+c),r&&(f=f.concat(f.map(lc)))),f}function Fa(n){return n.replace(/left|right|bottom|top/g,r=>$w[r])}function Yw(n){return{top:0,right:0,bottom:0,left:0,...n}}function jm(n){return typeof n!="number"?Yw(n):{top:n,right:n,bottom:n,left:n}}function La(n){const{x:r,y:s,width:a,height:c}=n;return{width:a,height:c,top:s,left:r,right:r+a,bottom:s+c,x:r,y:s}}function Qp(n,r,s){let{reference:a,floating:c}=n;const f=On(r),p=Pc(r),h=Ec(p),m=tr(r),v=f==="y",w=a.x+a.width/2-c.width/2,b=a.y+a.height/2-c.height/2,N=a[h]/2-c[h]/2;let y;switch(m){case"top":y={x:w,y:a.y-c.height};break;case"bottom":y={x:w,y:a.y+a.height};break;case"right":y={x:a.x+a.width,y:b};break;case"left":y={x:a.x-c.width,y:b};break;default:y={x:a.x,y:a.y}}switch(is(r)){case"start":y[p]-=N*(s&&v?-1:1);break;case"end":y[p]+=N*(s&&v?-1:1);break}return y}const Xw=async(n,r,s)=>{const{placement:a="bottom",strategy:c="absolute",middleware:f=[],platform:p}=s,h=f.filter(Boolean),m=await(p.isRTL==null?void 0:p.isRTL(r));let v=await p.getElementRects({reference:n,floating:r,strategy:c}),{x:w,y:b}=Qp(v,a,m),N=a,y={},P=0;for(let k=0;k<h.length;k++){const{name:j,fn:_}=h[k],{x:E,y:A,data:z,reset:O}=await _({x:w,y:b,initialPlacement:a,placement:N,strategy:c,middlewareData:y,rects:v,platform:p,elements:{reference:n,floating:r}});w=E??w,b=A??b,y={...y,[j]:{...y[j],...z}},O&&P<=50&&(P++,typeof O=="object"&&(O.placement&&(N=O.placement),O.rects&&(v=O.rects===!0?await p.getElementRects({reference:n,floating:r,strategy:c}):O.rects),{x:w,y:b}=Qp(v,N,m)),k=-1)}return{x:w,y:b,placement:N,strategy:c,middlewareData:y}};async function Js(n,r){var s;r===void 0&&(r={});const{x:a,y:c,platform:f,rects:p,elements:h,strategy:m}=n,{boundary:v="clippingAncestors",rootBoundary:w="viewport",elementContext:b="floating",altBoundary:N=!1,padding:y=0}=er(r,n),P=jm(y),j=h[N?b==="floating"?"reference":"floating":b],_=La(await f.getClippingRect({element:(s=await(f.isElement==null?void 0:f.isElement(j)))==null||s?j:j.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:v,rootBoundary:w,strategy:m})),E=b==="floating"?{x:a,y:c,width:p.floating.width,height:p.floating.height}:p.reference,A=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),z=await(f.isElement==null?void 0:f.isElement(A))?await(f.getScale==null?void 0:f.getScale(A))||{x:1,y:1}:{x:1,y:1},O=La(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:E,offsetParent:A,strategy:m}):E);return{top:(_.top-O.top+P.top)/z.y,bottom:(O.bottom-_.bottom+P.bottom)/z.y,left:(_.left-O.left+P.left)/z.x,right:(O.right-_.right+P.right)/z.x}}const Jw=n=>({name:"arrow",options:n,async fn(r){const{x:s,y:a,placement:c,rects:f,platform:p,elements:h,middlewareData:m}=r,{element:v,padding:w=0}=er(n,r)||{};if(v==null)return{};const b=jm(w),N={x:s,y:a},y=Pc(c),P=Ec(y),k=await p.getDimensions(v),j=y==="y",_=j?"top":"left",E=j?"bottom":"right",A=j?"clientHeight":"clientWidth",z=f.reference[P]+f.reference[y]-N[y]-f.floating[P],O=N[y]-f.reference[y],F=await(p.getOffsetParent==null?void 0:p.getOffsetParent(v));let $=F?F[A]:0;(!$||!await(p.isElement==null?void 0:p.isElement(F)))&&($=h.floating[A]||f.floating[P]);const G=z/2-O/2,J=$/2-k[P]/2-1,Z=Fr(b[_],J),pe=Fr(b[E],J),te=Z,ge=$-k[P]-pe,X=$/2-k[P]/2+G,ve=ac(te,X,ge),se=!m.arrow&&is(c)!=null&&X!==ve&&f.reference[P]/2-(X<te?Z:pe)-k[P]/2<0,le=se?X<te?X-te:X-ge:0;return{[y]:N[y]+le,data:{[y]:ve,centerOffset:X-ve-le,...se&&{alignmentOffset:le}},reset:se}}}),Zw=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(r){var s,a;const{placement:c,middlewareData:f,rects:p,initialPlacement:h,platform:m,elements:v}=r,{mainAxis:w=!0,crossAxis:b=!0,fallbackPlacements:N,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:k=!0,...j}=er(n,r);if((s=f.arrow)!=null&&s.alignmentOffset)return{};const _=tr(c),E=On(h),A=tr(h)===h,z=await(m.isRTL==null?void 0:m.isRTL(v.floating)),O=N||(A||!k?[Fa(h)]:Ww(h)),F=P!=="none";!N&&F&&O.push(...Gw(h,k,P,z));const $=[h,...O],G=await Js(r,j),J=[];let Z=((a=f.flip)==null?void 0:a.overflows)||[];if(w&&J.push(G[_]),b){const X=Hw(c,p,z);J.push(G[X[0]],G[X[1]])}if(Z=[...Z,{placement:c,overflows:J}],!J.every(X=>X<=0)){var pe,te;const X=(((pe=f.flip)==null?void 0:pe.index)||0)+1,ve=$[X];if(ve&&(!(b==="alignment"?E!==On(ve):!1)||Z.every(U=>U.overflows[0]>0&&On(U.placement)===E)))return{data:{index:X,overflows:Z},reset:{placement:ve}};let se=(te=Z.filter(le=>le.overflows[0]<=0).sort((le,U)=>le.overflows[1]-U.overflows[1])[0])==null?void 0:te.placement;if(!se)switch(y){case"bestFit":{var ge;const le=(ge=Z.filter(U=>{if(F){const V=On(U.placement);return V===E||V==="y"}return!0}).map(U=>[U.placement,U.overflows.filter(V=>V>0).reduce((V,q)=>V+q,0)]).sort((U,V)=>U[1]-V[1])[0])==null?void 0:ge[0];le&&(se=le);break}case"initialPlacement":se=h;break}if(c!==se)return{reset:{placement:se}}}return{}}}};function qp(n,r){return{top:n.top-r.height,right:n.right-r.width,bottom:n.bottom-r.height,left:n.left-r.width}}function Kp(n){return Uw.some(r=>n[r]>=0)}const e1=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(r){const{rects:s}=r,{strategy:a="referenceHidden",...c}=er(n,r);switch(a){case"referenceHidden":{const f=await Js(r,{...c,elementContext:"reference"}),p=qp(f,s.reference);return{data:{referenceHiddenOffsets:p,referenceHidden:Kp(p)}}}case"escaped":{const f=await Js(r,{...c,altBoundary:!0}),p=qp(f,s.floating);return{data:{escapedOffsets:p,escaped:Kp(p)}}}default:return{}}}}},km=new Set(["left","top"]);async function t1(n,r){const{placement:s,platform:a,elements:c}=n,f=await(a.isRTL==null?void 0:a.isRTL(c.floating)),p=tr(s),h=is(s),m=On(s)==="y",v=km.has(p)?-1:1,w=f&&m?-1:1,b=er(r,n);let{mainAxis:N,crossAxis:y,alignmentAxis:P}=typeof b=="number"?{mainAxis:b,crossAxis:0,alignmentAxis:null}:{mainAxis:b.mainAxis||0,crossAxis:b.crossAxis||0,alignmentAxis:b.alignmentAxis};return h&&typeof P=="number"&&(y=h==="end"?P*-1:P),m?{x:y*w,y:N*v}:{x:N*v,y:y*w}}const n1=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(r){var s,a;const{x:c,y:f,placement:p,middlewareData:h}=r,m=await t1(r,n);return p===((s=h.offset)==null?void 0:s.placement)&&(a=h.arrow)!=null&&a.alignmentOffset?{}:{x:c+m.x,y:f+m.y,data:{...m,placement:p}}}}},r1=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(r){const{x:s,y:a,placement:c}=r,{mainAxis:f=!0,crossAxis:p=!1,limiter:h={fn:j=>{let{x:_,y:E}=j;return{x:_,y:E}}},...m}=er(n,r),v={x:s,y:a},w=await Js(r,m),b=On(tr(c)),N=Cc(b);let y=v[N],P=v[b];if(f){const j=N==="y"?"top":"left",_=N==="y"?"bottom":"right",E=y+w[j],A=y-w[_];y=ac(E,y,A)}if(p){const j=b==="y"?"top":"left",_=b==="y"?"bottom":"right",E=P+w[j],A=P-w[_];P=ac(E,P,A)}const k=h.fn({...r,[N]:y,[b]:P});return{...k,data:{x:k.x-s,y:k.y-a,enabled:{[N]:f,[b]:p}}}}}},o1=function(n){return n===void 0&&(n={}),{options:n,fn(r){const{x:s,y:a,placement:c,rects:f,middlewareData:p}=r,{offset:h=0,mainAxis:m=!0,crossAxis:v=!0}=er(n,r),w={x:s,y:a},b=On(c),N=Cc(b);let y=w[N],P=w[b];const k=er(h,r),j=typeof k=="number"?{mainAxis:k,crossAxis:0}:{mainAxis:0,crossAxis:0,...k};if(m){const A=N==="y"?"height":"width",z=f.reference[N]-f.floating[A]+j.mainAxis,O=f.reference[N]+f.reference[A]-j.mainAxis;y<z?y=z:y>O&&(y=O)}if(v){var _,E;const A=N==="y"?"width":"height",z=km.has(tr(c)),O=f.reference[b]-f.floating[A]+(z&&((_=p.offset)==null?void 0:_[b])||0)+(z?0:j.crossAxis),F=f.reference[b]+f.reference[A]+(z?0:((E=p.offset)==null?void 0:E[b])||0)-(z?j.crossAxis:0);P<O?P=O:P>F&&(P=F)}return{[N]:y,[b]:P}}}},s1=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(r){var s,a;const{placement:c,rects:f,platform:p,elements:h}=r,{apply:m=()=>{},...v}=er(n,r),w=await Js(r,v),b=tr(c),N=is(c),y=On(c)==="y",{width:P,height:k}=f.floating;let j,_;b==="top"||b==="bottom"?(j=b,_=N===(await(p.isRTL==null?void 0:p.isRTL(h.floating))?"start":"end")?"left":"right"):(_=b,j=N==="end"?"top":"bottom");const E=k-w.top-w.bottom,A=P-w.left-w.right,z=Fr(k-w[j],E),O=Fr(P-w[_],A),F=!r.middlewareData.shift;let $=z,G=O;if((s=r.middlewareData.shift)!=null&&s.enabled.x&&(G=A),(a=r.middlewareData.shift)!=null&&a.enabled.y&&($=E),F&&!N){const Z=Lt(w.left,0),pe=Lt(w.right,0),te=Lt(w.top,0),ge=Lt(w.bottom,0);y?G=P-2*(Z!==0||pe!==0?Z+pe:Lt(w.left,w.right)):$=k-2*(te!==0||ge!==0?te+ge:Lt(w.top,w.bottom))}await m({...r,availableWidth:G,availableHeight:$});const J=await p.getDimensions(h.floating);return P!==J.width||k!==J.height?{reset:{rects:!0}}:{}}}};function Wa(){return typeof window<"u"}function as(n){return Nm(n)?(n.nodeName||"").toLowerCase():"#document"}function zt(n){var r;return(n==null||(r=n.ownerDocument)==null?void 0:r.defaultView)||window}function In(n){var r;return(r=(Nm(n)?n.ownerDocument:n.document)||window.document)==null?void 0:r.documentElement}function Nm(n){return Wa()?n instanceof Node||n instanceof zt(n).Node:!1}function mn(n){return Wa()?n instanceof Element||n instanceof zt(n).Element:!1}function Dn(n){return Wa()?n instanceof HTMLElement||n instanceof zt(n).HTMLElement:!1}function Gp(n){return!Wa()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof zt(n).ShadowRoot}const i1=new Set(["inline","contents"]);function li(n){const{overflow:r,overflowX:s,overflowY:a,display:c}=gn(n);return/auto|scroll|overlay|hidden|clip/.test(r+a+s)&&!i1.has(c)}const a1=new Set(["table","td","th"]);function l1(n){return a1.has(as(n))}const u1=[":popover-open",":modal"];function Qa(n){return u1.some(r=>{try{return n.matches(r)}catch{return!1}})}const c1=["transform","translate","scale","rotate","perspective"],d1=["transform","translate","scale","rotate","perspective","filter"],f1=["paint","layout","strict","content"];function Tc(n){const r=Rc(),s=mn(n)?gn(n):n;return c1.some(a=>s[a]?s[a]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!r&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!r&&(s.filter?s.filter!=="none":!1)||d1.some(a=>(s.willChange||"").includes(a))||f1.some(a=>(s.contain||"").includes(a))}function p1(n){let r=Lr(n);for(;Dn(r)&&!Zo(r);){if(Tc(r))return r;if(Qa(r))return null;r=Lr(r)}return null}function Rc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const h1=new Set(["html","body","#document"]);function Zo(n){return h1.has(as(n))}function gn(n){return zt(n).getComputedStyle(n)}function qa(n){return mn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Lr(n){if(as(n)==="html")return n;const r=n.assignedSlot||n.parentNode||Gp(n)&&n.host||In(n);return Gp(r)?r.host:r}function Sm(n){const r=Lr(n);return Zo(r)?n.ownerDocument?n.ownerDocument.body:n.body:Dn(r)&&li(r)?r:Sm(r)}function Zs(n,r,s){var a;r===void 0&&(r=[]),s===void 0&&(s=!0);const c=Sm(n),f=c===((a=n.ownerDocument)==null?void 0:a.body),p=zt(c);if(f){const h=uc(p);return r.concat(p,p.visualViewport||[],li(c)?c:[],h&&s?Zs(h):[])}return r.concat(c,Zs(c,[],s))}function uc(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function Cm(n){const r=gn(n);let s=parseFloat(r.width)||0,a=parseFloat(r.height)||0;const c=Dn(n),f=c?n.offsetWidth:s,p=c?n.offsetHeight:a,h=Ia(s)!==f||Ia(a)!==p;return h&&(s=f,a=p),{width:s,height:a,$:h}}function _c(n){return mn(n)?n:n.contextElement}function Ho(n){const r=_c(n);if(!Dn(r))return Mn(1);const s=r.getBoundingClientRect(),{width:a,height:c,$:f}=Cm(r);let p=(f?Ia(s.width):s.width)/a,h=(f?Ia(s.height):s.height)/c;return(!p||!Number.isFinite(p))&&(p=1),(!h||!Number.isFinite(h))&&(h=1),{x:p,y:h}}const m1=Mn(0);function Em(n){const r=zt(n);return!Rc()||!r.visualViewport?m1:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function g1(n,r,s){return r===void 0&&(r=!1),!s||r&&s!==zt(n)?!1:r}function co(n,r,s,a){r===void 0&&(r=!1),s===void 0&&(s=!1);const c=n.getBoundingClientRect(),f=_c(n);let p=Mn(1);r&&(a?mn(a)&&(p=Ho(a)):p=Ho(n));const h=g1(f,s,a)?Em(f):Mn(0);let m=(c.left+h.x)/p.x,v=(c.top+h.y)/p.y,w=c.width/p.x,b=c.height/p.y;if(f){const N=zt(f),y=a&&mn(a)?zt(a):a;let P=N,k=uc(P);for(;k&&a&&y!==P;){const j=Ho(k),_=k.getBoundingClientRect(),E=gn(k),A=_.left+(k.clientLeft+parseFloat(E.paddingLeft))*j.x,z=_.top+(k.clientTop+parseFloat(E.paddingTop))*j.y;m*=j.x,v*=j.y,w*=j.x,b*=j.y,m+=A,v+=z,P=zt(k),k=uc(P)}}return La({width:w,height:b,x:m,y:v})}function Ac(n,r){const s=qa(n).scrollLeft;return r?r.left+s:co(In(n)).left+s}function Pm(n,r,s){s===void 0&&(s=!1);const a=n.getBoundingClientRect(),c=a.left+r.scrollLeft-(s?0:Ac(n,a)),f=a.top+r.scrollTop;return{x:c,y:f}}function v1(n){let{elements:r,rect:s,offsetParent:a,strategy:c}=n;const f=c==="fixed",p=In(a),h=r?Qa(r.floating):!1;if(a===p||h&&f)return s;let m={scrollLeft:0,scrollTop:0},v=Mn(1);const w=Mn(0),b=Dn(a);if((b||!b&&!f)&&((as(a)!=="body"||li(p))&&(m=qa(a)),Dn(a))){const y=co(a);v=Ho(a),w.x=y.x+a.clientLeft,w.y=y.y+a.clientTop}const N=p&&!b&&!f?Pm(p,m,!0):Mn(0);return{width:s.width*v.x,height:s.height*v.y,x:s.x*v.x-m.scrollLeft*v.x+w.x+N.x,y:s.y*v.y-m.scrollTop*v.y+w.y+N.y}}function y1(n){return Array.from(n.getClientRects())}function x1(n){const r=In(n),s=qa(n),a=n.ownerDocument.body,c=Lt(r.scrollWidth,r.clientWidth,a.scrollWidth,a.clientWidth),f=Lt(r.scrollHeight,r.clientHeight,a.scrollHeight,a.clientHeight);let p=-s.scrollLeft+Ac(n);const h=-s.scrollTop;return gn(a).direction==="rtl"&&(p+=Lt(r.clientWidth,a.clientWidth)-c),{width:c,height:f,x:p,y:h}}function w1(n,r){const s=zt(n),a=In(n),c=s.visualViewport;let f=a.clientWidth,p=a.clientHeight,h=0,m=0;if(c){f=c.width,p=c.height;const v=Rc();(!v||v&&r==="fixed")&&(h=c.offsetLeft,m=c.offsetTop)}return{width:f,height:p,x:h,y:m}}const b1=new Set(["absolute","fixed"]);function j1(n,r){const s=co(n,!0,r==="fixed"),a=s.top+n.clientTop,c=s.left+n.clientLeft,f=Dn(n)?Ho(n):Mn(1),p=n.clientWidth*f.x,h=n.clientHeight*f.y,m=c*f.x,v=a*f.y;return{width:p,height:h,x:m,y:v}}function Yp(n,r,s){let a;if(r==="viewport")a=w1(n,s);else if(r==="document")a=x1(In(n));else if(mn(r))a=j1(r,s);else{const c=Em(n);a={x:r.x-c.x,y:r.y-c.y,width:r.width,height:r.height}}return La(a)}function Tm(n,r){const s=Lr(n);return s===r||!mn(s)||Zo(s)?!1:gn(s).position==="fixed"||Tm(s,r)}function k1(n,r){const s=r.get(n);if(s)return s;let a=Zs(n,[],!1).filter(h=>mn(h)&&as(h)!=="body"),c=null;const f=gn(n).position==="fixed";let p=f?Lr(n):n;for(;mn(p)&&!Zo(p);){const h=gn(p),m=Tc(p);!m&&h.position==="fixed"&&(c=null),(f?!m&&!c:!m&&h.position==="static"&&!!c&&b1.has(c.position)||li(p)&&!m&&Tm(n,p))?a=a.filter(w=>w!==p):c=h,p=Lr(p)}return r.set(n,a),a}function N1(n){let{element:r,boundary:s,rootBoundary:a,strategy:c}=n;const p=[...s==="clippingAncestors"?Qa(r)?[]:k1(r,this._c):[].concat(s),a],h=p[0],m=p.reduce((v,w)=>{const b=Yp(r,w,c);return v.top=Lt(b.top,v.top),v.right=Fr(b.right,v.right),v.bottom=Fr(b.bottom,v.bottom),v.left=Lt(b.left,v.left),v},Yp(r,h,c));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function S1(n){const{width:r,height:s}=Cm(n);return{width:r,height:s}}function C1(n,r,s){const a=Dn(r),c=In(r),f=s==="fixed",p=co(n,!0,f,r);let h={scrollLeft:0,scrollTop:0};const m=Mn(0);function v(){m.x=Ac(c)}if(a||!a&&!f)if((as(r)!=="body"||li(c))&&(h=qa(r)),a){const y=co(r,!0,f,r);m.x=y.x+r.clientLeft,m.y=y.y+r.clientTop}else c&&v();f&&!a&&c&&v();const w=c&&!a&&!f?Pm(c,h):Mn(0),b=p.left+h.scrollLeft-m.x-w.x,N=p.top+h.scrollTop-m.y-w.y;return{x:b,y:N,width:p.width,height:p.height}}function Gu(n){return gn(n).position==="static"}function Xp(n,r){if(!Dn(n)||gn(n).position==="fixed")return null;if(r)return r(n);let s=n.offsetParent;return In(n)===s&&(s=s.ownerDocument.body),s}function Rm(n,r){const s=zt(n);if(Qa(n))return s;if(!Dn(n)){let c=Lr(n);for(;c&&!Zo(c);){if(mn(c)&&!Gu(c))return c;c=Lr(c)}return s}let a=Xp(n,r);for(;a&&l1(a)&&Gu(a);)a=Xp(a,r);return a&&Zo(a)&&Gu(a)&&!Tc(a)?s:a||p1(n)||s}const E1=async function(n){const r=this.getOffsetParent||Rm,s=this.getDimensions,a=await s(n.floating);return{reference:C1(n.reference,await r(n.floating),n.strategy),floating:{x:0,y:0,width:a.width,height:a.height}}};function P1(n){return gn(n).direction==="rtl"}const T1={convertOffsetParentRelativeRectToViewportRelativeRect:v1,getDocumentElement:In,getClippingRect:N1,getOffsetParent:Rm,getElementRects:E1,getClientRects:y1,getDimensions:S1,getScale:Ho,isElement:mn,isRTL:P1};function _m(n,r){return n.x===r.x&&n.y===r.y&&n.width===r.width&&n.height===r.height}function R1(n,r){let s=null,a;const c=In(n);function f(){var h;clearTimeout(a),(h=s)==null||h.disconnect(),s=null}function p(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),f();const v=n.getBoundingClientRect(),{left:w,top:b,width:N,height:y}=v;if(h||r(),!N||!y)return;const P=Ta(b),k=Ta(c.clientWidth-(w+N)),j=Ta(c.clientHeight-(b+y)),_=Ta(w),A={rootMargin:-P+"px "+-k+"px "+-j+"px "+-_+"px",threshold:Lt(0,Fr(1,m))||1};let z=!0;function O(F){const $=F[0].intersectionRatio;if($!==m){if(!z)return p();$?p(!1,$):a=setTimeout(()=>{p(!1,1e-7)},1e3)}$===1&&!_m(v,n.getBoundingClientRect())&&p(),z=!1}try{s=new IntersectionObserver(O,{...A,root:c.ownerDocument})}catch{s=new IntersectionObserver(O,A)}s.observe(n)}return p(!0),f}function _1(n,r,s,a){a===void 0&&(a={});const{ancestorScroll:c=!0,ancestorResize:f=!0,elementResize:p=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=a,v=_c(n),w=c||f?[...v?Zs(v):[],...Zs(r)]:[];w.forEach(_=>{c&&_.addEventListener("scroll",s,{passive:!0}),f&&_.addEventListener("resize",s)});const b=v&&h?R1(v,s):null;let N=-1,y=null;p&&(y=new ResizeObserver(_=>{let[E]=_;E&&E.target===v&&y&&(y.unobserve(r),cancelAnimationFrame(N),N=requestAnimationFrame(()=>{var A;(A=y)==null||A.observe(r)})),s()}),v&&!m&&y.observe(v),y.observe(r));let P,k=m?co(n):null;m&&j();function j(){const _=co(n);k&&!_m(k,_)&&s(),k=_,P=requestAnimationFrame(j)}return s(),()=>{var _;w.forEach(E=>{c&&E.removeEventListener("scroll",s),f&&E.removeEventListener("resize",s)}),b==null||b(),(_=y)==null||_.disconnect(),y=null,m&&cancelAnimationFrame(P)}}const A1=n1,O1=r1,M1=Zw,D1=s1,I1=e1,Jp=Jw,F1=o1,L1=(n,r,s)=>{const a=new Map,c={platform:T1,...s},f={...c.platform,_c:a};return Xw(n,r,{...c,platform:f})};var z1=typeof document<"u",U1=function(){},Ma=z1?x.useLayoutEffect:U1;function za(n,r){if(n===r)return!0;if(typeof n!=typeof r)return!1;if(typeof n=="function"&&n.toString()===r.toString())return!0;let s,a,c;if(n&&r&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==r.length)return!1;for(a=s;a--!==0;)if(!za(n[a],r[a]))return!1;return!0}if(c=Object.keys(n),s=c.length,s!==Object.keys(r).length)return!1;for(a=s;a--!==0;)if(!{}.hasOwnProperty.call(r,c[a]))return!1;for(a=s;a--!==0;){const f=c[a];if(!(f==="_owner"&&n.$$typeof)&&!za(n[f],r[f]))return!1}return!0}return n!==n&&r!==r}function Am(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function Zp(n,r){const s=Am(n);return Math.round(r*s)/s}function Yu(n){const r=x.useRef(n);return Ma(()=>{r.current=n}),r}function $1(n){n===void 0&&(n={});const{placement:r="bottom",strategy:s="absolute",middleware:a=[],platform:c,elements:{reference:f,floating:p}={},transform:h=!0,whileElementsMounted:m,open:v}=n,[w,b]=x.useState({x:0,y:0,strategy:s,placement:r,middlewareData:{},isPositioned:!1}),[N,y]=x.useState(a);za(N,a)||y(a);const[P,k]=x.useState(null),[j,_]=x.useState(null),E=x.useCallback(U=>{U!==F.current&&(F.current=U,k(U))},[]),A=x.useCallback(U=>{U!==$.current&&($.current=U,_(U))},[]),z=f||P,O=p||j,F=x.useRef(null),$=x.useRef(null),G=x.useRef(w),J=m!=null,Z=Yu(m),pe=Yu(c),te=Yu(v),ge=x.useCallback(()=>{if(!F.current||!$.current)return;const U={placement:r,strategy:s,middleware:N};pe.current&&(U.platform=pe.current),L1(F.current,$.current,U).then(V=>{const q={...V,isPositioned:te.current!==!1};X.current&&!za(G.current,q)&&(G.current=q,$a.flushSync(()=>{b(q)}))})},[N,r,s,pe,te]);Ma(()=>{v===!1&&G.current.isPositioned&&(G.current.isPositioned=!1,b(U=>({...U,isPositioned:!1})))},[v]);const X=x.useRef(!1);Ma(()=>(X.current=!0,()=>{X.current=!1}),[]),Ma(()=>{if(z&&(F.current=z),O&&($.current=O),z&&O){if(Z.current)return Z.current(z,O,ge);ge()}},[z,O,ge,Z,J]);const ve=x.useMemo(()=>({reference:F,floating:$,setReference:E,setFloating:A}),[E,A]),se=x.useMemo(()=>({reference:z,floating:O}),[z,O]),le=x.useMemo(()=>{const U={position:s,left:0,top:0};if(!se.floating)return U;const V=Zp(se.floating,w.x),q=Zp(se.floating,w.y);return h?{...U,transform:"translate("+V+"px, "+q+"px)",...Am(se.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:V,top:q}},[s,h,se.floating,w.x,w.y]);return x.useMemo(()=>({...w,update:ge,refs:ve,elements:se,floatingStyles:le}),[w,ge,ve,se,le])}const B1=n=>{function r(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:a,padding:c}=typeof n=="function"?n(s):n;return a&&r(a)?a.current!=null?Jp({element:a.current,padding:c}).fn(s):{}:a?Jp({element:a,padding:c}).fn(s):{}}}},V1=(n,r)=>({...A1(n),options:[n,r]}),H1=(n,r)=>({...O1(n),options:[n,r]}),W1=(n,r)=>({...F1(n),options:[n,r]}),Q1=(n,r)=>({...M1(n),options:[n,r]}),q1=(n,r)=>({...D1(n),options:[n,r]}),K1=(n,r)=>({...I1(n),options:[n,r]}),G1=(n,r)=>({...B1(n),options:[n,r]});var Y1="Arrow",Om=x.forwardRef((n,r)=>{const{children:s,width:a=10,height:c=5,...f}=n;return l.jsx($e.svg,{...f,ref:r,width:a,height:c,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});Om.displayName=Y1;var X1=Om;function Oc(n){const[r,s]=x.useState(void 0);return Zn(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const a=new ResizeObserver(c=>{if(!Array.isArray(c)||!c.length)return;const f=c[0];let p,h;if("borderBoxSize"in f){const m=f.borderBoxSize,v=Array.isArray(m)?m[0]:m;p=v.inlineSize,h=v.blockSize}else p=n.offsetWidth,h=n.offsetHeight;s({width:p,height:h})});return a.observe(n,{box:"border-box"}),()=>a.unobserve(n)}else s(void 0)},[n]),r}var Mm="Popper",[Dm,Im]=zr(Mm),[Z2,Fm]=Dm(Mm),Lm="PopperAnchor",zm=x.forwardRef((n,r)=>{const{__scopePopper:s,virtualRef:a,...c}=n,f=Fm(Lm,s),p=x.useRef(null),h=ut(r,p);return x.useEffect(()=>{f.onAnchorChange((a==null?void 0:a.current)||p.current)}),a?null:l.jsx($e.div,{...c,ref:h})});zm.displayName=Lm;var Mc="PopperContent",[J1,Z1]=Dm(Mc),Um=x.forwardRef((n,r)=>{var ce,Ce,Pe,Ge,_t,Fn;const{__scopePopper:s,side:a="bottom",sideOffset:c=0,align:f="center",alignOffset:p=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:v=[],collisionPadding:w=0,sticky:b="partial",hideWhenDetached:N=!1,updatePositionStrategy:y="optimized",onPlaced:P,...k}=n,j=Fm(Mc,s),[_,E]=x.useState(null),A=ut(r,At=>E(At)),[z,O]=x.useState(null),F=Oc(z),$=(F==null?void 0:F.width)??0,G=(F==null?void 0:F.height)??0,J=a+(f!=="center"?"-"+f:""),Z=typeof w=="number"?w:{top:0,right:0,bottom:0,left:0,...w},pe=Array.isArray(v)?v:[v],te=pe.length>0,ge={padding:Z,boundary:pe.filter(tb),altBoundary:te},{refs:X,floatingStyles:ve,placement:se,isPositioned:le,middlewareData:U}=$1({strategy:"fixed",placement:J,whileElementsMounted:(...At)=>_1(...At,{animationFrame:y==="always"}),elements:{reference:j.anchor},middleware:[V1({mainAxis:c+G,alignmentAxis:p}),m&&H1({mainAxis:!0,crossAxis:!1,limiter:b==="partial"?W1():void 0,...ge}),m&&Q1({...ge}),q1({...ge,apply:({elements:At,rects:nr,availableWidth:ho,availableHeight:Ur})=>{const{width:$r,height:rr}=nr.reference,vn=At.floating.style;vn.setProperty("--radix-popper-available-width",`${ho}px`),vn.setProperty("--radix-popper-available-height",`${Ur}px`),vn.setProperty("--radix-popper-anchor-width",`${$r}px`),vn.setProperty("--radix-popper-anchor-height",`${rr}px`)}}),z&&G1({element:z,padding:h}),nb({arrowWidth:$,arrowHeight:G}),N&&K1({strategy:"referenceHidden",...ge})]}),[V,q]=Vm(se),R=Jn(P);Zn(()=>{le&&(R==null||R())},[le,R]);const L=(ce=U.arrow)==null?void 0:ce.x,ee=(Ce=U.arrow)==null?void 0:Ce.y,re=((Pe=U.arrow)==null?void 0:Pe.centerOffset)!==0,[he,be]=x.useState();return Zn(()=>{_&&be(window.getComputedStyle(_).zIndex)},[_]),l.jsx("div",{ref:X.setFloating,"data-radix-popper-content-wrapper":"",style:{...ve,transform:le?ve.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:he,"--radix-popper-transform-origin":[(Ge=U.transformOrigin)==null?void 0:Ge.x,(_t=U.transformOrigin)==null?void 0:_t.y].join(" "),...((Fn=U.hide)==null?void 0:Fn.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:l.jsx(J1,{scope:s,placedSide:V,onArrowChange:O,arrowX:L,arrowY:ee,shouldHideArrow:re,children:l.jsx($e.div,{"data-side":V,"data-align":q,...k,ref:A,style:{...k.style,animation:le?void 0:"none"}})})})});Um.displayName=Mc;var $m="PopperArrow",eb={top:"bottom",right:"left",bottom:"top",left:"right"},Bm=x.forwardRef(function(r,s){const{__scopePopper:a,...c}=r,f=Z1($m,a),p=eb[f.placedSide];return l.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[p]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:l.jsx(X1,{...c,ref:s,style:{...c.style,display:"block"}})})});Bm.displayName=$m;function tb(n){return n!==null}var nb=n=>({name:"transformOrigin",options:n,fn(r){var j,_,E;const{placement:s,rects:a,middlewareData:c}=r,p=((j=c.arrow)==null?void 0:j.centerOffset)!==0,h=p?0:n.arrowWidth,m=p?0:n.arrowHeight,[v,w]=Vm(s),b={start:"0%",center:"50%",end:"100%"}[w],N=(((_=c.arrow)==null?void 0:_.x)??0)+h/2,y=(((E=c.arrow)==null?void 0:E.y)??0)+m/2;let P="",k="";return v==="bottom"?(P=p?b:`${N}px`,k=`${-m}px`):v==="top"?(P=p?b:`${N}px`,k=`${a.floating.height+m}px`):v==="right"?(P=`${-m}px`,k=p?b:`${y}px`):v==="left"&&(P=`${a.floating.width+m}px`,k=p?b:`${y}px`),{data:{x:P,y:k}}}});function Vm(n){const[r,s="center"]=n.split("-");return[r,s]}var rb=zm,ob=Um,sb=Bm,[Ka,ek]=zr("Tooltip",[Im]),Dc=Im(),Hm="TooltipProvider",ib=700,eh="tooltip.open",[ab,Wm]=Ka(Hm),Qm=n=>{const{__scopeTooltip:r,delayDuration:s=ib,skipDelayDuration:a=300,disableHoverableContent:c=!1,children:f}=n,p=x.useRef(!0),h=x.useRef(!1),m=x.useRef(0);return x.useEffect(()=>{const v=m.current;return()=>window.clearTimeout(v)},[]),l.jsx(ab,{scope:r,isOpenDelayedRef:p,delayDuration:s,onOpen:x.useCallback(()=>{window.clearTimeout(m.current),p.current=!1},[]),onClose:x.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>p.current=!0,a)},[a]),isPointerInTransitRef:h,onPointerInTransitChange:x.useCallback(v=>{h.current=v},[]),disableHoverableContent:c,children:f})};Qm.displayName=Hm;var qm="Tooltip",[tk,Ga]=Ka(qm),cc="TooltipTrigger",lb=x.forwardRef((n,r)=>{const{__scopeTooltip:s,...a}=n,c=Ga(cc,s),f=Wm(cc,s),p=Dc(s),h=x.useRef(null),m=ut(r,h,c.onTriggerChange),v=x.useRef(!1),w=x.useRef(!1),b=x.useCallback(()=>v.current=!1,[]);return x.useEffect(()=>()=>document.removeEventListener("pointerup",b),[b]),l.jsx(rb,{asChild:!0,...p,children:l.jsx($e.button,{"aria-describedby":c.open?c.contentId:void 0,"data-state":c.stateAttribute,...a,ref:m,onPointerMove:Oe(n.onPointerMove,N=>{N.pointerType!=="touch"&&!w.current&&!f.isPointerInTransitRef.current&&(c.onTriggerEnter(),w.current=!0)}),onPointerLeave:Oe(n.onPointerLeave,()=>{c.onTriggerLeave(),w.current=!1}),onPointerDown:Oe(n.onPointerDown,()=>{c.open&&c.onClose(),v.current=!0,document.addEventListener("pointerup",b,{once:!0})}),onFocus:Oe(n.onFocus,()=>{v.current||c.onOpen()}),onBlur:Oe(n.onBlur,c.onClose),onClick:Oe(n.onClick,c.onClose)})})});lb.displayName=cc;var ub="TooltipPortal",[nk,cb]=Ka(ub,{forceMount:void 0}),es="TooltipContent",Km=x.forwardRef((n,r)=>{const s=cb(es,n.__scopeTooltip),{forceMount:a=s.forceMount,side:c="top",...f}=n,p=Ga(es,n.__scopeTooltip);return l.jsx(ii,{present:a||p.open,children:p.disableHoverableContent?l.jsx(Gm,{side:c,...f,ref:r}):l.jsx(db,{side:c,...f,ref:r})})}),db=x.forwardRef((n,r)=>{const s=Ga(es,n.__scopeTooltip),a=Wm(es,n.__scopeTooltip),c=x.useRef(null),f=ut(r,c),[p,h]=x.useState(null),{trigger:m,onClose:v}=s,w=c.current,{onPointerInTransitChange:b}=a,N=x.useCallback(()=>{h(null),b(!1)},[b]),y=x.useCallback((P,k)=>{const j=P.currentTarget,_={x:P.clientX,y:P.clientY},E=gb(_,j.getBoundingClientRect()),A=vb(_,E),z=yb(k.getBoundingClientRect()),O=wb([...A,...z]);h(O),b(!0)},[b]);return x.useEffect(()=>()=>N(),[N]),x.useEffect(()=>{if(m&&w){const P=j=>y(j,w),k=j=>y(j,m);return m.addEventListener("pointerleave",P),w.addEventListener("pointerleave",k),()=>{m.removeEventListener("pointerleave",P),w.removeEventListener("pointerleave",k)}}},[m,w,y,N]),x.useEffect(()=>{if(p){const P=k=>{const j=k.target,_={x:k.clientX,y:k.clientY},E=(m==null?void 0:m.contains(j))||(w==null?void 0:w.contains(j)),A=!xb(_,p);E?N():A&&(N(),v())};return document.addEventListener("pointermove",P),()=>document.removeEventListener("pointermove",P)}},[m,w,p,v,N]),l.jsx(Gm,{...n,ref:f})}),[fb,pb]=Ka(qm,{isInside:!1}),hb=ly("TooltipContent"),Gm=x.forwardRef((n,r)=>{const{__scopeTooltip:s,children:a,"aria-label":c,onEscapeKeyDown:f,onPointerDownOutside:p,...h}=n,m=Ga(es,s),v=Dc(s),{onClose:w}=m;return x.useEffect(()=>(document.addEventListener(eh,w),()=>document.removeEventListener(eh,w)),[w]),x.useEffect(()=>{if(m.trigger){const b=N=>{const y=N.target;y!=null&&y.contains(m.trigger)&&w()};return window.addEventListener("scroll",b,{capture:!0}),()=>window.removeEventListener("scroll",b,{capture:!0})}},[m.trigger,w]),l.jsx(xc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:b=>b.preventDefault(),onDismiss:w,children:l.jsxs(ob,{"data-state":m.stateAttribute,...v,...h,ref:r,style:{...h.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(hb,{children:a}),l.jsx(fb,{scope:s,isInside:!0,children:l.jsx(_y,{id:m.contentId,role:"tooltip",children:c||a})})]})})});Km.displayName=es;var Ym="TooltipArrow",mb=x.forwardRef((n,r)=>{const{__scopeTooltip:s,...a}=n,c=Dc(s);return pb(Ym,s).isInside?null:l.jsx(sb,{...c,...a,ref:r})});mb.displayName=Ym;function gb(n,r){const s=Math.abs(r.top-n.y),a=Math.abs(r.bottom-n.y),c=Math.abs(r.right-n.x),f=Math.abs(r.left-n.x);switch(Math.min(s,a,c,f)){case f:return"left";case c:return"right";case s:return"top";case a:return"bottom";default:throw new Error("unreachable")}}function vb(n,r,s=5){const a=[];switch(r){case"top":a.push({x:n.x-s,y:n.y+s},{x:n.x+s,y:n.y+s});break;case"bottom":a.push({x:n.x-s,y:n.y-s},{x:n.x+s,y:n.y-s});break;case"left":a.push({x:n.x+s,y:n.y-s},{x:n.x+s,y:n.y+s});break;case"right":a.push({x:n.x-s,y:n.y-s},{x:n.x-s,y:n.y+s});break}return a}function yb(n){const{top:r,right:s,bottom:a,left:c}=n;return[{x:c,y:r},{x:s,y:r},{x:s,y:a},{x:c,y:a}]}function xb(n,r){const{x:s,y:a}=n;let c=!1;for(let f=0,p=r.length-1;f<r.length;p=f++){const h=r[f],m=r[p],v=h.x,w=h.y,b=m.x,N=m.y;w>a!=N>a&&s<(b-v)*(a-w)/(N-w)+v&&(c=!c)}return c}function wb(n){const r=n.slice();return r.sort((s,a)=>s.x<a.x?-1:s.x>a.x?1:s.y<a.y?-1:s.y>a.y?1:0),bb(r)}function bb(n){if(n.length<=1)return n.slice();const r=[];for(let a=0;a<n.length;a++){const c=n[a];for(;r.length>=2;){const f=r[r.length-1],p=r[r.length-2];if((f.x-p.x)*(c.y-p.y)>=(f.y-p.y)*(c.x-p.x))r.pop();else break}r.push(c)}r.pop();const s=[];for(let a=n.length-1;a>=0;a--){const c=n[a];for(;s.length>=2;){const f=s[s.length-1],p=s[s.length-2];if((f.x-p.x)*(c.y-p.y)>=(f.y-p.y)*(c.x-p.x))s.pop();else break}s.push(c)}return s.pop(),r.length===1&&s.length===1&&r[0].x===s[0].x&&r[0].y===s[0].y?r:r.concat(s)}var jb=Qm,Xm=Km;const kb=jb,Nb=x.forwardRef(({className:n,sideOffset:r=4,...s},a)=>l.jsx(Xm,{ref:a,sideOffset:r,className:Se("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...s}));Nb.displayName=Xm.displayName;var Ya=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Xa=typeof window>"u"||"Deno"in globalThis;function cn(){}function Sb(n,r){return typeof n=="function"?n(r):n}function Cb(n){return typeof n=="number"&&n>=0&&n!==1/0}function Eb(n,r){return Math.max(n+(r||0)-Date.now(),0)}function dc(n,r){return typeof n=="function"?n(r):n}function Pb(n,r){return typeof n=="function"?n(r):n}function th(n,r){const{type:s="all",exact:a,fetchStatus:c,predicate:f,queryKey:p,stale:h}=n;if(p){if(a){if(r.queryHash!==Ic(p,r.options))return!1}else if(!ti(r.queryKey,p))return!1}if(s!=="all"){const m=r.isActive();if(s==="active"&&!m||s==="inactive"&&m)return!1}return!(typeof h=="boolean"&&r.isStale()!==h||c&&c!==r.state.fetchStatus||f&&!f(r))}function nh(n,r){const{exact:s,status:a,predicate:c,mutationKey:f}=n;if(f){if(!r.options.mutationKey)return!1;if(s){if(ei(r.options.mutationKey)!==ei(f))return!1}else if(!ti(r.options.mutationKey,f))return!1}return!(a&&r.state.status!==a||c&&!c(r))}function Ic(n,r){return((r==null?void 0:r.queryKeyHashFn)||ei)(n)}function ei(n){return JSON.stringify(n,(r,s)=>fc(s)?Object.keys(s).sort().reduce((a,c)=>(a[c]=s[c],a),{}):s)}function ti(n,r){return n===r?!0:typeof n!=typeof r?!1:n&&r&&typeof n=="object"&&typeof r=="object"?Object.keys(r).every(s=>ti(n[s],r[s])):!1}function Jm(n,r){if(n===r)return n;const s=rh(n)&&rh(r);if(s||fc(n)&&fc(r)){const a=s?n:Object.keys(n),c=a.length,f=s?r:Object.keys(r),p=f.length,h=s?[]:{},m=new Set(a);let v=0;for(let w=0;w<p;w++){const b=s?w:f[w];(!s&&m.has(b)||s)&&n[b]===void 0&&r[b]===void 0?(h[b]=void 0,v++):(h[b]=Jm(n[b],r[b]),h[b]===n[b]&&n[b]!==void 0&&v++)}return c===p&&v===c?n:h}return r}function rh(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function fc(n){if(!oh(n))return!1;const r=n.constructor;if(r===void 0)return!0;const s=r.prototype;return!(!oh(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function oh(n){return Object.prototype.toString.call(n)==="[object Object]"}function Tb(n){return new Promise(r=>{setTimeout(r,n)})}function Rb(n,r,s){return typeof s.structuralSharing=="function"?s.structuralSharing(n,r):s.structuralSharing!==!1?Jm(n,r):r}function _b(n,r,s=0){const a=[...n,r];return s&&a.length>s?a.slice(1):a}function Ab(n,r,s=0){const a=[r,...n];return s&&a.length>s?a.slice(0,-1):a}var Fc=Symbol();function Zm(n,r){return!n.queryFn&&(r!=null&&r.initialPromise)?()=>r.initialPromise:!n.queryFn||n.queryFn===Fc?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}var ro,Tr,Wo,bh,Ob=(bh=class extends Ya{constructor(){super();_e(this,ro);_e(this,Tr);_e(this,Wo);we(this,Wo,r=>{if(!Xa&&window.addEventListener){const s=()=>r();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){B(this,Tr)||this.setEventListener(B(this,Wo))}onUnsubscribe(){var r;this.hasListeners()||((r=B(this,Tr))==null||r.call(this),we(this,Tr,void 0))}setEventListener(r){var s;we(this,Wo,r),(s=B(this,Tr))==null||s.call(this),we(this,Tr,r(a=>{typeof a=="boolean"?this.setFocused(a):this.onFocus()}))}setFocused(r){B(this,ro)!==r&&(we(this,ro,r),this.onFocus())}onFocus(){const r=this.isFocused();this.listeners.forEach(s=>{s(r)})}isFocused(){var r;return typeof B(this,ro)=="boolean"?B(this,ro):((r=globalThis.document)==null?void 0:r.visibilityState)!=="hidden"}},ro=new WeakMap,Tr=new WeakMap,Wo=new WeakMap,bh),eg=new Ob,Qo,Rr,qo,jh,Mb=(jh=class extends Ya{constructor(){super();_e(this,Qo,!0);_e(this,Rr);_e(this,qo);we(this,qo,r=>{if(!Xa&&window.addEventListener){const s=()=>r(!0),a=()=>r(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",a)}}})}onSubscribe(){B(this,Rr)||this.setEventListener(B(this,qo))}onUnsubscribe(){var r;this.hasListeners()||((r=B(this,Rr))==null||r.call(this),we(this,Rr,void 0))}setEventListener(r){var s;we(this,qo,r),(s=B(this,Rr))==null||s.call(this),we(this,Rr,r(this.setOnline.bind(this)))}setOnline(r){B(this,Qo)!==r&&(we(this,Qo,r),this.listeners.forEach(a=>{a(r)}))}isOnline(){return B(this,Qo)}},Qo=new WeakMap,Rr=new WeakMap,qo=new WeakMap,jh),Ua=new Mb;function Db(){let n,r;const s=new Promise((c,f)=>{n=c,r=f});s.status="pending",s.catch(()=>{});function a(c){Object.assign(s,c),delete s.resolve,delete s.reject}return s.resolve=c=>{a({status:"fulfilled",value:c}),n(c)},s.reject=c=>{a({status:"rejected",reason:c}),r(c)},s}function Ib(n){return Math.min(1e3*2**n,3e4)}function tg(n){return(n??"online")==="online"?Ua.isOnline():!0}var ng=class extends Error{constructor(n){super("CancelledError"),this.revert=n==null?void 0:n.revert,this.silent=n==null?void 0:n.silent}};function Xu(n){return n instanceof ng}function rg(n){let r=!1,s=0,a=!1,c;const f=Db(),p=k=>{var j;a||(N(new ng(k)),(j=n.abort)==null||j.call(n))},h=()=>{r=!0},m=()=>{r=!1},v=()=>eg.isFocused()&&(n.networkMode==="always"||Ua.isOnline())&&n.canRun(),w=()=>tg(n.networkMode)&&n.canRun(),b=k=>{var j;a||(a=!0,(j=n.onSuccess)==null||j.call(n,k),c==null||c(),f.resolve(k))},N=k=>{var j;a||(a=!0,(j=n.onError)==null||j.call(n,k),c==null||c(),f.reject(k))},y=()=>new Promise(k=>{var j;c=_=>{(a||v())&&k(_)},(j=n.onPause)==null||j.call(n)}).then(()=>{var k;c=void 0,a||(k=n.onContinue)==null||k.call(n)}),P=()=>{if(a)return;let k;const j=s===0?n.initialPromise:void 0;try{k=j??n.fn()}catch(_){k=Promise.reject(_)}Promise.resolve(k).then(b).catch(_=>{var F;if(a)return;const E=n.retry??(Xa?0:3),A=n.retryDelay??Ib,z=typeof A=="function"?A(s,_):A,O=E===!0||typeof E=="number"&&s<E||typeof E=="function"&&E(s,_);if(r||!O){N(_);return}s++,(F=n.onFail)==null||F.call(n,s,_),Tb(z).then(()=>v()?void 0:y()).then(()=>{r?N(_):P()})})};return{promise:f,cancel:p,continue:()=>(c==null||c(),f),cancelRetry:h,continueRetry:m,canStart:w,start:()=>(w()?P():y().then(P),f)}}var Fb=n=>setTimeout(n,0);function Lb(){let n=[],r=0,s=h=>{h()},a=h=>{h()},c=Fb;const f=h=>{r?n.push(h):c(()=>{s(h)})},p=()=>{const h=n;n=[],h.length&&c(()=>{a(()=>{h.forEach(m=>{s(m)})})})};return{batch:h=>{let m;r++;try{m=h()}finally{r--,r||p()}return m},batchCalls:h=>(...m)=>{f(()=>{h(...m)})},schedule:f,setNotifyFunction:h=>{s=h},setBatchNotifyFunction:h=>{a=h},setScheduler:h=>{c=h}}}var bt=Lb(),oo,kh,og=(kh=class{constructor(){_e(this,oo)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Cb(this.gcTime)&&we(this,oo,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(Xa?1/0:5*60*1e3))}clearGcTimeout(){B(this,oo)&&(clearTimeout(B(this,oo)),we(this,oo,void 0))}},oo=new WeakMap,kh),Ko,so,qt,io,mt,oi,ao,dn,Yn,Nh,zb=(Nh=class extends og{constructor(r){super();_e(this,dn);_e(this,Ko);_e(this,so);_e(this,qt);_e(this,io);_e(this,mt);_e(this,oi);_e(this,ao);we(this,ao,!1),we(this,oi,r.defaultOptions),this.setOptions(r.options),this.observers=[],we(this,io,r.client),we(this,qt,B(this,io).getQueryCache()),this.queryKey=r.queryKey,this.queryHash=r.queryHash,we(this,Ko,$b(this.options)),this.state=r.state??B(this,Ko),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var r;return(r=B(this,mt))==null?void 0:r.promise}setOptions(r){this.options={...B(this,oi),...r},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&B(this,qt).remove(this)}setData(r,s){const a=Rb(this.state.data,r,this.options);return ht(this,dn,Yn).call(this,{data:a,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),a}setState(r,s){ht(this,dn,Yn).call(this,{type:"setState",state:r,setStateOptions:s})}cancel(r){var a,c;const s=(a=B(this,mt))==null?void 0:a.promise;return(c=B(this,mt))==null||c.cancel(r),s?s.then(cn).catch(cn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(B(this,Ko))}isActive(){return this.observers.some(r=>Pb(r.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Fc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(r=>dc(r.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(r=>r.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(r=0){return this.state.data===void 0?!0:r==="static"?!1:this.state.isInvalidated?!0:!Eb(this.state.dataUpdatedAt,r)}onFocus(){var s;const r=this.observers.find(a=>a.shouldFetchOnWindowFocus());r==null||r.refetch({cancelRefetch:!1}),(s=B(this,mt))==null||s.continue()}onOnline(){var s;const r=this.observers.find(a=>a.shouldFetchOnReconnect());r==null||r.refetch({cancelRefetch:!1}),(s=B(this,mt))==null||s.continue()}addObserver(r){this.observers.includes(r)||(this.observers.push(r),this.clearGcTimeout(),B(this,qt).notify({type:"observerAdded",query:this,observer:r}))}removeObserver(r){this.observers.includes(r)&&(this.observers=this.observers.filter(s=>s!==r),this.observers.length||(B(this,mt)&&(B(this,ao)?B(this,mt).cancel({revert:!0}):B(this,mt).cancelRetry()),this.scheduleGc()),B(this,qt).notify({type:"observerRemoved",query:this,observer:r}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||ht(this,dn,Yn).call(this,{type:"invalidate"})}fetch(r,s){var v,w,b;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(B(this,mt))return B(this,mt).continueRetry(),B(this,mt).promise}if(r&&this.setOptions(r),!this.options.queryFn){const N=this.observers.find(y=>y.options.queryFn);N&&this.setOptions(N.options)}const a=new AbortController,c=N=>{Object.defineProperty(N,"signal",{enumerable:!0,get:()=>(we(this,ao,!0),a.signal)})},f=()=>{const N=Zm(this.options,s),P=(()=>{const k={client:B(this,io),queryKey:this.queryKey,meta:this.meta};return c(k),k})();return we(this,ao,!1),this.options.persister?this.options.persister(N,P,this):N(P)},h=(()=>{const N={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:B(this,io),state:this.state,fetchFn:f};return c(N),N})();(v=this.options.behavior)==null||v.onFetch(h,this),we(this,so,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((w=h.fetchOptions)==null?void 0:w.meta))&&ht(this,dn,Yn).call(this,{type:"fetch",meta:(b=h.fetchOptions)==null?void 0:b.meta});const m=N=>{var y,P,k,j;Xu(N)&&N.silent||ht(this,dn,Yn).call(this,{type:"error",error:N}),Xu(N)||((P=(y=B(this,qt).config).onError)==null||P.call(y,N,this),(j=(k=B(this,qt).config).onSettled)==null||j.call(k,this.state.data,N,this)),this.scheduleGc()};return we(this,mt,rg({initialPromise:s==null?void 0:s.initialPromise,fn:h.fetchFn,abort:a.abort.bind(a),onSuccess:N=>{var y,P,k,j;if(N===void 0){m(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(N)}catch(_){m(_);return}(P=(y=B(this,qt).config).onSuccess)==null||P.call(y,N,this),(j=(k=B(this,qt).config).onSettled)==null||j.call(k,N,this.state.error,this),this.scheduleGc()},onError:m,onFail:(N,y)=>{ht(this,dn,Yn).call(this,{type:"failed",failureCount:N,error:y})},onPause:()=>{ht(this,dn,Yn).call(this,{type:"pause"})},onContinue:()=>{ht(this,dn,Yn).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),B(this,mt).start()}},Ko=new WeakMap,so=new WeakMap,qt=new WeakMap,io=new WeakMap,mt=new WeakMap,oi=new WeakMap,ao=new WeakMap,dn=new WeakSet,Yn=function(r){const s=a=>{switch(r.type){case"failed":return{...a,fetchFailureCount:r.failureCount,fetchFailureReason:r.error};case"pause":return{...a,fetchStatus:"paused"};case"continue":return{...a,fetchStatus:"fetching"};case"fetch":return{...a,...Ub(a.data,this.options),fetchMeta:r.meta??null};case"success":return we(this,so,void 0),{...a,data:r.data,dataUpdateCount:a.dataUpdateCount+1,dataUpdatedAt:r.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!r.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const c=r.error;return Xu(c)&&c.revert&&B(this,so)?{...B(this,so),fetchStatus:"idle"}:{...a,error:c,errorUpdateCount:a.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:a.fetchFailureCount+1,fetchFailureReason:c,fetchStatus:"idle",status:"error"};case"invalidate":return{...a,isInvalidated:!0};case"setState":return{...a,...r.state}}};this.state=s(this.state),bt.batch(()=>{this.observers.forEach(a=>{a.onQueryUpdate()}),B(this,qt).notify({query:this,type:"updated",action:r})})},Nh);function Ub(n,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:tg(r.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function $b(n){const r=typeof n.initialData=="function"?n.initialData():n.initialData,s=r!==void 0,a=s?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:s?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var Rn,Sh,Bb=(Sh=class extends Ya{constructor(r={}){super();_e(this,Rn);this.config=r,we(this,Rn,new Map)}build(r,s,a){const c=s.queryKey,f=s.queryHash??Ic(c,s);let p=this.get(f);return p||(p=new zb({client:r,queryKey:c,queryHash:f,options:r.defaultQueryOptions(s),state:a,defaultOptions:r.getQueryDefaults(c)}),this.add(p)),p}add(r){B(this,Rn).has(r.queryHash)||(B(this,Rn).set(r.queryHash,r),this.notify({type:"added",query:r}))}remove(r){const s=B(this,Rn).get(r.queryHash);s&&(r.destroy(),s===r&&B(this,Rn).delete(r.queryHash),this.notify({type:"removed",query:r}))}clear(){bt.batch(()=>{this.getAll().forEach(r=>{this.remove(r)})})}get(r){return B(this,Rn).get(r)}getAll(){return[...B(this,Rn).values()]}find(r){const s={exact:!0,...r};return this.getAll().find(a=>th(s,a))}findAll(r={}){const s=this.getAll();return Object.keys(r).length>0?s.filter(a=>th(r,a)):s}notify(r){bt.batch(()=>{this.listeners.forEach(s=>{s(r)})})}onFocus(){bt.batch(()=>{this.getAll().forEach(r=>{r.onFocus()})})}onOnline(){bt.batch(()=>{this.getAll().forEach(r=>{r.onOnline()})})}},Rn=new WeakMap,Sh),_n,wt,lo,An,Pr,Ch,Vb=(Ch=class extends og{constructor(r){super();_e(this,An);_e(this,_n);_e(this,wt);_e(this,lo);this.mutationId=r.mutationId,we(this,wt,r.mutationCache),we(this,_n,[]),this.state=r.state||Hb(),this.setOptions(r.options),this.scheduleGc()}setOptions(r){this.options=r,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(r){B(this,_n).includes(r)||(B(this,_n).push(r),this.clearGcTimeout(),B(this,wt).notify({type:"observerAdded",mutation:this,observer:r}))}removeObserver(r){we(this,_n,B(this,_n).filter(s=>s!==r)),this.scheduleGc(),B(this,wt).notify({type:"observerRemoved",mutation:this,observer:r})}optionalRemove(){B(this,_n).length||(this.state.status==="pending"?this.scheduleGc():B(this,wt).remove(this))}continue(){var r;return((r=B(this,lo))==null?void 0:r.continue())??this.execute(this.state.variables)}async execute(r){var f,p,h,m,v,w,b,N,y,P,k,j,_,E,A,z,O,F,$,G;const s=()=>{ht(this,An,Pr).call(this,{type:"continue"})};we(this,lo,rg({fn:()=>this.options.mutationFn?this.options.mutationFn(r):Promise.reject(new Error("No mutationFn found")),onFail:(J,Z)=>{ht(this,An,Pr).call(this,{type:"failed",failureCount:J,error:Z})},onPause:()=>{ht(this,An,Pr).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>B(this,wt).canRun(this)}));const a=this.state.status==="pending",c=!B(this,lo).canStart();try{if(a)s();else{ht(this,An,Pr).call(this,{type:"pending",variables:r,isPaused:c}),await((p=(f=B(this,wt).config).onMutate)==null?void 0:p.call(f,r,this));const Z=await((m=(h=this.options).onMutate)==null?void 0:m.call(h,r));Z!==this.state.context&&ht(this,An,Pr).call(this,{type:"pending",context:Z,variables:r,isPaused:c})}const J=await B(this,lo).start();return await((w=(v=B(this,wt).config).onSuccess)==null?void 0:w.call(v,J,r,this.state.context,this)),await((N=(b=this.options).onSuccess)==null?void 0:N.call(b,J,r,this.state.context)),await((P=(y=B(this,wt).config).onSettled)==null?void 0:P.call(y,J,null,this.state.variables,this.state.context,this)),await((j=(k=this.options).onSettled)==null?void 0:j.call(k,J,null,r,this.state.context)),ht(this,An,Pr).call(this,{type:"success",data:J}),J}catch(J){try{throw await((E=(_=B(this,wt).config).onError)==null?void 0:E.call(_,J,r,this.state.context,this)),await((z=(A=this.options).onError)==null?void 0:z.call(A,J,r,this.state.context)),await((F=(O=B(this,wt).config).onSettled)==null?void 0:F.call(O,void 0,J,this.state.variables,this.state.context,this)),await((G=($=this.options).onSettled)==null?void 0:G.call($,void 0,J,r,this.state.context)),J}finally{ht(this,An,Pr).call(this,{type:"error",error:J})}}finally{B(this,wt).runNext(this)}}},_n=new WeakMap,wt=new WeakMap,lo=new WeakMap,An=new WeakSet,Pr=function(r){const s=a=>{switch(r.type){case"failed":return{...a,failureCount:r.failureCount,failureReason:r.error};case"pause":return{...a,isPaused:!0};case"continue":return{...a,isPaused:!1};case"pending":return{...a,context:r.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:r.isPaused,status:"pending",variables:r.variables,submittedAt:Date.now()};case"success":return{...a,data:r.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...a,data:void 0,error:r.error,failureCount:a.failureCount+1,failureReason:r.error,isPaused:!1,status:"error"}}};this.state=s(this.state),bt.batch(()=>{B(this,_n).forEach(a=>{a.onMutationUpdate(r)}),B(this,wt).notify({mutation:this,type:"updated",action:r})})},Ch);function Hb(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Xn,fn,si,Eh,Wb=(Eh=class extends Ya{constructor(r={}){super();_e(this,Xn);_e(this,fn);_e(this,si);this.config=r,we(this,Xn,new Set),we(this,fn,new Map),we(this,si,0)}build(r,s,a){const c=new Vb({mutationCache:this,mutationId:++Na(this,si)._,options:r.defaultMutationOptions(s),state:a});return this.add(c),c}add(r){B(this,Xn).add(r);const s=Ra(r);if(typeof s=="string"){const a=B(this,fn).get(s);a?a.push(r):B(this,fn).set(s,[r])}this.notify({type:"added",mutation:r})}remove(r){if(B(this,Xn).delete(r)){const s=Ra(r);if(typeof s=="string"){const a=B(this,fn).get(s);if(a)if(a.length>1){const c=a.indexOf(r);c!==-1&&a.splice(c,1)}else a[0]===r&&B(this,fn).delete(s)}}this.notify({type:"removed",mutation:r})}canRun(r){const s=Ra(r);if(typeof s=="string"){const a=B(this,fn).get(s),c=a==null?void 0:a.find(f=>f.state.status==="pending");return!c||c===r}else return!0}runNext(r){var a;const s=Ra(r);if(typeof s=="string"){const c=(a=B(this,fn).get(s))==null?void 0:a.find(f=>f!==r&&f.state.isPaused);return(c==null?void 0:c.continue())??Promise.resolve()}else return Promise.resolve()}clear(){bt.batch(()=>{B(this,Xn).forEach(r=>{this.notify({type:"removed",mutation:r})}),B(this,Xn).clear(),B(this,fn).clear()})}getAll(){return Array.from(B(this,Xn))}find(r){const s={exact:!0,...r};return this.getAll().find(a=>nh(s,a))}findAll(r={}){return this.getAll().filter(s=>nh(r,s))}notify(r){bt.batch(()=>{this.listeners.forEach(s=>{s(r)})})}resumePausedMutations(){const r=this.getAll().filter(s=>s.state.isPaused);return bt.batch(()=>Promise.all(r.map(s=>s.continue().catch(cn))))}},Xn=new WeakMap,fn=new WeakMap,si=new WeakMap,Eh);function Ra(n){var r;return(r=n.options.scope)==null?void 0:r.id}function sh(n){return{onFetch:(r,s)=>{var w,b,N,y,P;const a=r.options,c=(N=(b=(w=r.fetchOptions)==null?void 0:w.meta)==null?void 0:b.fetchMore)==null?void 0:N.direction,f=((y=r.state.data)==null?void 0:y.pages)||[],p=((P=r.state.data)==null?void 0:P.pageParams)||[];let h={pages:[],pageParams:[]},m=0;const v=async()=>{let k=!1;const j=A=>{Object.defineProperty(A,"signal",{enumerable:!0,get:()=>(r.signal.aborted?k=!0:r.signal.addEventListener("abort",()=>{k=!0}),r.signal)})},_=Zm(r.options,r.fetchOptions),E=async(A,z,O)=>{if(k)return Promise.reject();if(z==null&&A.pages.length)return Promise.resolve(A);const $=(()=>{const pe={client:r.client,queryKey:r.queryKey,pageParam:z,direction:O?"backward":"forward",meta:r.options.meta};return j(pe),pe})(),G=await _($),{maxPages:J}=r.options,Z=O?Ab:_b;return{pages:Z(A.pages,G,J),pageParams:Z(A.pageParams,z,J)}};if(c&&f.length){const A=c==="backward",z=A?Qb:ih,O={pages:f,pageParams:p},F=z(a,O);h=await E(O,F,A)}else{const A=n??f.length;do{const z=m===0?p[0]??a.initialPageParam:ih(a,h);if(m>0&&z==null)break;h=await E(h,z),m++}while(m<A)}return h};r.options.persister?r.fetchFn=()=>{var k,j;return(j=(k=r.options).persister)==null?void 0:j.call(k,v,{client:r.client,queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},s)}:r.fetchFn=v}}}function ih(n,{pages:r,pageParams:s}){const a=r.length-1;return r.length>0?n.getNextPageParam(r[a],r,s[a],s):void 0}function Qb(n,{pages:r,pageParams:s}){var a;return r.length>0?(a=n.getPreviousPageParam)==null?void 0:a.call(n,r[0],r,s[0],s):void 0}var Qe,_r,Ar,Go,Yo,Or,Xo,Jo,Ph,qb=(Ph=class{constructor(n={}){_e(this,Qe);_e(this,_r);_e(this,Ar);_e(this,Go);_e(this,Yo);_e(this,Or);_e(this,Xo);_e(this,Jo);we(this,Qe,n.queryCache||new Bb),we(this,_r,n.mutationCache||new Wb),we(this,Ar,n.defaultOptions||{}),we(this,Go,new Map),we(this,Yo,new Map),we(this,Or,0)}mount(){Na(this,Or)._++,B(this,Or)===1&&(we(this,Xo,eg.subscribe(async n=>{n&&(await this.resumePausedMutations(),B(this,Qe).onFocus())})),we(this,Jo,Ua.subscribe(async n=>{n&&(await this.resumePausedMutations(),B(this,Qe).onOnline())})))}unmount(){var n,r;Na(this,Or)._--,B(this,Or)===0&&((n=B(this,Xo))==null||n.call(this),we(this,Xo,void 0),(r=B(this,Jo))==null||r.call(this),we(this,Jo,void 0))}isFetching(n){return B(this,Qe).findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return B(this,_r).findAll({...n,status:"pending"}).length}getQueryData(n){var s;const r=this.defaultQueryOptions({queryKey:n});return(s=B(this,Qe).get(r.queryHash))==null?void 0:s.state.data}ensureQueryData(n){const r=this.defaultQueryOptions(n),s=B(this,Qe).build(this,r),a=s.state.data;return a===void 0?this.fetchQuery(n):(n.revalidateIfStale&&s.isStaleByTime(dc(r.staleTime,s))&&this.prefetchQuery(r),Promise.resolve(a))}getQueriesData(n){return B(this,Qe).findAll(n).map(({queryKey:r,state:s})=>{const a=s.data;return[r,a]})}setQueryData(n,r,s){const a=this.defaultQueryOptions({queryKey:n}),c=B(this,Qe).get(a.queryHash),f=c==null?void 0:c.state.data,p=Sb(r,f);if(p!==void 0)return B(this,Qe).build(this,a).setData(p,{...s,manual:!0})}setQueriesData(n,r,s){return bt.batch(()=>B(this,Qe).findAll(n).map(({queryKey:a})=>[a,this.setQueryData(a,r,s)]))}getQueryState(n){var s;const r=this.defaultQueryOptions({queryKey:n});return(s=B(this,Qe).get(r.queryHash))==null?void 0:s.state}removeQueries(n){const r=B(this,Qe);bt.batch(()=>{r.findAll(n).forEach(s=>{r.remove(s)})})}resetQueries(n,r){const s=B(this,Qe);return bt.batch(()=>(s.findAll(n).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...n},r)))}cancelQueries(n,r={}){const s={revert:!0,...r},a=bt.batch(()=>B(this,Qe).findAll(n).map(c=>c.cancel(s)));return Promise.all(a).then(cn).catch(cn)}invalidateQueries(n,r={}){return bt.batch(()=>(B(this,Qe).findAll(n).forEach(s=>{s.invalidate()}),(n==null?void 0:n.refetchType)==="none"?Promise.resolve():this.refetchQueries({...n,type:(n==null?void 0:n.refetchType)??(n==null?void 0:n.type)??"active"},r)))}refetchQueries(n,r={}){const s={...r,cancelRefetch:r.cancelRefetch??!0},a=bt.batch(()=>B(this,Qe).findAll(n).filter(c=>!c.isDisabled()&&!c.isStatic()).map(c=>{let f=c.fetch(void 0,s);return s.throwOnError||(f=f.catch(cn)),c.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(a).then(cn)}fetchQuery(n){const r=this.defaultQueryOptions(n);r.retry===void 0&&(r.retry=!1);const s=B(this,Qe).build(this,r);return s.isStaleByTime(dc(r.staleTime,s))?s.fetch(r):Promise.resolve(s.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(cn).catch(cn)}fetchInfiniteQuery(n){return n.behavior=sh(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(cn).catch(cn)}ensureInfiniteQueryData(n){return n.behavior=sh(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return Ua.isOnline()?B(this,_r).resumePausedMutations():Promise.resolve()}getQueryCache(){return B(this,Qe)}getMutationCache(){return B(this,_r)}getDefaultOptions(){return B(this,Ar)}setDefaultOptions(n){we(this,Ar,n)}setQueryDefaults(n,r){B(this,Go).set(ei(n),{queryKey:n,defaultOptions:r})}getQueryDefaults(n){const r=[...B(this,Go).values()],s={};return r.forEach(a=>{ti(n,a.queryKey)&&Object.assign(s,a.defaultOptions)}),s}setMutationDefaults(n,r){B(this,Yo).set(ei(n),{mutationKey:n,defaultOptions:r})}getMutationDefaults(n){const r=[...B(this,Yo).values()],s={};return r.forEach(a=>{ti(n,a.mutationKey)&&Object.assign(s,a.defaultOptions)}),s}defaultQueryOptions(n){if(n._defaulted)return n;const r={...B(this,Ar).queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return r.queryHash||(r.queryHash=Ic(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.queryFn===Fc&&(r.enabled=!1),r}defaultMutationOptions(n){return n!=null&&n._defaulted?n:{...B(this,Ar).mutations,...(n==null?void 0:n.mutationKey)&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){B(this,Qe).clear(),B(this,_r).clear()}},Qe=new WeakMap,_r=new WeakMap,Ar=new WeakMap,Go=new WeakMap,Yo=new WeakMap,Or=new WeakMap,Xo=new WeakMap,Jo=new WeakMap,Ph),Kb=x.createContext(void 0),Gb=({client:n,children:r})=>(x.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),l.jsx(Kb.Provider,{value:n,children:r}));/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ni(){return ni=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(n[a]=s[a])}return n},ni.apply(this,arguments)}var Mr;(function(n){n.Pop="POP",n.Push="PUSH",n.Replace="REPLACE"})(Mr||(Mr={}));const ah="popstate";function Yb(n){n===void 0&&(n={});function r(c,f){let{pathname:p="/",search:h="",hash:m=""}=fo(c.location.hash.substr(1));return!p.startsWith("/")&&!p.startsWith(".")&&(p="/"+p),pc("",{pathname:p,search:h,hash:m},f.state&&f.state.usr||null,f.state&&f.state.key||"default")}function s(c,f){let p=c.document.querySelector("base"),h="";if(p&&p.getAttribute("href")){let m=c.location.href,v=m.indexOf("#");h=v===-1?m:m.slice(0,v)}return h+"#"+(typeof f=="string"?f:sg(f))}function a(c,f){Lc(c.pathname.charAt(0)==="/","relative pathnames are not supported in hash history.push("+JSON.stringify(f)+")")}return Jb(r,s,a,n)}function nt(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function Lc(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Xb(){return Math.random().toString(36).substr(2,8)}function lh(n,r){return{usr:n.state,key:n.key,idx:r}}function pc(n,r,s,a){return s===void 0&&(s=null),ni({pathname:typeof n=="string"?n:n.pathname,search:"",hash:""},typeof r=="string"?fo(r):r,{state:s,key:r&&r.key||a||Xb()})}function sg(n){let{pathname:r="/",search:s="",hash:a=""}=n;return s&&s!=="?"&&(r+=s.charAt(0)==="?"?s:"?"+s),a&&a!=="#"&&(r+=a.charAt(0)==="#"?a:"#"+a),r}function fo(n){let r={};if(n){let s=n.indexOf("#");s>=0&&(r.hash=n.substr(s),n=n.substr(0,s));let a=n.indexOf("?");a>=0&&(r.search=n.substr(a),n=n.substr(0,a)),n&&(r.pathname=n)}return r}function Jb(n,r,s,a){a===void 0&&(a={});let{window:c=document.defaultView,v5Compat:f=!1}=a,p=c.history,h=Mr.Pop,m=null,v=w();v==null&&(v=0,p.replaceState(ni({},p.state,{idx:v}),""));function w(){return(p.state||{idx:null}).idx}function b(){h=Mr.Pop;let j=w(),_=j==null?null:j-v;v=j,m&&m({action:h,location:k.location,delta:_})}function N(j,_){h=Mr.Push;let E=pc(k.location,j,_);s&&s(E,j),v=w()+1;let A=lh(E,v),z=k.createHref(E);try{p.pushState(A,"",z)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;c.location.assign(z)}f&&m&&m({action:h,location:k.location,delta:1})}function y(j,_){h=Mr.Replace;let E=pc(k.location,j,_);s&&s(E,j),v=w();let A=lh(E,v),z=k.createHref(E);p.replaceState(A,"",z),f&&m&&m({action:h,location:k.location,delta:0})}function P(j){let _=c.location.origin!=="null"?c.location.origin:c.location.href,E=typeof j=="string"?j:sg(j);return E=E.replace(/ $/,"%20"),nt(_,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,_)}let k={get action(){return h},get location(){return n(c,p)},listen(j){if(m)throw new Error("A history only accepts one active listener");return c.addEventListener(ah,b),m=j,()=>{c.removeEventListener(ah,b),m=null}},createHref(j){return r(c,j)},createURL:P,encodeLocation(j){let _=P(j);return{pathname:_.pathname,search:_.search,hash:_.hash}},push:N,replace:y,go(j){return p.go(j)}};return k}var uh;(function(n){n.data="data",n.deferred="deferred",n.redirect="redirect",n.error="error"})(uh||(uh={}));function Zb(n,r,s){return s===void 0&&(s="/"),ej(n,r,s)}function ej(n,r,s,a){let c=typeof r=="string"?fo(r):r,f=lg(c.pathname||"/",s);if(f==null)return null;let p=ig(n);tj(p);let h=null;for(let m=0;h==null&&m<p.length;++m){let v=pj(f);h=cj(p[m],v)}return h}function ig(n,r,s,a){r===void 0&&(r=[]),s===void 0&&(s=[]),a===void 0&&(a="");let c=(f,p,h)=>{let m={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:p,route:f};m.relativePath.startsWith("/")&&(nt(m.relativePath.startsWith(a),'Absolute route path "'+m.relativePath+'" nested under path '+('"'+a+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),m.relativePath=m.relativePath.slice(a.length));let v=uo([a,m.relativePath]),w=s.concat(m);f.children&&f.children.length>0&&(nt(f.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+v+'".')),ig(f.children,r,w,v)),!(f.path==null&&!f.index)&&r.push({path:v,score:lj(v,f.index),routesMeta:w})};return n.forEach((f,p)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))c(f,p);else for(let m of ag(f.path))c(f,p,m)}),r}function ag(n){let r=n.split("/");if(r.length===0)return[];let[s,...a]=r,c=s.endsWith("?"),f=s.replace(/\?$/,"");if(a.length===0)return c?[f,""]:[f];let p=ag(a.join("/")),h=[];return h.push(...p.map(m=>m===""?f:[f,m].join("/"))),c&&h.push(...p),h.map(m=>n.startsWith("/")&&m===""?"/":m)}function tj(n){n.sort((r,s)=>r.score!==s.score?s.score-r.score:uj(r.routesMeta.map(a=>a.childrenIndex),s.routesMeta.map(a=>a.childrenIndex)))}const nj=/^:[\w-]+$/,rj=3,oj=2,sj=1,ij=10,aj=-2,ch=n=>n==="*";function lj(n,r){let s=n.split("/"),a=s.length;return s.some(ch)&&(a+=aj),r&&(a+=oj),s.filter(c=>!ch(c)).reduce((c,f)=>c+(nj.test(f)?rj:f===""?sj:ij),a)}function uj(n,r){return n.length===r.length&&n.slice(0,-1).every((a,c)=>a===r[c])?n[n.length-1]-r[r.length-1]:0}function cj(n,r,s){let{routesMeta:a}=n,c={},f="/",p=[];for(let h=0;h<a.length;++h){let m=a[h],v=h===a.length-1,w=f==="/"?r:r.slice(f.length)||"/",b=dj({path:m.relativePath,caseSensitive:m.caseSensitive,end:v},w),N=m.route;if(!b)return null;Object.assign(c,b.params),p.push({params:c,pathname:uo([f,b.pathname]),pathnameBase:xj(uo([f,b.pathnameBase])),route:N}),b.pathnameBase!=="/"&&(f=uo([f,b.pathnameBase]))}return p}function dj(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[s,a]=fj(n.path,n.caseSensitive,n.end),c=r.match(s);if(!c)return null;let f=c[0],p=f.replace(/(.)\/+$/,"$1"),h=c.slice(1);return{params:a.reduce((v,w,b)=>{let{paramName:N,isOptional:y}=w;if(N==="*"){let k=h[b]||"";p=f.slice(0,f.length-k.length).replace(/(.)\/+$/,"$1")}const P=h[b];return y&&!P?v[N]=void 0:v[N]=(P||"").replace(/%2F/g,"/"),v},{}),pathname:f,pathnameBase:p,pattern:n}}function fj(n,r,s){r===void 0&&(r=!1),s===void 0&&(s=!0),Lc(n==="*"||!n.endsWith("*")||n.endsWith("/*"),'Route path "'+n+'" will be treated as if it were '+('"'+n.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+n.replace(/\*$/,"/*")+'".'));let a=[],c="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(p,h,m)=>(a.push({paramName:h,isOptional:m!=null}),m?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(a.push({paramName:"*"}),c+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?c+="\\/*$":n!==""&&n!=="/"&&(c+="(?:(?=\\/|$))"),[new RegExp(c,r?void 0:"i"),a]}function pj(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return Lc(!1,'The URL path "'+n+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),n}}function lg(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let s=r.endsWith("/")?r.length-1:r.length,a=n.charAt(s);return a&&a!=="/"?null:n.slice(s)||"/"}function hj(n,r){r===void 0&&(r="/");let{pathname:s,search:a="",hash:c=""}=typeof n=="string"?fo(n):n;return{pathname:s?s.startsWith("/")?s:mj(s,r):r,search:wj(a),hash:bj(c)}}function mj(n,r){let s=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(c=>{c===".."?s.length>1&&s.pop():c!=="."&&s.push(c)}),s.length>1?s.join("/"):"/"}function Ju(n,r,s,a){return"Cannot include a '"+n+"' character in a manually specified "+("`to."+r+"` field ["+JSON.stringify(a)+"].  Please separate it out to the ")+("`to."+s+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function gj(n){return n.filter((r,s)=>s===0||r.route.path&&r.route.path.length>0)}function vj(n,r){let s=gj(n);return r?s.map((a,c)=>c===s.length-1?a.pathname:a.pathnameBase):s.map(a=>a.pathnameBase)}function yj(n,r,s,a){a===void 0&&(a=!1);let c;typeof n=="string"?c=fo(n):(c=ni({},n),nt(!c.pathname||!c.pathname.includes("?"),Ju("?","pathname","search",c)),nt(!c.pathname||!c.pathname.includes("#"),Ju("#","pathname","hash",c)),nt(!c.search||!c.search.includes("#"),Ju("#","search","hash",c)));let f=n===""||c.pathname==="",p=f?"/":c.pathname,h;if(p==null)h=s;else{let b=r.length-1;if(!a&&p.startsWith("..")){let N=p.split("/");for(;N[0]==="..";)N.shift(),b-=1;c.pathname=N.join("/")}h=b>=0?r[b]:"/"}let m=hj(c,h),v=p&&p!=="/"&&p.endsWith("/"),w=(f||p===".")&&s.endsWith("/");return!m.pathname.endsWith("/")&&(v||w)&&(m.pathname+="/"),m}const uo=n=>n.join("/").replace(/\/\/+/g,"/"),xj=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),wj=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,bj=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function jj(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}const ug=["post","put","patch","delete"];new Set(ug);const kj=["get",...ug];new Set(kj);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ri(){return ri=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(n[a]=s[a])}return n},ri.apply(this,arguments)}const zc=x.createContext(null),Nj=x.createContext(null),Ja=x.createContext(null),Za=x.createContext(null),ls=x.createContext({outlet:null,matches:[],isDataRoute:!1}),cg=x.createContext(null);function el(){return x.useContext(Za)!=null}function Uc(){return el()||nt(!1),x.useContext(Za).location}function dg(n){x.useContext(Ja).static||x.useLayoutEffect(n)}function Sj(){let{isDataRoute:n}=x.useContext(ls);return n?Lj():Cj()}function Cj(){el()||nt(!1);let n=x.useContext(zc),{basename:r,future:s,navigator:a}=x.useContext(Ja),{matches:c}=x.useContext(ls),{pathname:f}=Uc(),p=JSON.stringify(vj(c,s.v7_relativeSplatPath)),h=x.useRef(!1);return dg(()=>{h.current=!0}),x.useCallback(function(v,w){if(w===void 0&&(w={}),!h.current)return;if(typeof v=="number"){a.go(v);return}let b=yj(v,JSON.parse(p),f,w.relative==="path");n==null&&r!=="/"&&(b.pathname=b.pathname==="/"?r:uo([r,b.pathname])),(w.replace?a.replace:a.push)(b,w.state,w)},[r,a,p,f,n])}function Ej(n,r){return Pj(n,r)}function Pj(n,r,s,a){el()||nt(!1);let{navigator:c}=x.useContext(Ja),{matches:f}=x.useContext(ls),p=f[f.length-1],h=p?p.params:{};p&&p.pathname;let m=p?p.pathnameBase:"/";p&&p.route;let v=Uc(),w;if(r){var b;let j=typeof r=="string"?fo(r):r;m==="/"||(b=j.pathname)!=null&&b.startsWith(m)||nt(!1),w=j}else w=v;let N=w.pathname||"/",y=N;if(m!=="/"){let j=m.replace(/^\//,"").split("/");y="/"+N.replace(/^\//,"").split("/").slice(j.length).join("/")}let P=Zb(n,{pathname:y}),k=Oj(P&&P.map(j=>Object.assign({},j,{params:Object.assign({},h,j.params),pathname:uo([m,c.encodeLocation?c.encodeLocation(j.pathname).pathname:j.pathname]),pathnameBase:j.pathnameBase==="/"?m:uo([m,c.encodeLocation?c.encodeLocation(j.pathnameBase).pathname:j.pathnameBase])})),f,s,a);return r&&k?x.createElement(Za.Provider,{value:{location:ri({pathname:"/",search:"",hash:"",state:null,key:"default"},w),navigationType:Mr.Pop}},k):k}function Tj(){let n=Fj(),r=jj(n)?n.status+" "+n.statusText:n instanceof Error?n.message:JSON.stringify(n),s=n instanceof Error?n.stack:null,c={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},r),s?x.createElement("pre",{style:c},s):null,null)}const Rj=x.createElement(Tj,null);class _j extends x.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,s){return s.location!==r.location||s.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:s.error,location:s.location,revalidation:r.revalidation||s.revalidation}}componentDidCatch(r,s){console.error("React Router caught the following error during render",r,s)}render(){return this.state.error!==void 0?x.createElement(ls.Provider,{value:this.props.routeContext},x.createElement(cg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Aj(n){let{routeContext:r,match:s,children:a}=n,c=x.useContext(zc);return c&&c.static&&c.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=s.route.id),x.createElement(ls.Provider,{value:r},a)}function Oj(n,r,s,a){var c;if(r===void 0&&(r=[]),s===void 0&&(s=null),a===void 0&&(a=null),n==null){var f;if(!s)return null;if(s.errors)n=s.matches;else if((f=a)!=null&&f.v7_partialHydration&&r.length===0&&!s.initialized&&s.matches.length>0)n=s.matches;else return null}let p=n,h=(c=s)==null?void 0:c.errors;if(h!=null){let w=p.findIndex(b=>b.route.id&&(h==null?void 0:h[b.route.id])!==void 0);w>=0||nt(!1),p=p.slice(0,Math.min(p.length,w+1))}let m=!1,v=-1;if(s&&a&&a.v7_partialHydration)for(let w=0;w<p.length;w++){let b=p[w];if((b.route.HydrateFallback||b.route.hydrateFallbackElement)&&(v=w),b.route.id){let{loaderData:N,errors:y}=s,P=b.route.loader&&N[b.route.id]===void 0&&(!y||y[b.route.id]===void 0);if(b.route.lazy||P){m=!0,v>=0?p=p.slice(0,v+1):p=[p[0]];break}}}return p.reduceRight((w,b,N)=>{let y,P=!1,k=null,j=null;s&&(y=h&&b.route.id?h[b.route.id]:void 0,k=b.route.errorElement||Rj,m&&(v<0&&N===0?(zj("route-fallback"),P=!0,j=null):v===N&&(P=!0,j=b.route.hydrateFallbackElement||null)));let _=r.concat(p.slice(0,N+1)),E=()=>{let A;return y?A=k:P?A=j:b.route.Component?A=x.createElement(b.route.Component,null):b.route.element?A=b.route.element:A=w,x.createElement(Aj,{match:b,routeContext:{outlet:w,matches:_,isDataRoute:s!=null},children:A})};return s&&(b.route.ErrorBoundary||b.route.errorElement||N===0)?x.createElement(_j,{location:s.location,revalidation:s.revalidation,component:k,error:y,children:E(),routeContext:{outlet:null,matches:_,isDataRoute:!0}}):E()},null)}var fg=function(n){return n.UseBlocker="useBlocker",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n}(fg||{}),pg=function(n){return n.UseBlocker="useBlocker",n.UseLoaderData="useLoaderData",n.UseActionData="useActionData",n.UseRouteError="useRouteError",n.UseNavigation="useNavigation",n.UseRouteLoaderData="useRouteLoaderData",n.UseMatches="useMatches",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n.UseRouteId="useRouteId",n}(pg||{});function Mj(n){let r=x.useContext(zc);return r||nt(!1),r}function Dj(n){let r=x.useContext(Nj);return r||nt(!1),r}function Ij(n){let r=x.useContext(ls);return r||nt(!1),r}function hg(n){let r=Ij(),s=r.matches[r.matches.length-1];return s.route.id||nt(!1),s.route.id}function Fj(){var n;let r=x.useContext(cg),s=Dj(),a=hg();return r!==void 0?r:(n=s.errors)==null?void 0:n[a]}function Lj(){let{router:n}=Mj(fg.UseNavigateStable),r=hg(pg.UseNavigateStable),s=x.useRef(!1);return dg(()=>{s.current=!0}),x.useCallback(function(c,f){f===void 0&&(f={}),s.current&&(typeof c=="number"?n.navigate(c):n.navigate(c,ri({fromRouteId:r},f)))},[n,r])}const dh={};function zj(n,r,s){dh[n]||(dh[n]=!0)}function Uj(n,r){n==null||n.v7_startTransition,n==null||n.v7_relativeSplatPath}function hc(n){nt(!1)}function $j(n){let{basename:r="/",children:s=null,location:a,navigationType:c=Mr.Pop,navigator:f,static:p=!1,future:h}=n;el()&&nt(!1);let m=r.replace(/^\/*/,"/"),v=x.useMemo(()=>({basename:m,navigator:f,static:p,future:ri({v7_relativeSplatPath:!1},h)}),[m,h,f,p]);typeof a=="string"&&(a=fo(a));let{pathname:w="/",search:b="",hash:N="",state:y=null,key:P="default"}=a,k=x.useMemo(()=>{let j=lg(w,m);return j==null?null:{location:{pathname:j,search:b,hash:N,state:y,key:P},navigationType:c}},[m,w,b,N,y,P,c]);return k==null?null:x.createElement(Ja.Provider,{value:v},x.createElement(Za.Provider,{children:s,value:k}))}function Bj(n){let{children:r,location:s}=n;return Ej(mc(r),s)}new Promise(()=>{});function mc(n,r){r===void 0&&(r=[]);let s=[];return x.Children.forEach(n,(a,c)=>{if(!x.isValidElement(a))return;let f=[...r,c];if(a.type===x.Fragment){s.push.apply(s,mc(a.props.children,f));return}a.type!==hc&&nt(!1),!a.props.index||!a.props.children||nt(!1);let p={id:a.props.id||f.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(p.children=mc(a.props.children,f)),s.push(p)}),s}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Vj="6";try{window.__reactRouterVersion=Vj}catch{}const Hj="startTransition",fh=yc[Hj];function Wj(n){let{basename:r,children:s,future:a,window:c}=n,f=x.useRef();f.current==null&&(f.current=Yb({window:c,v5Compat:!0}));let p=f.current,[h,m]=x.useState({action:p.action,location:p.location}),{v7_startTransition:v}=a||{},w=x.useCallback(b=>{v&&fh?fh(()=>m(b)):m(b)},[m,v]);return x.useLayoutEffect(()=>p.listen(w),[p,w]),x.useEffect(()=>Uj(a),[a]),x.createElement($j,{basename:r,children:s,location:h.location,navigationType:h.action,navigator:p,future:a})}var ph;(function(n){n.UseScrollRestoration="useScrollRestoration",n.UseSubmit="useSubmit",n.UseSubmitFetcher="useSubmitFetcher",n.UseFetcher="useFetcher",n.useViewTransitionState="useViewTransitionState"})(ph||(ph={}));var hh;(function(n){n.UseFetcher="useFetcher",n.UseFetchers="useFetchers",n.UseScrollRestoration="useScrollRestoration"})(hh||(hh={}));const mg={personalInfo:{ownerName:"",age:0,maritalStatus:"",familySize:0,education:"",phone:"",refereePhone:"",address:""},projectDescription:{projectSummary:"",projectName:"",projectLocation:"",projectType:"",projectGoals:"",targetMarket:"",expectedRevenue:0,startupCost:0},marketStudy:{products:"",services:"",hasCompetitors:!1,competitorsCount:0,competitorProducts:"",competitorProfitStrategies:[],competitorPricing:"same",competitorPromotionMethods:[],competitor1Customers:0,competitor2Customers:0},swotAnalysis:{strengths:"",weaknesses:"",opportunities:"",threats:""},marketingMix:{product:"",price:"",place:"",promotion:"",people:""},productionRequirements:{equipment:"",materials:"",humanResources:"",location:"",licenses:""},financialStudy:{fixedCosts:[{name:"الرواتب الثابتة (صاحب المشروع + الموظفين الثابتين)",monthly:0},{name:"الإيجار",monthly:0},{name:"الصيانة",monthly:0},{name:"التسويق والدعاية",monthly:0},{name:"تكاليف ثابتة أخرى",monthly:0}],variableCosts:[{name:"المواد الخام",monthly:0},{name:"أجور العمال المباشرين",monthly:0},{name:"فواتير الماء والكهرباء والهاتف والإنترنت",monthly:0},{name:"أجور النقل والمواصلات",monthly:0},{name:"تكاليف متغيرة أخرى",monthly:0}],profitRows:[{id:1,name:"",units:0,costPerUnit:0,pricePerUnit:0}],annualSales:[{id:1,name:"منتج (1)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))},{id:2,name:"منتج (2)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))},{id:3,name:"منتج (3)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))},{id:4,name:"منتج (4)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))}],breakEvenInputs:{salePrice:0,variableCost:0}},lastUpdated:new Date,currentStep:0},mh=typeof window<"u"&&window.electronAPI,Qj=(n,r)=>{switch(r.type){case"UPDATE_PERSONAL_INFO":return{...n,personalInfo:{...n.personalInfo,...r.payload},lastUpdated:new Date};case"UPDATE_PROJECT_DESCRIPTION":return{...n,projectDescription:{...n.projectDescription,...r.payload},lastUpdated:new Date};case"UPDATE_MARKET_STUDY":return{...n,marketStudy:{...n.marketStudy,...r.payload},lastUpdated:new Date};case"UPDATE_SWOT_ANALYSIS":return{...n,swotAnalysis:{...n.swotAnalysis,...r.payload},lastUpdated:new Date};case"UPDATE_MARKETING_MIX":return{...n,marketingMix:{...n.marketingMix,...r.payload},lastUpdated:new Date};case"UPDATE_PRODUCTION_REQUIREMENTS":return{...n,productionRequirements:{...n.productionRequirements,...r.payload},lastUpdated:new Date};case"UPDATE_FINANCIAL_STUDY":return{...n,financialStudy:{...n.financialStudy,...r.payload},lastUpdated:new Date};case"SET_CURRENT_STEP":return{...n,currentStep:r.payload,lastUpdated:new Date};case"LOAD_PROJECT_DATA":return r.payload;case"RESET_PROJECT_DATA":return mg;case"AUTO_SAVE":return{...n,lastUpdated:new Date};default:return n}},gg=x.createContext(void 0),Zu="project-business-plan-data",qj=({children:n})=>{const[r,s]=x.useReducer(Qj,mg);x.useEffect(()=>{y(),mh&&(window.electronAPI.onSaveProject(()=>{N()}),window.electronAPI.onLoadProject(E=>{P(E)}),window.electronAPI.onExportPDF(()=>{Ze({title:"تصدير PDF",description:"ميزة تصدير PDF ستكون متاحة قريباً"})}),window.electronAPI.onExportExcel(()=>{Ze({title:"تصدير Excel",description:"ميزة تصدير Excel ستكون متاحة قريباً"})}))},[]),x.useEffect(()=>{const E=setTimeout(()=>{b()},1e3);return()=>clearTimeout(E)},[r]);const a=E=>{s({type:"UPDATE_PERSONAL_INFO",payload:E})},c=E=>{s({type:"UPDATE_PROJECT_DESCRIPTION",payload:E})},f=E=>{s({type:"UPDATE_MARKET_STUDY",payload:E})},p=E=>{s({type:"UPDATE_SWOT_ANALYSIS",payload:E})},h=E=>{s({type:"UPDATE_MARKETING_MIX",payload:E})},m=E=>{s({type:"UPDATE_PRODUCTION_REQUIREMENTS",payload:E})},v=E=>{s({type:"UPDATE_FINANCIAL_STUDY",payload:E})},w=E=>{s({type:"SET_CURRENT_STEP",payload:E})},b=()=>{try{const E={...r,lastUpdated:new Date().toISOString()};localStorage.setItem(Zu,JSON.stringify(E))}catch(E){console.error("خطأ في حفظ البيانات:",E),Ze({title:"خطأ في الحفظ",description:"حدث خطأ أثناء حفظ البيانات",variant:"destructive"})}},N=async()=>{if(!mh){Ze({title:"غير متاح",description:"هذه الميزة متاحة فقط في تطبيق سطح المكتب",variant:"destructive"});return}try{const E={...r,lastUpdated:new Date().toISOString()},A=await window.electronAPI.saveProjectFile(E);A.success?Ze({title:"تم الحفظ بنجاح",description:`تم حفظ المشروع في: ${A.path}`}):Ze({title:"خطأ في الحفظ",description:A.error||"حدث خطأ أثناء حفظ الملف",variant:"destructive"})}catch(E){console.error("خطأ في حفظ الملف:",E),Ze({title:"خطأ في الحفظ",description:"حدث خطأ أثناء حفظ الملف",variant:"destructive"})}},y=()=>{try{const E=localStorage.getItem(Zu);if(E){const A=JSON.parse(E);A.lastUpdated=new Date(A.lastUpdated),s({type:"LOAD_PROJECT_DATA",payload:A}),Ze({title:"تم تحميل البيانات",description:"تم استرداد بيانات المشروع المحفوظة"})}}catch(E){console.error("خطأ في تحميل البيانات:",E),Ze({title:"خطأ في التحميل",description:"حدث خطأ أثناء تحميل البيانات المحفوظة",variant:"destructive"})}},P=E=>{try{const A={...E,lastUpdated:new Date(E.lastUpdated)};s({type:"LOAD_PROJECT_DATA",payload:A}),Ze({title:"تم تحميل المشروع",description:"تم تحميل بيانات المشروع من الملف بنجاح"})}catch(A){console.error("خطأ في تحميل الملف:",A),Ze({title:"خطأ في التحميل",description:"حدث خطأ أثناء تحميل بيانات المشروع",variant:"destructive"})}},_={projectData:r,updatePersonalInfo:a,updateProjectDescription:c,updateMarketStudy:f,updateSwotAnalysis:p,updateMarketingMix:h,updateProductionRequirements:m,updateFinancialStudy:v,setCurrentStep:w,saveProject:b,saveProjectToFile:N,loadProject:y,loadProjectFromFile:P,resetProject:()=>{s({type:"RESET_PROJECT_DATA"}),localStorage.removeItem(Zu),Ze({title:"تم إعادة التعيين",description:"تم مسح جميع بيانات المشروع"})},isDataValid:E=>{switch(E){case 0:return!!(r.personalInfo.ownerName&&r.personalInfo.phone&&r.projectDescription.projectName);case 1:return!!(r.marketStudy.products||r.marketStudy.services);case 2:return!!(r.swotAnalysis.strengths&&r.swotAnalysis.weaknesses);case 3:return!!(r.marketingMix.product&&r.marketingMix.price);case 4:return!!(r.productionRequirements.equipment||r.productionRequirements.materials);case 5:return r.financialStudy.fixedCosts.some(A=>A.monthly>0)||r.financialStudy.variableCosts.some(A=>A.monthly>0);default:return!0}}};return l.jsx(gg.Provider,{value:_,children:n})},po=()=>{const n=x.useContext(gg);if(!n)throw new Error("useProject must be used within a ProjectProvider");return n},Kt=x.forwardRef(({className:n,...r},s)=>l.jsx("div",{ref:s,className:Se("rounded-lg border bg-card text-card-foreground shadow-sm",n),...r}));Kt.displayName="Card";const Gt=x.forwardRef(({className:n,...r},s)=>l.jsx("div",{ref:s,className:Se("flex flex-col space-y-1.5 p-6",n),...r}));Gt.displayName="CardHeader";const Yt=x.forwardRef(({className:n,...r},s)=>l.jsx("h3",{ref:s,className:Se("text-2xl font-semibold leading-none tracking-tight",n),...r}));Yt.displayName="CardTitle";const vg=x.forwardRef(({className:n,...r},s)=>l.jsx("p",{ref:s,className:Se("text-sm text-muted-foreground",n),...r}));vg.displayName="CardDescription";const Xt=x.forwardRef(({className:n,...r},s)=>l.jsx("div",{ref:s,className:Se("p-6 pt-0",n),...r}));Xt.displayName="CardContent";const yg=x.forwardRef(({className:n,...r},s)=>l.jsx("div",{ref:s,className:Se("flex items-center p-6 pt-0",n),...r}));yg.displayName="CardFooter";const Kj=kc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),pn=x.forwardRef(({className:n,variant:r,size:s,asChild:a=!1,...c},f)=>{const p=a?iy:"button";return l.jsx(p,{className:Se(Kj({variant:r,size:s,className:n})),ref:f,...c})});pn.displayName="Button";const Gj=({currentStep:n,totalSteps:r})=>{const{isDataValid:s}=po(),a=(n+1)/r*100,c=["المعلومات الشخصية","دراسة السوق","تحليل SWOT","المزيج التسويقي","مستلزمات الإنتاج","الدراسة المالية","الإنهاء والحفظ"];return l.jsxs("div",{className:"w-full space-y-6 p-6 rounded-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-3d border border-white/20 dark:border-slate-700/30",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("div",{className:"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg animate-pulse"}),l.jsx("span",{className:"text-lg font-bold text-gradient-primary",children:"التقدم"})]}),l.jsx("div",{className:"px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-200 dark:border-blue-700/50",children:l.jsxs("span",{className:"text-sm font-bold text-blue-700 dark:text-blue-300",children:["الخطوة ",n+1,"/",r]})})]}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner",children:l.jsxs("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-700 ease-out shadow-lg relative overflow-hidden",style:{width:`${a}%`},children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/30 to-transparent"}),l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"})]})}),l.jsx("div",{className:"absolute top-0 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-blue-500 transition-all duration-700 ease-out -mt-0.5",style:{left:`calc(${a}% - 8px)`},children:l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})})]}),l.jsx("div",{className:"hidden lg:flex justify-between items-center gap-2",children:c.map((f,p)=>{const h=p<n,m=p===n,v=s(p);return l.jsxs("div",{className:"flex flex-col items-center space-y-2 flex-1",children:[l.jsxs("div",{className:Se("relative flex items-center justify-center w-12 h-12 rounded-full border-3 transition-all duration-500 shadow-lg",h?"bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 text-white shadow-green-200 dark:shadow-green-900/50 scale-110":m?v?"bg-gradient-to-r from-blue-500 to-purple-500 border-blue-400 text-white shadow-blue-200 dark:shadow-blue-900/50 scale-110 animate-glow":"bg-gradient-to-r from-amber-500 to-orange-500 border-amber-400 text-white shadow-amber-200 dark:shadow-amber-900/50 scale-110 animate-pulse":"bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"),children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-white/20 to-transparent rounded-full"}),h?l.jsx(Nc,{className:"w-6 h-6 relative z-10"}):m&&!v?l.jsx(lm,{className:"w-6 h-6 relative z-10"}):l.jsx("div",{className:"w-3 h-3 bg-current rounded-full relative z-10"}),m&&l.jsx("div",{className:"absolute inset-0 rounded-full bg-current opacity-20 animate-ping"})]}),l.jsx("span",{className:Se("text-center text-xs font-medium leading-tight px-2 py-1 rounded-lg transition-all duration-300",m?"text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 shadow-sm":h?"text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30":"text-gray-500 dark:text-gray-400"),children:f}),p<c.length-1&&l.jsx("div",{className:"absolute top-6 left-1/2 w-full h-0.5 -z-10",children:l.jsx("div",{className:Se("h-full transition-all duration-500",h?"bg-gradient-to-r from-green-400 to-emerald-400":p<n?"bg-gradient-to-r from-blue-400 to-purple-400":"bg-gray-300 dark:bg-gray-600")})})]},p)})})]})},ke=x.forwardRef(({className:n,type:r,...s},a)=>l.jsx("input",{type:r,className:Se("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),ref:a,...s}));ke.displayName="Input";var Yj="Label",xg=x.forwardRef((n,r)=>l.jsx($e.label,{...n,ref:r,onMouseDown:s=>{var c;s.target.closest("button, input, select, textarea")||((c=n.onMouseDown)==null||c.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));xg.displayName=Yj;var wg=xg;const Xj=kc("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ee=x.forwardRef(({className:n,...r},s)=>l.jsx(wg,{ref:s,className:Se(Xj(),n),...r}));Ee.displayName=wg.displayName;const Er=({label:n,id:r,type:s="text"})=>{const{projectData:a,updatePersonalInfo:c}=po(),f=a.personalInfo[r],p=h=>{const m=s==="number"?Number(h.target.value):h.target.value;c({[r]:m})};return l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 items-center gap-4 group",children:[l.jsx(Ee,{htmlFor:r,className:"md:text-right font-semibold text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300",children:n}),l.jsx(ke,{id:r,type:s,value:f||"",onChange:p,className:"col-span-1 md:col-span-2 input-3d shadow-lg border-2 border-white/50 dark:border-slate-600/50 focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-300 hover:shadow-xl"})]})},Jj=()=>l.jsxs(Kt,{className:"w-full card-3d shadow-3d-hover border-glow bg-gradient-to-br from-sky-50/90 via-blue-50/90 to-indigo-50/90 dark:from-sky-900/30 dark:via-blue-900/30 dark:to-indigo-900/30 backdrop-blur-sm relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-sky-400/5 to-blue-400/5 animate-pulse"}),l.jsxs(Gt,{className:"border-b border-sky-200/50 dark:border-sky-700/50 relative z-10 bg-gradient-to-r from-white/50 to-sky-50/50 dark:from-slate-800/50 dark:to-sky-900/50",children:[l.jsxs(Yt,{className:"flex items-center gap-4 text-2xl md:text-3xl font-bold",children:[l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full blur opacity-50"}),l.jsx("div",{className:"relative w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg",children:l.jsx(dm,{className:"h-6 w-6 text-white"})})]}),l.jsx("span",{className:"text-gradient-primary",children:"المعلومات الشخصية"})]}),l.jsx("div",{className:"mt-2 w-16 h-1 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full shadow-lg"})]}),l.jsx(Xt,{className:"pt-8 pb-6 relative z-10",children:l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[l.jsx(Er,{label:"إسم صاحب/ة المشروع",id:"ownerName"}),l.jsx(Er,{label:"العمر",id:"age",type:"number"})]}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[l.jsx(Er,{label:"الحالة الإجتماعية",id:"maritalStatus"}),l.jsx(Er,{label:"عدد أفراد الأسرة",id:"familySize",type:"number"})]}),l.jsx(Er,{label:"المؤهل العلمي",id:"education"}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[l.jsx(Er,{label:"رقم الهاتف",id:"phone"}),l.jsx(Er,{label:"رقم هاتف شخص معرف",id:"refereePhone"})]}),l.jsx(Er,{label:"مكان السكن",id:"address"})]})}),l.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-sky-500 to-blue-500 opacity-50"})]}),hn=x.forwardRef(({className:n,...r},s)=>l.jsx("textarea",{className:Se("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),ref:s,...r}));hn.displayName="Textarea";var Zj=x.createContext(void 0);function bg(n){const r=x.useContext(Zj);return n||r||"ltr"}var ec="rovingFocusGroup.onEntryFocus",e2={bubbles:!1,cancelable:!0},ui="RovingFocusGroup",[gc,jg,t2]=Mh(ui),[n2,kg]=zr(ui,[t2]),[r2,o2]=n2(ui),Ng=x.forwardRef((n,r)=>l.jsx(gc.Provider,{scope:n.__scopeRovingFocusGroup,children:l.jsx(gc.Slot,{scope:n.__scopeRovingFocusGroup,children:l.jsx(s2,{...n,ref:r})})}));Ng.displayName=ui;var s2=x.forwardRef((n,r)=>{const{__scopeRovingFocusGroup:s,orientation:a,loop:c=!1,dir:f,currentTabStopId:p,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:m,onEntryFocus:v,preventScrollOnEntryFocus:w=!1,...b}=n,N=x.useRef(null),y=ut(r,N),P=bg(f),[k,j]=Ba({prop:p,defaultProp:h??null,onChange:m,caller:ui}),[_,E]=x.useState(!1),A=Jn(v),z=jg(s),O=x.useRef(!1),[F,$]=x.useState(0);return x.useEffect(()=>{const G=N.current;if(G)return G.addEventListener(ec,A),()=>G.removeEventListener(ec,A)},[A]),l.jsx(r2,{scope:s,orientation:a,dir:P,loop:c,currentTabStopId:k,onItemFocus:x.useCallback(G=>j(G),[j]),onItemShiftTab:x.useCallback(()=>E(!0),[]),onFocusableItemAdd:x.useCallback(()=>$(G=>G+1),[]),onFocusableItemRemove:x.useCallback(()=>$(G=>G-1),[]),children:l.jsx($e.div,{tabIndex:_||F===0?-1:0,"data-orientation":a,...b,ref:y,style:{outline:"none",...n.style},onMouseDown:Oe(n.onMouseDown,()=>{O.current=!0}),onFocus:Oe(n.onFocus,G=>{const J=!O.current;if(G.target===G.currentTarget&&J&&!_){const Z=new CustomEvent(ec,e2);if(G.currentTarget.dispatchEvent(Z),!Z.defaultPrevented){const pe=z().filter(se=>se.focusable),te=pe.find(se=>se.active),ge=pe.find(se=>se.id===k),ve=[te,ge,...pe].filter(Boolean).map(se=>se.ref.current);Eg(ve,w)}}O.current=!1}),onBlur:Oe(n.onBlur,()=>E(!1))})})}),Sg="RovingFocusGroupItem",Cg=x.forwardRef((n,r)=>{const{__scopeRovingFocusGroup:s,focusable:a=!0,active:c=!1,tabStopId:f,children:p,...h}=n,m=zw(),v=f||m,w=o2(Sg,s),b=w.currentTabStopId===v,N=jg(s),{onFocusableItemAdd:y,onFocusableItemRemove:P,currentTabStopId:k}=w;return x.useEffect(()=>{if(a)return y(),()=>P()},[a,y,P]),l.jsx(gc.ItemSlot,{scope:s,id:v,focusable:a,active:c,children:l.jsx($e.span,{tabIndex:b?0:-1,"data-orientation":w.orientation,...h,ref:r,onMouseDown:Oe(n.onMouseDown,j=>{a?w.onItemFocus(v):j.preventDefault()}),onFocus:Oe(n.onFocus,()=>w.onItemFocus(v)),onKeyDown:Oe(n.onKeyDown,j=>{if(j.key==="Tab"&&j.shiftKey){w.onItemShiftTab();return}if(j.target!==j.currentTarget)return;const _=l2(j,w.orientation,w.dir);if(_!==void 0){if(j.metaKey||j.ctrlKey||j.altKey||j.shiftKey)return;j.preventDefault();let A=N().filter(z=>z.focusable).map(z=>z.ref.current);if(_==="last")A.reverse();else if(_==="prev"||_==="next"){_==="prev"&&A.reverse();const z=A.indexOf(j.currentTarget);A=w.loop?u2(A,z+1):A.slice(z+1)}setTimeout(()=>Eg(A))}}),children:typeof p=="function"?p({isCurrentTabStop:b,hasTabStop:k!=null}):p})})});Cg.displayName=Sg;var i2={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function a2(n,r){return r!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function l2(n,r,s){const a=a2(n.key,s);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(a))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(a)))return i2[a]}function Eg(n,r=!1){const s=document.activeElement;for(const a of n)if(a===s||(a.focus({preventScroll:r}),document.activeElement!==s))return}function u2(n,r){return n.map((s,a)=>n[(r+a)%n.length])}var c2=Ng,d2=Cg;function Pg(n){const r=x.useRef({value:n,previous:n});return x.useMemo(()=>(r.current.value!==n&&(r.current.previous=r.current.value,r.current.value=n),r.current.previous),[n])}var $c="Radio",[f2,Tg]=zr($c),[p2,h2]=f2($c),Rg=x.forwardRef((n,r)=>{const{__scopeRadio:s,name:a,checked:c=!1,required:f,disabled:p,value:h="on",onCheck:m,form:v,...w}=n,[b,N]=x.useState(null),y=ut(r,j=>N(j)),P=x.useRef(!1),k=b?v||!!b.closest("form"):!0;return l.jsxs(p2,{scope:s,checked:c,disabled:p,children:[l.jsx($e.button,{type:"button",role:"radio","aria-checked":c,"data-state":Mg(c),"data-disabled":p?"":void 0,disabled:p,value:h,...w,ref:y,onClick:Oe(n.onClick,j=>{c||m==null||m(),k&&(P.current=j.isPropagationStopped(),P.current||j.stopPropagation())})}),k&&l.jsx(Og,{control:b,bubbles:!P.current,name:a,value:h,checked:c,required:f,disabled:p,form:v,style:{transform:"translateX(-100%)"}})]})});Rg.displayName=$c;var _g="RadioIndicator",Ag=x.forwardRef((n,r)=>{const{__scopeRadio:s,forceMount:a,...c}=n,f=h2(_g,s);return l.jsx(ii,{present:a||f.checked,children:l.jsx($e.span,{"data-state":Mg(f.checked),"data-disabled":f.disabled?"":void 0,...c,ref:r})})});Ag.displayName=_g;var m2="RadioBubbleInput",Og=x.forwardRef(({__scopeRadio:n,control:r,checked:s,bubbles:a=!0,...c},f)=>{const p=x.useRef(null),h=ut(p,f),m=Pg(s),v=Oc(r);return x.useEffect(()=>{const w=p.current;if(!w)return;const b=window.HTMLInputElement.prototype,y=Object.getOwnPropertyDescriptor(b,"checked").set;if(m!==s&&y){const P=new Event("click",{bubbles:a});y.call(w,s),w.dispatchEvent(P)}},[m,s,a]),l.jsx($e.input,{type:"radio","aria-hidden":!0,defaultChecked:s,...c,tabIndex:-1,ref:h,style:{...c.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Og.displayName=m2;function Mg(n){return n?"checked":"unchecked"}var g2=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],tl="RadioGroup",[v2,rk]=zr(tl,[kg,Tg]),Dg=kg(),Ig=Tg(),[y2,x2]=v2(tl),Fg=x.forwardRef((n,r)=>{const{__scopeRadioGroup:s,name:a,defaultValue:c,value:f,required:p=!1,disabled:h=!1,orientation:m,dir:v,loop:w=!0,onValueChange:b,...N}=n,y=Dg(s),P=bg(v),[k,j]=Ba({prop:f,defaultProp:c??null,onChange:b,caller:tl});return l.jsx(y2,{scope:s,name:a,required:p,disabled:h,value:k,onValueChange:j,children:l.jsx(c2,{asChild:!0,...y,orientation:m,dir:P,loop:w,children:l.jsx($e.div,{role:"radiogroup","aria-required":p,"aria-orientation":m,"data-disabled":h?"":void 0,dir:P,...N,ref:r})})})});Fg.displayName=tl;var Lg="RadioGroupItem",zg=x.forwardRef((n,r)=>{const{__scopeRadioGroup:s,disabled:a,...c}=n,f=x2(Lg,s),p=f.disabled||a,h=Dg(s),m=Ig(s),v=x.useRef(null),w=ut(r,v),b=f.value===c.value,N=x.useRef(!1);return x.useEffect(()=>{const y=k=>{g2.includes(k.key)&&(N.current=!0)},P=()=>N.current=!1;return document.addEventListener("keydown",y),document.addEventListener("keyup",P),()=>{document.removeEventListener("keydown",y),document.removeEventListener("keyup",P)}},[]),l.jsx(d2,{asChild:!0,...h,focusable:!p,active:b,children:l.jsx(Rg,{disabled:p,required:f.required,checked:b,...m,...c,name:f.name,ref:w,onCheck:()=>f.onValueChange(c.value),onKeyDown:Oe(y=>{y.key==="Enter"&&y.preventDefault()}),onFocus:Oe(c.onFocus,()=>{var y;N.current&&((y=v.current)==null||y.click())})})})});zg.displayName=Lg;var w2="RadioGroupIndicator",Ug=x.forwardRef((n,r)=>{const{__scopeRadioGroup:s,...a}=n,c=Ig(s);return l.jsx(Ag,{...c,...a,ref:r})});Ug.displayName=w2;var $g=Fg,Bg=zg,b2=Ug;const Dr=x.forwardRef(({className:n,...r},s)=>l.jsx($g,{className:Se("grid gap-2",n),...r,ref:s}));Dr.displayName=$g.displayName;const nl=x.forwardRef(({className:n,...r},s)=>l.jsx(Bg,{ref:s,className:Se("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),...r,children:l.jsx(b2,{className:"flex items-center justify-center",children:l.jsx(lx,{className:"h-2.5 w-2.5 fill-current text-current"})})}));nl.displayName=Bg.displayName;var rl="Checkbox",[j2,ok]=zr(rl),[k2,Bc]=j2(rl);function N2(n){const{__scopeCheckbox:r,checked:s,children:a,defaultChecked:c,disabled:f,form:p,name:h,onCheckedChange:m,required:v,value:w="on",internal_do_not_use_render:b}=n,[N,y]=Ba({prop:s,defaultProp:c??!1,onChange:m,caller:rl}),[P,k]=x.useState(null),[j,_]=x.useState(null),E=x.useRef(!1),A=P?!!p||!!P.closest("form"):!0,z={checked:N,disabled:f,setChecked:y,control:P,setControl:k,name:h,form:p,value:w,hasConsumerStoppedPropagationRef:E,required:v,defaultChecked:Ir(c)?!1:c,isFormControl:A,bubbleInput:j,setBubbleInput:_};return l.jsx(k2,{scope:r,...z,children:S2(b)?b(z):a})}var Vg="CheckboxTrigger",Hg=x.forwardRef(({__scopeCheckbox:n,onKeyDown:r,onClick:s,...a},c)=>{const{control:f,value:p,disabled:h,checked:m,required:v,setControl:w,setChecked:b,hasConsumerStoppedPropagationRef:N,isFormControl:y,bubbleInput:P}=Bc(Vg,n),k=ut(c,w),j=x.useRef(m);return x.useEffect(()=>{const _=f==null?void 0:f.form;if(_){const E=()=>b(j.current);return _.addEventListener("reset",E),()=>_.removeEventListener("reset",E)}},[f,b]),l.jsx($e.button,{type:"button",role:"checkbox","aria-checked":Ir(m)?"mixed":m,"aria-required":v,"data-state":Gg(m),"data-disabled":h?"":void 0,disabled:h,value:p,...a,ref:k,onKeyDown:Oe(r,_=>{_.key==="Enter"&&_.preventDefault()}),onClick:Oe(s,_=>{b(E=>Ir(E)?!0:!E),P&&y&&(N.current=_.isPropagationStopped(),N.current||_.stopPropagation())})})});Hg.displayName=Vg;var Vc=x.forwardRef((n,r)=>{const{__scopeCheckbox:s,name:a,checked:c,defaultChecked:f,required:p,disabled:h,value:m,onCheckedChange:v,form:w,...b}=n;return l.jsx(N2,{__scopeCheckbox:s,checked:c,defaultChecked:f,disabled:h,required:p,onCheckedChange:v,name:a,form:w,value:m,internal_do_not_use_render:({isFormControl:N})=>l.jsxs(l.Fragment,{children:[l.jsx(Hg,{...b,ref:r,__scopeCheckbox:s}),N&&l.jsx(Kg,{__scopeCheckbox:s})]})})});Vc.displayName=rl;var Wg="CheckboxIndicator",Qg=x.forwardRef((n,r)=>{const{__scopeCheckbox:s,forceMount:a,...c}=n,f=Bc(Wg,s);return l.jsx(ii,{present:a||Ir(f.checked)||f.checked===!0,children:l.jsx($e.span,{"data-state":Gg(f.checked),"data-disabled":f.disabled?"":void 0,...c,ref:r,style:{pointerEvents:"none",...n.style}})})});Qg.displayName=Wg;var qg="CheckboxBubbleInput",Kg=x.forwardRef(({__scopeCheckbox:n,...r},s)=>{const{control:a,hasConsumerStoppedPropagationRef:c,checked:f,defaultChecked:p,required:h,disabled:m,name:v,value:w,form:b,bubbleInput:N,setBubbleInput:y}=Bc(qg,n),P=ut(s,y),k=Pg(f),j=Oc(a);x.useEffect(()=>{const E=N;if(!E)return;const A=window.HTMLInputElement.prototype,O=Object.getOwnPropertyDescriptor(A,"checked").set,F=!c.current;if(k!==f&&O){const $=new Event("click",{bubbles:F});E.indeterminate=Ir(f),O.call(E,Ir(f)?!1:f),E.dispatchEvent($)}},[N,k,f,c]);const _=x.useRef(Ir(f)?!1:f);return l.jsx($e.input,{type:"checkbox","aria-hidden":!0,defaultChecked:p??_.current,required:h,disabled:m,name:v,value:w,form:b,...r,tabIndex:-1,ref:P,style:{...r.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});Kg.displayName=qg;function S2(n){return typeof n=="function"}function Ir(n){return n==="indeterminate"}function Gg(n){return Ir(n)?"indeterminate":n?"checked":"unchecked"}const ci=x.forwardRef(({className:n,...r},s)=>l.jsx(Vc,{ref:s,className:Se("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",n),...r,children:l.jsx(Qg,{className:Se("flex items-center justify-center text-current"),children:l.jsx(ix,{className:"h-4 w-4"})})}));ci.displayName=Vc.displayName;const Ft=({label:n,id:r,children:s,type:a="text"})=>{const{projectData:c,updateProjectDescription:f}=po();if(s)return l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 items-start gap-4",children:[l.jsx(Ee,{htmlFor:r,className:"md:text-right pt-2",children:n}),l.jsx("div",{className:"col-span-1 md:col-span-2",children:s})]});const p=c.projectDescription[r],h=m=>{const v=a==="number"?Number(m.target.value):m.target.value;f({[r]:v})};return l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 items-start gap-4",children:[l.jsx(Ee,{htmlFor:r,className:"md:text-right pt-2",children:n}),l.jsx("div",{className:"col-span-1 md:col-span-2",children:a==="textarea"?l.jsx(hn,{id:r,value:p||"",onChange:h}):l.jsx(ke,{id:r,type:a,value:p||"",onChange:h})})]})},Kn=({label:n,id:r})=>l.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center gap-2 cursor-pointer",children:[l.jsx("span",{children:n}),l.jsx(ci,{id:r})]}),gh=({label:n,id:r,value:s})=>l.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center gap-2 cursor-pointer",children:[l.jsx("span",{children:n}),l.jsx(nl,{value:s,id:r})]}),C2=()=>l.jsxs(Kt,{className:"w-full bg-blue-50 dark:bg-blue-900/30",children:[l.jsx(Gt,{className:"border-b",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[l.jsx(um,{className:"h-6 w-6 text-primary"}),"وصف المشروع"]})}),l.jsx(Xt,{className:"pt-6",children:l.jsxs("div",{className:"space-y-6",children:[l.jsx(Ft,{label:"ملخص وصف خصائص المشروع",id:"projectSummary",type:"textarea"}),l.jsx(Ft,{label:"اسم المشروع",id:"projectName"}),l.jsx(Ft,{label:"موقع المشروع",id:"projectLocation"}),l.jsx(Ft,{label:"قيمة المنحة المطلوبة",id:"grantAmount",children:l.jsx(ke,{id:"grantAmount",type:"number"})}),l.jsx(Ft,{label:"التمويل الذاتي أو مصادر التمويل الأخرى المتوفرة",id:"selfFunding",children:l.jsx(ke,{id:"selfFunding"})}),l.jsx(Ft,{label:"تكلفة المشروع الكلية",id:"totalCost",children:l.jsx(ke,{id:"totalCost",type:"number"})}),l.jsx(Ft,{label:"تاريخ تقديم خطة المشروع",id:"submissionDate",children:l.jsx(ke,{id:"submissionDate",type:"date"})}),l.jsx(Ft,{label:"وصف أهمية الفكرة ونوع المشروع",id:"ideaDescription",children:l.jsx(hn,{id:"ideaDescription"})}),l.jsx(Ft,{label:"وصف المهارات اللازم امتلاكها لتنفيذ المشروع",id:"skillsDescription",children:l.jsx(hn,{id:"skillsDescription"})}),l.jsx(Ft,{label:"وصف حاجة المجتمع للمشروع",id:"communityNeed",children:l.jsx(hn,{id:"communityNeed"})}),l.jsx(Ft,{label:"هل يحتاج المشروع إلى ترخيص؟",id:"licenseRequired",children:l.jsxs(Dr,{defaultValue:"no",className:"flex items-center gap-x-6",children:[l.jsx(gh,{label:"نعم",id:"licenseYes",value:"yes"}),l.jsx(gh,{label:"لا",id:"licenseNo",value:"no"})]})}),l.jsx(Ft,{label:"إذا كان نعم، أذكر جهة الترخيص",id:"licensingAuthority",children:l.jsx(ke,{id:"licensingAuthority"})}),l.jsx(Ft,{label:"الفئة المستهدفة بالمشروع",id:"targetAudience",children:l.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-4",children:[l.jsx(Kn,{label:"أطفال",id:"targetChildren"}),l.jsx(Kn,{label:"شباب",id:"targetYouth"}),l.jsx(Kn,{label:"نساء",id:"targetWomen"}),l.jsx(Kn,{label:"رجال",id:"targetMen"}),l.jsx(Kn,{label:"كبار سن",id:"targetSeniors"}),l.jsx(Kn,{label:"مؤسسات/شركات",id:"targetCompanies"}),l.jsx(Kn,{label:"جمعيات",id:"targetAssociations"}),l.jsx(Kn,{label:"مدارس",id:"targetSchools"}),l.jsx(Kn,{label:"فنادق",id:"targetHotels"}),l.jsxs("div",{className:"flex items-center col-span-2 sm:col-span-3 gap-2",children:[l.jsxs(Ee,{htmlFor:"targetOther",className:"font-normal flex items-center gap-2 cursor-pointer",children:[l.jsx("span",{children:"أخرى:"}),l.jsx(ci,{id:"targetOther"})]}),l.jsx(ke,{id:"targetOtherText",className:"flex-1"})]})]})})]})})]}),E2=()=>l.jsxs("div",{className:"space-y-8",children:[l.jsx(Jj,{}),l.jsx(C2,{})]}),Ks=({label:n,id:r,value:s})=>l.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[l.jsx(nl,{value:s,id:r}),l.jsx("span",{children:n})]}),Tn=({label:n,id:r})=>l.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[l.jsx(ci,{id:r}),l.jsx("span",{children:n})]}),P2=()=>l.jsxs(Kt,{className:"w-full bg-teal-50 dark:bg-teal-900/30",children:[l.jsx(Gt,{className:"border-b",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[l.jsx(sx,{className:"h-6 w-6 text-primary"}),"دراسة السوق والمنافسين"]})}),l.jsxs(Xt,{className:"pt-6 space-y-8",children:[l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"ماذا ستقدم في مشروعك؟"}),l.jsxs("div",{className:"pr-4 space-y-4",children:[l.jsxs("div",{children:[l.jsx(Ee,{htmlFor:"products",className:"font-normal text-muted-foreground",children:"◼️ منتجات (اذكرها):"}),l.jsx(hn,{id:"products",className:"mt-1"})]}),l.jsxs("div",{children:[l.jsx(Ee,{htmlFor:"services",className:"font-normal text-muted-foreground",children:"◼️ خدمات (اذكرها):"}),l.jsx(hn,{id:"services",className:"mt-1"})]})]})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"هل يوجد منافسين يبيعون نفس المنتج أو منتج شبيه في منطقة مشروعك؟"}),l.jsx("div",{className:"pr-4",children:l.jsxs(Dr,{defaultValue:"no",className:"flex items-center flex-wrap gap-x-6 gap-y-2",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Ks,{label:"نعم – كم عددهم؟",id:"competitorsYes",value:"yes"}),l.jsx(ke,{id:"competitorsCount",type:"number",className:"w-24 h-8"})]}),l.jsx(Ks,{label:"لا",id:"competitorsNo",value:"no"})]})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{htmlFor:"competitorProducts",className:"text-base font-semibold",children:"ما هي المنتجات المنافسة أو الشبيهة التي يبيعها المنافسون؟"}),l.jsx("div",{className:"pr-4",children:l.jsx(hn,{id:"competitorProducts"})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"كيف يحاول المنافسون أن يحققوا الربح؟"}),l.jsxs("div",{className:"pr-4 space-y-2",children:[l.jsx(Tn,{label:"من خلال السعر المنخفض",id:"profitPrice"}),l.jsx(Tn,{label:"من خلال جودة المنتج",id:"profitQuality"}),l.jsx(Tn,{label:"من خلال الخدمة المميزة",id:"profitService"}),l.jsx(Tn,{label:"من خلال التكلفة المنخفضة",id:"profitCost"}),l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[l.jsx(Tn,{label:"أخرى (اذكرها):",id:"profitOther"}),l.jsx(ke,{id:"profitOtherText",className:"flex-1 h-8"})]})]})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"ما هي أسعار المنافسين مقارنة بالسعر الذي تنوي البيع به؟"}),l.jsx("div",{className:"pr-4",children:l.jsxs(Dr,{className:"space-y-2",children:[l.jsx(Ks,{label:"نفس سعر البيع الذي سأبيع به تقريبًا",id:"priceSame",value:"same"}),l.jsx(Ks,{label:"أسعار المنافسين أعلى من سعري",id:"priceHigher",value:"higher"}),l.jsx(Ks,{label:"أسعار المنافسين أقل من سعري",id:"priceLower",value:"lower"})]})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"كيف يبيع ويُروّج المنافسون منتجاتهم؟"}),l.jsxs("div",{className:"pr-4 space-y-2",children:[l.jsx(Tn,{label:"من خلال الإنترنت ووسائل التواصل الاجتماعي",id:"promoSocial"}),l.jsx(Tn,{label:"من خلال البيع المباشر للناس",id:"promoDirect"}),l.jsx(Tn,{label:"من خلال زيارة الزبائن والترويج للمنتج",id:"promoVisits"}),l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[l.jsx(Tn,{label:"الإعلان – وسيلة الإعلان:",id:"promoAd"}),l.jsx(ke,{id:"promoAdText",className:"flex-1 h-8"})]}),l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[l.jsx(Tn,{label:"أخرى (اذكرها):",id:"promoOther"}),l.jsx(ke,{id:"promoOtherText",className:"flex-1 h-8"})]})]})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"قم بمراقبة اثنين من المنافسين وقدر عدد الزبائن الذين يترددون عليهم في اليوم الواحد:"}),l.jsxs("div",{className:"pr-4 space-y-3",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Ee,{className:"font-normal w-48",children:"◼️ عدد الزبائن عند المنافس 1:"}),l.jsx(ke,{type:"number",className:"w-24 h-8"})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Ee,{className:"font-normal w-48",children:"◼️ عدد الزبائن عند المنافس 2:"}),l.jsx(ke,{type:"number",className:"w-24 h-8"})]})]})]})]})]}),un=({label:n,id:r,value:s})=>l.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[l.jsx(nl,{value:s,id:r}),l.jsx("span",{children:n})]}),Gn=({label:n,id:r})=>l.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[l.jsx(ci,{id:r}),l.jsx("span",{children:n})]}),T2=()=>l.jsxs(Kt,{className:"w-full bg-emerald-50 dark:bg-emerald-900/30",children:[l.jsx(Gt,{className:"border-b",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[l.jsx(ox,{className:"h-6 w-6 text-primary"}),"دراسة السوق والمنافسين – الجزء الثاني"]})}),l.jsxs(Xt,{className:"pt-6 space-y-8",children:[l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{htmlFor:"potentialCustomers",className:"text-base font-semibold",children:"كم أعداد الزبائن المحتمل أن يشتروا منتجاتك (العدد الكلي للزبائن المحتملين)"}),l.jsx("div",{className:"pr-4",children:l.jsx(ke,{type:"number",id:"potentialCustomers"})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{htmlFor:"consumptionRate",className:"text-base font-semibold",children:"كم معدل استهلاكهم للمنتج في الشهر"}),l.jsx("div",{className:"pr-4",children:l.jsx(ke,{type:"number",id:"consumptionRate"})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"هل المنتجات الشبيهة تلبي احتياج السوق أم أن هناك طلبًا كبيرًا على المنتجات والمعروض أقل:"}),l.jsx("div",{className:"pr-4",children:l.jsxs(Dr,{className:"space-y-2",children:[l.jsx(un,{label:"هناك طلب كبير على المنتجات والمعروض أقل",id:"demandHigh",value:"high"}),l.jsx(un,{label:"المعروض من المنتجات الشبيهة أكبر من الطلب",id:"supplyHigh",value:"supply_high"}),l.jsx(un,{label:"المعروض في السوق هو نفس حاجة السوق (العرض = الطلب)",id:"demandEqual",value:"equal"})]})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{htmlFor:"peakSeasons",className:"text-base font-semibold",children:"ما هي المواسم التي يشتد فيها البيع لمنتجاتك أو خدماتك (مثل: الأعياد، رمضان، الصيف...)"}),l.jsx("div",{className:"pr-4",children:l.jsx(hn,{id:"peakSeasons"})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"بماذا سيتميز (يتفوق) منتجك عن المنتجات الشبيهة في السوق؟"}),l.jsxs("div",{className:"pr-4 space-y-2",children:[l.jsx(Gn,{label:"السعر المنخفض",id:"advantagePrice"}),l.jsx(Gn,{label:"الجودة العالية",id:"advantageQuality"}),l.jsx(Gn,{label:"الخدمة المميزة",id:"advantageService"}),l.jsx(Gn,{label:"التكلفة المنخفضة",id:"advantageCost"}),l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[l.jsx(Gn,{label:"أخرى (اذكرها):",id:"advantageOther"}),l.jsx(ke,{id:"advantageOtherText",className:"flex-1 h-8"})]})]})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"كيف ستقوم ببيع وتسويق منتجك؟"}),l.jsxs("div",{className:"pr-4 space-y-2",children:[l.jsx(Gn,{label:"من خلال الإنترنت ووسائل التواصل الاجتماعي",id:"marketingSocial"}),l.jsx(Gn,{label:"من خلال البيع المباشر للناس",id:"marketingDirect"}),l.jsx(Gn,{label:"من خلال زيارة الزبائن والترويج للمنتج",id:"marketingVisits"}),l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[l.jsx(Gn,{label:"أخرى (اذكرها):",id:"marketingOther"}),l.jsx(ke,{id:"marketingOtherText",className:"flex-1 h-8"})]})]})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"هل سيحتاج مشروعك إلى موردين أو مزودين للمواد الخام (الأولية) أو الخدمات؟"}),l.jsx("div",{className:"pr-4",children:l.jsxs(Dr,{defaultValue:"no",className:"flex items-center gap-x-6",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(un,{label:"نعم – كم عددهم؟",id:"suppliersYes",value:"yes"}),l.jsx(ke,{id:"suppliersCount",type:"number",className:"w-24 h-8"})]}),l.jsx(un,{label:"لا",id:"suppliersNo",value:"no"})]})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"هل المزودين أو الموردين سهل الوصول إليهم؟"}),l.jsx("div",{className:"pr-4",children:l.jsxs(Dr,{className:"space-y-2",children:[l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-md",children:[l.jsx(un,{label:"نعم (كيف؟)",id:"suppliersAccessYes",value:"yes"}),l.jsx(ke,{id:"suppliersAccessYesText",className:"flex-1 h-8"})]}),l.jsxs("div",{className:"flex items-center gap-2 w-full max-w-md",children:[l.jsx(un,{label:"لا (لماذا؟)",id:"suppliersAccessNo",value:"no"}),l.jsx(ke,{id:"suppliersAccessNoText",className:"flex-1 h-8"})]})]})})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx(Ee,{className:"text-base font-semibold",children:"هل أسعار المزودين أو الموردين مناسبة؟"}),l.jsx("div",{className:"pr-4",children:l.jsxs(Dr,{className:"space-y-2",children:[l.jsx(un,{label:"مناسبة",id:"supplierPriceGood",value:"good"}),l.jsx(un,{label:"مرتفعة",id:"supplierPriceHigh",value:"high"}),l.jsx(un,{label:"منخفضة",id:"supplierPriceLow",value:"low"}),l.jsx(un,{label:"متذبذبة حسب السوق",id:"supplierPriceFluctuates",value:"fluctuates"})]})})]})]})]}),R2=()=>l.jsxs("div",{className:"space-y-8",children:[l.jsx(P2,{}),l.jsx(T2,{})]}),_a=({title:n,description:r,id:s,icon:a,color:c})=>{const{projectData:f,updateSwotAnalysis:p}=po(),h=f.swotAnalysis[s],m=v=>{p({[s]:v.target.value})};return l.jsxs("div",{className:"space-y-4 p-6 rounded-2xl border-2 border-white/30 dark:border-slate-600/30 bg-gradient-card shadow-3d-hover transition-all duration-300 hover:scale-105 relative overflow-hidden group",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),l.jsxs("div",{className:"flex items-center gap-4 relative z-10",children:[l.jsxs("div",{className:Se("relative flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-2xl shadow-lg transition-transform duration-300 group-hover:scale-110",c),children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-2xl"}),l.jsx(a,{className:"h-7 w-7 text-white relative z-10"}),l.jsx("div",{className:Se("absolute inset-0 rounded-2xl opacity-50 animate-ping",c.replace("bg-","bg-"))})]}),l.jsxs("div",{className:"flex-1",children:[l.jsx(Ee,{htmlFor:s,className:"font-bold text-xl md:text-2xl text-gradient-primary block",children:n}),l.jsx("p",{className:"text-sm text-muted-foreground mt-1 leading-relaxed",children:r})]})]}),l.jsx(hn,{id:s,rows:6,className:"input-3d shadow-lg border-2 border-white/50 dark:border-slate-600/50 focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-300 hover:shadow-xl resize-none relative z-10",value:h||"",onChange:m,placeholder:`اكتب ${n.toLowerCase()} هنا...`}),l.jsx("div",{className:Se("absolute bottom-0 left-0 right-0 h-1 opacity-60",c)})]})},_2=()=>l.jsxs(Kt,{className:"w-full card-3d shadow-3d-hover border-glow bg-gradient-to-br from-amber-50/90 via-orange-50/90 to-yellow-50/90 dark:from-amber-900/30 dark:via-orange-900/30 dark:to-yellow-900/30 backdrop-blur-sm relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-400/5 to-orange-400/5 animate-pulse"}),l.jsxs(Gt,{className:"border-b border-amber-200/50 dark:border-amber-700/50 relative z-10 bg-gradient-to-r from-white/50 to-amber-50/50 dark:from-slate-800/50 dark:to-amber-900/50",children:[l.jsxs(Yt,{className:"flex items-center gap-4 text-2xl md:text-3xl font-bold",children:[l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full blur opacity-50"}),l.jsx("div",{className:"relative w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg",children:l.jsx(mx,{className:"h-6 w-6 text-white"})})]}),l.jsxs("div",{className:"flex flex-col",children:[l.jsx("span",{className:"text-gradient-primary",children:"التحليل الرباعي"}),l.jsx("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-300",children:"SWOT Analysis"})]})]}),l.jsx("div",{className:"mt-2 w-16 h-1 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full shadow-lg"})]}),l.jsx(Xt,{className:"pt-8 pb-6 relative z-10",children:l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[l.jsx(_a,{title:"نقاط القوة",description:"(المهارات، القدرة المالية، الدعم العائلي، الخبرة السابقة...)",id:"strengths",icon:vx,color:"bg-gradient-to-r from-green-500 to-emerald-500"}),l.jsx(_a,{title:"نقاط الضعف",description:"(عدم وجود المهارات، ضعف القدرة المالية، قلة الخبرة...)",id:"weaknesses",icon:gx,color:"bg-gradient-to-r from-red-500 to-rose-500"}),l.jsx(_a,{title:"الفرص",description:"(عوامل اقتصادية، قانونية، اجتماعية إيجابية، اتجاهات السوق...)",id:"opportunities",icon:cm,color:"bg-gradient-to-r from-blue-500 to-cyan-500"}),l.jsx(_a,{title:"التهديدات",description:"(عوامل خارجية قد تضر المشروع، منافسة شرسة، تغيرات قانونية...)",id:"threats",icon:xx,color:"bg-gradient-to-r from-yellow-500 to-amber-500"})]})}),l.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-500 to-orange-500 opacity-50"})]}),A2=()=>l.jsx(_2,{}),Gs=({title:n,questions:r,icon:s})=>l.jsxs("div",{className:"relative pl-8",children:[l.jsx("div",{className:"absolute top-1 right-0 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground",children:l.jsx(s,{className:"h-5 w-5"})}),l.jsxs("div",{className:"border-r-2 border-primary/20 pr-8 space-y-6 pb-8 last:border-r-0 last:pb-0",children:[l.jsx("h3",{className:"text-xl font-bold pt-1",children:n}),l.jsx("div",{className:"space-y-4",children:r.map((a,c)=>l.jsxs("div",{className:"space-y-2",children:[l.jsx(Ee,{children:a}),l.jsx(hn,{})]},c))})]})]}),O2=()=>l.jsxs(Kt,{className:"w-full bg-indigo-50 dark:bg-indigo-900/30",children:[l.jsx(Gt,{className:"border-b",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[l.jsx(hx,{className:"h-6 w-6 text-primary"}),"عناصر المزيج التسويقي – Marketing Mix (4Ps + 1)"]})}),l.jsxs(Xt,{className:"pt-6 space-y-2",children:[l.jsx(Gs,{title:"المنتج (Product)",icon:tx,questions:["ما هي المنتجات التي ستقدمها؟ وهل فيها تنوع؟","هل جودتها عالية؟ وهل ستقدم خدمات مصاحبة؟","هل ستميزها بعلامة تجارية وتغليف مميز؟"]}),l.jsx(Gs,{title:"السعر (Price)",icon:ux,questions:["ما هي أسعار البيع؟ وهل هناك أسعار جملة وتجزئة؟","هل ستقدم تخفيضات أو خصومات؟ وهل ستسمح بالبيع الآجل؟"]}),l.jsx(Gs,{title:"المكان (Place)",icon:dx,questions:["ما هي قنوات البيع والتوزيع؟ وما مدى التغطية السوقية؟","كيف سيكون الموقع والديكور؟ وكيف ستدير المخزون؟"]}),l.jsx(Gs,{title:"الترويج (Promotion)",icon:fx,questions:["كيف ستروج للمشروع؟ (إعلان، بيع شخصي، عروض...)","هل ستستخدم التسويق الإلكتروني ووسائل التواصل الاجتماعي؟"]}),l.jsx(Gs,{title:"الناس (People)",icon:wx,questions:["من سيساعدك في المشروع؟ (موظفين، أفراد من العائلة، أصدقاء)"]})]})]}),M2=()=>l.jsx(O2,{}),ts=x.forwardRef(({className:n,...r},s)=>l.jsx("div",{className:"relative w-full overflow-auto",children:l.jsx("table",{ref:s,className:Se("w-full caption-bottom text-sm",n),...r})}));ts.displayName="Table";const ns=x.forwardRef(({className:n,...r},s)=>l.jsx("thead",{ref:s,className:Se("[&_tr]:border-b",n),...r}));ns.displayName="TableHeader";const rs=x.forwardRef(({className:n,...r},s)=>l.jsx("tbody",{ref:s,className:Se("[&_tr:last-child]:border-0",n),...r}));rs.displayName="TableBody";const os=x.forwardRef(({className:n,...r},s)=>l.jsx("tfoot",{ref:s,className:Se("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",n),...r}));os.displayName="TableFooter";const Ke=x.forwardRef(({className:n,...r},s)=>l.jsx("tr",{ref:s,className:Se("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",n),...r}));Ke.displayName="TableRow";const qe=x.forwardRef(({className:n,...r},s)=>l.jsx("th",{ref:s,className:Se("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",n),...r}));qe.displayName="TableHead";const me=x.forwardRef(({className:n,...r},s)=>l.jsx("td",{ref:s,className:Se("p-4 align-middle [&:has([role=checkbox])]:pr-0",n),...r}));me.displayName="TableCell";const D2=x.forwardRef(({className:n,...r},s)=>l.jsx("caption",{ref:s,className:Se("mt-4 text-sm text-muted-foreground",n),...r}));D2.displayName="TableCaption";const vh=({title:n,example:r})=>{const s=[{id:1,item:"",unitPrice:"",quantity:"",total:""},{id:2,item:"",unitPrice:"",quantity:"",total:""},{id:3,item:"",unitPrice:"",quantity:"",total:""}];return l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-lg font-semibold",children:n}),l.jsxs("p",{className:"text-sm text-muted-foreground",children:["مثال: ",r]}),l.jsx("div",{className:"rounded-md border",children:l.jsxs(ts,{children:[l.jsx(ns,{children:l.jsxs(Ke,{children:[l.jsx(qe,{className:"w-[40%]",children:"البند"}),l.jsx(qe,{children:"سعر الوحدة"}),l.jsx(qe,{children:"عدد الوحدات"}),l.jsx(qe,{className:"text-left",children:"المجموع"})]})}),l.jsx(rs,{children:s.map(a=>l.jsxs(Ke,{children:[l.jsx(me,{children:l.jsx(ke,{placeholder:"اسم البند..."})}),l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0.00"})}),l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0"})}),l.jsx(me,{className:"text-left",children:l.jsx(ke,{readOnly:!0,placeholder:"0.00"})})]},a.id))}),l.jsx(os,{children:l.jsxs(Ke,{children:[l.jsx(me,{colSpan:3,className:"font-bold",children:"الإجمالي الكلي"}),l.jsx(me,{className:"text-left font-bold",children:"0.00"})]})})]})}),l.jsx(pn,{variant:"outline",size:"sm",children:"إضافة صف جديد"})]})},I2=()=>l.jsxs(Kt,{className:"w-full bg-stone-50 dark:bg-stone-900/30",children:[l.jsx(Gt,{className:"border-b",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[l.jsx(bx,{className:"h-6 w-6 text-primary"}),"مستلزمات الإنتاج للمشروع"]})}),l.jsxs(Xt,{className:"pt-6 space-y-8",children:[l.jsx(vh,{title:"1. الأجهزة والآلات والمعدات والأثاث المطلوبة عند التأسيس",example:"ماكينة خياطة – عجانة – مولينكس – طاولات – كراسي – أرفف ..."}),l.jsx(vh,{title:"2. المواد الخام المطلوبة لمدة شهر",example:"طحين – قماش – صبغات – خيوط – أعلاف ..."})]})]}),F2=()=>l.jsx(I2,{});var L2="Separator",yh="horizontal",z2=["horizontal","vertical"],Yg=x.forwardRef((n,r)=>{const{decorative:s,orientation:a=yh,...c}=n,f=U2(a)?a:yh,h=s?{role:"none"}:{"aria-orientation":f==="vertical"?f:void 0,role:"separator"};return l.jsx($e.div,{"data-orientation":f,...h,...c,ref:r})});Yg.displayName=L2;function U2(n){return z2.includes(n)}var Xg=Yg;const Bo=x.forwardRef(({className:n,orientation:r="horizontal",decorative:s=!0,...a},c)=>l.jsx(Xg,{ref:c,decorative:s,orientation:r,className:Se("shrink-0 bg-border",r==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",n),...a}));Bo.displayName=Xg.displayName;const Ys=({title:n})=>l.jsx("h3",{className:"text-xl font-bold text-primary mt-8 mb-4",children:n}),xh=({title:n,costs:r,onCostChange:s,totalLabel:a,showRawMaterials:c=!1})=>{const f=x.useMemo(()=>r.reduce((m,v)=>m+v.monthly,0),[r]),p=f*12,h=x.useMemo(()=>{var v;if(!c)return 0;const m=((v=r.find(w=>w.name==="المواد الخام"))==null?void 0:v.monthly)||0;return f-m},[r,f,c]);return l.jsxs("div",{className:"space-y-2",children:[l.jsx("h4",{className:"font-semibold",children:n}),l.jsx("div",{className:"rounded-md border",children:l.jsxs(ts,{children:[l.jsx(ns,{children:l.jsxs(Ke,{children:[l.jsx(qe,{className:"w-[50%]",children:"البند"}),l.jsx(qe,{children:"المبلغ الشهري"}),l.jsx(qe,{className:"text-left",children:"المبلغ السنوي"})]})}),l.jsx(rs,{children:r.map((m,v)=>l.jsxs(Ke,{children:[l.jsx(me,{children:m.name}),l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0.00",value:m.monthly||"",onChange:w=>s(v,w.target.value),className:"max-w-32"})}),l.jsx(me,{className:"text-left font-medium",children:(m.monthly*12).toFixed(2)})]},v))}),l.jsxs(os,{children:[l.jsxs(Ke,{className:"bg-muted/50",children:[l.jsx(me,{className:"font-bold",children:a}),l.jsx(me,{className:"font-bold",children:f.toFixed(2)}),l.jsx(me,{className:"text-left font-bold",children:p.toFixed(2)})]}),c&&l.jsxs(l.Fragment,{children:[l.jsxs(Ke,{children:[l.jsx(me,{className:"font-bold",children:"🔸 مجموع التكاليف المتغيرة (بدون المواد الخام)"}),l.jsx(me,{className:"font-bold",children:h.toFixed(2)}),l.jsx(me,{className:"text-left font-bold",children:(h*12).toFixed(2)})]}),l.jsxs(Ke,{className:"bg-muted/50",children:[l.jsx(me,{className:"font-bold",children:"🔹 مجموع التكاليف المتغيرة (مع المواد الخام)"}),l.jsx(me,{className:"font-bold",children:f.toFixed(2)}),l.jsx(me,{className:"text-left font-bold",children:p.toFixed(2)})]})]})]})]})})]})},wh=({title:n,items:r,totalLabel:s})=>l.jsxs("div",{className:"space-y-2",children:[l.jsx("h4",{className:"font-semibold",children:n}),l.jsx("div",{className:"rounded-md border",children:l.jsxs(ts,{children:[l.jsx(ns,{children:l.jsxs(Ke,{children:[l.jsx(qe,{children:"البند"}),l.jsx(qe,{className:"text-left w-48",children:"المبلغ بالدينار"})]})}),l.jsx(rs,{children:r.map(a=>l.jsxs(Ke,{children:[l.jsx(me,{children:a}),l.jsx(me,{className:"text-left",children:l.jsx(ke,{type:"number",placeholder:"0.00"})})]},a))}),l.jsx(os,{children:l.jsxs(Ke,{className:"bg-muted/50",children:[l.jsx(me,{className:"font-bold",children:s}),l.jsx(me,{className:"text-left font-bold",children:"0.00"})]})})]})})]}),$2=()=>{const{projectData:n,updateFinancialStudy:r}=po(),{fixedCosts:s,variableCosts:a,profitRows:c,annualSales:f,breakEvenInputs:p}=n.financialStudy,h=(O,F)=>{const $=[...s];$[O]={...$[O],monthly:Number(F)||0},r({fixedCosts:$})},m=(O,F)=>{const $=[...a];$[O]={...$[O],monthly:Number(F)||0},r({variableCosts:$})},v=(O,F,$)=>{const G=[...c];F==="name"?G[O][F]=$:G[O][F]=Number($)||0,r({profitRows:G})},w=()=>{const O=[...c,{id:Date.now(),name:"",units:0,costPerUnit:0,pricePerUnit:0}];r({profitRows:O})},b=O=>{const F=c.filter($=>$.id!==O);r({profitRows:F})},N=(O,F,$,G)=>{const J=JSON.parse(JSON.stringify(f));J[O].monthlyData[F][$]=Number(G)||0,r({annualSales:J})},y=(O,F)=>{const $={...p,[O]:Number(F)||0};r({breakEvenInputs:$})},P=x.useMemo(()=>s.reduce((O,F)=>O+F.monthly,0),[s]),k=x.useMemo(()=>a.reduce((O,F)=>O+F.monthly,0),[a]),j=P+k,_=x.useMemo(()=>c.reduce((O,F)=>{const $=F.units*F.costPerUnit,G=F.units*F.pricePerUnit;return O.totalCost+=$,O.totalRevenue+=G,O.totalProfit+=G-$,O},{totalCost:0,totalRevenue:0,totalProfit:0}),[c]),E=x.useMemo(()=>{const O=Array(12).fill(0);return f.forEach(F=>{F.monthlyData.forEach(($,G)=>{O[G]+=$.quantity*$.price})}),O},[f]),A=x.useMemo(()=>E.reduce((O,F)=>O+F,0),[E]),z=x.useMemo(()=>{const O=p.salePrice-p.variableCost;return O<=0?0:P/O},[P,p]);return l.jsxs(Kt,{className:"w-full bg-purple-50 dark:bg-purple-900/30",children:[l.jsx(Gt,{className:"border-b",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[l.jsx(nx,{className:"h-6 w-6 text-primary"}),"الدراسة المالية للمشروع"]})}),l.jsxs(Xt,{className:"pt-6",children:[l.jsx(Ys,{title:"💰 أولًا: رأس المال العامل – تكاليف المشروع التشغيلية"}),l.jsxs("div",{className:"space-y-6",children:[l.jsx(xh,{title:"1. التكاليف الثابتة",costs:s,onCostChange:h,totalLabel:"🔹 مجموع التكاليف الثابتة"}),l.jsx(xh,{title:"2. التكاليف المتغيرة",costs:a,onCostChange:m,totalLabel:"",showRawMaterials:!0}),l.jsxs("div",{className:"p-4 bg-primary text-primary-foreground rounded-lg flex justify-between items-center",children:[l.jsx("span",{className:"font-bold",children:"✅ إجمالي رأس المال العامل (التكاليف التشغيلية)"}),l.jsxs("div",{className:"text-right",children:[l.jsxs("p",{className:"font-bold text-lg",children:[j.toFixed(2)," دينار / شهريًا"]}),l.jsxs("p",{className:"font-bold text-sm",children:[(j*12).toFixed(2)," دينار / سنويًا"]})]})]})]}),l.jsx(Bo,{className:"my-10"}),l.jsx(Ys,{title:"🏗️ ثانيًا: النفقات التأسيسية – ما قبل التشغيل"}),l.jsx(wh,{title:"",items:["التسجيل والترخيص","توصيل الخدمات","خلو / تجهيز الموقع","تجهيز أولي","أخرى"],totalLabel:"مجموع نفقات ما قبل التشغيل"}),l.jsx(Bo,{className:"my-10"}),l.jsx(Ys,{title:"💼 ثالثًا: إجمالي رأس مال المشروع المتوقع"}),l.jsx(wh,{title:"",items:["رأس المال الثابت (تكاليف المشروع الرأسمالية – المعدات، الأجهزة...)","رأس المال العامل (لأول شهر فقط)"],totalLabel:"🔷 إجمالي رأس مال المشروع المتوقع (مجموع النفقات والتكاليف)"}),l.jsx(Bo,{className:"my-10"}),l.jsx(Ys,{title:"📊 رابعًا: حساب الربح والخسارة الشهري لكل منتج"}),l.jsx("div",{className:"rounded-md border",children:l.jsxs(ts,{children:[l.jsx(ns,{children:l.jsxs(Ke,{children:[l.jsx(qe,{className:"w-[20%]",children:"المنتج / الخدمة"}),l.jsx(qe,{children:"عدد الوحدات"}),l.jsx(qe,{children:"تكلفة الوحدة"}),l.jsx(qe,{children:"إجمالي التكلفة"}),l.jsx(qe,{children:"سعر بيع الوحدة"}),l.jsx(qe,{children:"إجمالي الإيراد"}),l.jsx(qe,{children:"إجمالي الربح"}),l.jsx(qe,{className:"w-[5%]"})]})}),l.jsx(rs,{children:c.map((O,F)=>{const $=O.units*O.costPerUnit,G=O.units*O.pricePerUnit,J=G-$;return l.jsxs(Ke,{children:[l.jsx(me,{children:l.jsx(ke,{placeholder:`منتج ${F+1}`,value:O.name,onChange:Z=>v(F,"name",Z.target.value)})}),l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0",value:O.units||"",onChange:Z=>v(F,"units",Z.target.value)})}),l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0.00",value:O.costPerUnit||"",onChange:Z=>v(F,"costPerUnit",Z.target.value)})}),l.jsx(me,{children:l.jsx(ke,{readOnly:!0,value:$.toFixed(2),className:"font-medium bg-muted"})}),l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0.00",value:O.pricePerUnit||"",onChange:Z=>v(F,"pricePerUnit",Z.target.value)})}),l.jsx(me,{children:l.jsx(ke,{readOnly:!0,value:G.toFixed(2),className:"font-medium bg-muted"})}),l.jsx(me,{children:l.jsx(ke,{readOnly:!0,value:J.toFixed(2),className:Se("font-bold bg-muted",J>0?"text-green-600":J<0?"text-red-600":"")})}),l.jsx(me,{children:c.length>1&&l.jsx(pn,{variant:"ghost",size:"icon",onClick:()=>b(O.id),children:l.jsx(yx,{className:"h-4 w-4 text-red-500"})})})]},O.id)})}),l.jsx(os,{children:l.jsxs(Ke,{className:"bg-primary/10 font-bold text-lg",children:[l.jsx(me,{colSpan:3,children:"الإجمالي الشهري"}),l.jsx(me,{children:_.totalCost.toFixed(2)}),l.jsx(me,{}),l.jsx(me,{children:_.totalRevenue.toFixed(2)}),l.jsx(me,{colSpan:2,className:Se(_.totalProfit>0?"text-green-700":_.totalProfit<0?"text-red-700":""),children:_.totalProfit.toFixed(2)})]})})]})}),l.jsx("div",{className:"mt-4 flex justify-start",children:l.jsxs(pn,{onClick:w,variant:"outline",children:[l.jsx(ax,{className:"ml-2 h-4 w-4"}),"إضافة منتج جديد"]})}),l.jsx(Bo,{className:"my-10"}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-xl font-bold text-white bg-red-800 p-2 rounded-md text-center",children:"🧾 تقدير الإيرادات / المبيعات لسنة"}),l.jsx("div",{className:"rounded-md border overflow-x-auto",children:l.jsxs(ts,{className:"min-w-[1200px]",children:[l.jsxs(ns,{children:[l.jsxs(Ke,{children:[l.jsx(qe,{rowSpan:2,className:"w-[200px] sticky right-0 bg-muted align-bottom text-center",children:"المبيعات / الإيرادات"}),l.jsx(qe,{colSpan:12,className:"text-center bg-muted/50",children:"أشهر السنة الأولى"}),l.jsx(qe,{rowSpan:2,className:"w-[120px] sticky left-0 bg-muted align-bottom text-center",children:"المجموع السنوي"})]}),l.jsx(Ke,{children:Array.from({length:12},(O,F)=>l.jsx(qe,{className:"text-center bg-muted/50",children:F+1},F))})]}),l.jsx(rs,{children:f.map((O,F)=>{const $=O.monthlyData.reduce((J,Z)=>J+Z.quantity*Z.price,0),G=O.monthlyData.reduce((J,Z)=>J+Z.quantity,0);return l.jsxs(x.Fragment,{children:[l.jsx(Ke,{className:"bg-primary/5",children:l.jsx(me,{colSpan:14,className:"font-bold sticky right-0 bg-primary/5",children:O.name})}),l.jsxs(Ke,{children:[l.jsx(me,{className:"font-medium sticky right-0 bg-background",children:"الكمية المتوقع بيعها"}),O.monthlyData.map((J,Z)=>l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0",className:"min-w-[65px] text-center",value:f[F].monthlyData[Z].quantity||"",onChange:pe=>N(F,Z,"quantity",pe.target.value)})},Z)),l.jsx(me,{className:"text-center font-bold bg-muted sticky left-0",children:G})]}),l.jsxs(Ke,{children:[l.jsx(me,{className:"font-medium sticky right-0 bg-background",children:"سعر البيع للوحدة"}),O.monthlyData.map((J,Z)=>l.jsx(me,{children:l.jsx(ke,{type:"number",placeholder:"0.00",className:"min-w-[65px] text-center",value:f[F].monthlyData[Z].price||"",onChange:pe=>N(F,Z,"price",pe.target.value)})},Z)),l.jsx(me,{className:"text-center font-bold bg-muted sticky left-0",children:"-"})]}),l.jsxs(Ke,{className:"bg-muted/20",children:[l.jsx(me,{className:"font-medium sticky right-0 bg-muted/20",children:"الإيراد (الكمية × السعر)"}),O.monthlyData.map((J,Z)=>l.jsx(me,{className:"text-center font-semibold",children:(J.quantity*J.price).toFixed(2)},Z)),l.jsx(me,{className:"text-center font-bold bg-muted/50 sticky left-0",children:$.toFixed(2)})]})]},O.id)})}),l.jsx(os,{children:l.jsxs(Ke,{className:"bg-primary text-primary-foreground",children:[l.jsx(me,{className:"font-bold text-lg sticky right-0 bg-primary",children:"مجموع الإيرادات الشهرية"}),E.map((O,F)=>l.jsx(me,{className:"text-center font-bold",children:O.toFixed(2)},F)),l.jsx(me,{className:"text-center font-bold text-lg sticky left-0 bg-primary",children:A.toFixed(2)})]})})]})})]}),l.jsx(Bo,{className:"my-10"}),l.jsx(Ys,{title:"📍 خامسًا: حساب نقطة التعادل (Break-even Point)"}),l.jsx("p",{className:"text-sm text-muted-foreground",children:"هي عدد الوحدات التي يجب بيعها لتغطية جميع التكاليف (الثابتة + المتغيرة)، بحيث لا يوجد ربح ولا خسارة."}),l.jsx("p",{className:"text-sm text-muted-foreground font-mono p-2 bg-muted rounded-md",children:"نقطة التعادل (بالوحدات) = التكاليف الثابتة الشهرية ÷ (سعر بيع الوحدة – تكلفة الوحدة المتغيرة)"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[l.jsxs("div",{className:"space-y-2",children:[l.jsx(Ee,{children:"التكاليف الثابتة الشهرية"}),l.jsx(ke,{type:"number",value:P.toFixed(2),readOnly:!0,disabled:!0})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx(Ee,{children:"متوسط سعر بيع الوحدة"}),l.jsx(ke,{type:"number",placeholder:"0.00",value:p.salePrice||"",onChange:O=>y("salePrice",O.target.value)})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx(Ee,{children:"متوسط تكلفة الوحدة المتغيرة"}),l.jsx(ke,{type:"number",placeholder:"0.00",value:p.variableCost||"",onChange:O=>y("variableCost",O.target.value)})]}),l.jsxs("div",{className:"p-4 bg-primary text-primary-foreground rounded-lg flex justify-between items-center md:col-span-2",children:[l.jsx("span",{className:"font-bold",children:"🔹 نقطة التعادل (عدد الوحدات)"}),l.jsxs("span",{className:"font-bold text-lg",children:[z.toFixed(2)," وحدة"]})]})]})]})]})},B2=()=>l.jsx($2,{}),V2=()=>{const{projectData:n,saveProject:r,resetProject:s}=po(),a=()=>{r(),Ze({title:"تم الحفظ بنجاح!",description:"تم حفظ جميع بيانات المشروع في التخزين المحلي"})},c=()=>{Ze({title:"قريباً",description:"ميزة تصدير PDF ستكون متاحة قريباً",variant:"destructive"})},f=()=>{Ze({title:"قريباً",description:"ميزة تصدير Excel ستكون متاحة قريباً",variant:"destructive"})},p=()=>{confirm("هل أنت متأكد من أنك تريد مسح جميع البيانات؟")&&s()},h=m=>new Intl.DateTimeFormat("ar-SA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(m);return l.jsxs("div",{className:"space-y-8",children:[l.jsxs("div",{className:"text-center p-8 space-y-8 relative",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/10 via-blue-400/10 to-purple-400/10 rounded-3xl animate-pulse"}),l.jsx("div",{className:"flex justify-center relative z-10",children:l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-xl opacity-50 animate-pulse"}),l.jsx("div",{className:"relative w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-3d animate-glow",children:l.jsx(Nc,{className:"h-12 w-12 text-white"})}),l.jsx("div",{className:"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-ping"}),l.jsx("div",{className:"absolute -bottom-2 -left-2 w-3 h-3 bg-blue-400 rounded-full animate-ping",style:{animationDelay:"0.5s"}}),l.jsx("div",{className:"absolute top-2 -left-4 w-2 h-2 bg-purple-400 rounded-full animate-ping",style:{animationDelay:"1s"}})]})}),l.jsxs("div",{className:"space-y-4 relative z-10",children:[l.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gradient-primary animate-glow",children:"🎉 تهانينا! 🎉"}),l.jsx("h3",{className:"text-2xl md:text-3xl font-bold text-green-600 dark:text-green-400",children:"لقد أكملت خطة المشروع بنجاح!"}),l.jsx("p",{className:"text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:"عمل رائع! لقد قمت بإنشاء خطة عمل شاملة ومتكاملة لمشروعك. يمكنك الآن حفظ خطة عملك أو تصديرها كملف PDF أو Excel للمشاركة والعرض."})]}),l.jsx("div",{className:"flex justify-center relative z-10",children:l.jsx("div",{className:"w-32 h-1 bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 rounded-full shadow-lg animate-pulse"})})]}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[l.jsxs(Kt,{className:"card-3d shadow-3d-hover border-glow bg-gradient-to-br from-blue-50/90 to-indigo-50/90 dark:from-blue-900/30 dark:to-indigo-900/30 backdrop-blur-sm relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400/5 to-indigo-400/5"}),l.jsx(Gt,{className:"relative z-10 border-b border-blue-200/50 dark:border-blue-700/50",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-xl font-bold",children:[l.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg",children:l.jsx(dm,{className:"h-5 w-5 text-white"})}),l.jsx("span",{className:"text-gradient-primary",children:"ملخص المشروع"})]})}),l.jsxs(Xt,{className:"space-y-4 pt-6 relative z-10",children:[l.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30",children:[l.jsx("strong",{className:"text-blue-700 dark:text-blue-300",children:"اسم المشروع:"}),l.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:n.projectDescription.projectName||"غير محدد"})]}),l.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30",children:[l.jsx("strong",{className:"text-blue-700 dark:text-blue-300",children:"صاحب المشروع:"}),l.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:n.personalInfo.ownerName||"غير محدد"})]}),l.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30",children:[l.jsx("strong",{className:"text-blue-700 dark:text-blue-300",children:"موقع المشروع:"}),l.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:n.projectDescription.projectLocation||"غير محدد"})]})]}),l.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 opacity-50"})]}),l.jsxs(Kt,{className:"card-3d shadow-3d-hover border-glow bg-gradient-to-br from-green-50/90 to-emerald-50/90 dark:from-green-900/30 dark:to-emerald-900/30 backdrop-blur-sm relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-emerald-400/5"}),l.jsx(Gt,{className:"relative z-10 border-b border-green-200/50 dark:border-green-700/50",children:l.jsxs(Yt,{className:"flex items-center gap-3 text-xl font-bold",children:[l.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg",children:l.jsx(rx,{className:"h-5 w-5 text-white"})}),l.jsx("span",{className:"text-gradient-primary",children:"معلومات الحفظ"})]})}),l.jsxs(Xt,{className:"space-y-4 pt-6 relative z-10",children:[l.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-green-200/30 dark:border-green-700/30",children:[l.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"آخر تحديث:"}),l.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:h(n.lastUpdated)})]}),l.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-green-200/30 dark:border-green-700/30",children:[l.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"الخطوة الحالية:"}),l.jsxs("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:[n.currentStep+1," من 7"]})]}),l.jsxs("div",{className:"p-3 rounded-lg bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 border border-green-200 dark:border-green-700/50",children:[l.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"حالة الإكمال:"}),l.jsx("span",{className:"mr-2 text-green-600 dark:text-green-400 font-bold",children:"✅ مكتمل 100%"})]})]}),l.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500 opacity-50"})]})]}),l.jsxs("div",{className:"flex flex-wrap justify-center gap-6 pt-8",children:[l.jsxs(pn,{size:"lg",variant:"outline",onClick:a,className:"btn-3d shadow-3d border-2 border-blue-200 dark:border-blue-700 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-blue-50 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300",children:[l.jsx(cx,{className:"ml-2 h-5 w-5"}),"حفظ التقدم"]}),l.jsxs(pn,{size:"lg",onClick:c,className:"btn-3d shadow-3d bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white border-0 relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),l.jsxs("span",{className:"relative flex items-center",children:[l.jsx(um,{className:"ml-2 h-5 w-5"}),"تصدير PDF"]})]}),l.jsxs(pn,{size:"lg",onClick:f,className:"btn-3d shadow-3d bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),l.jsxs("span",{className:"relative flex items-center",children:[l.jsx(px,{className:"ml-2 h-5 w-5"}),"تصدير Excel"]})]}),l.jsxs(pn,{size:"lg",variant:"destructive",onClick:p,className:"btn-3d shadow-3d bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white border-0 relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),l.jsx("span",{className:"relative flex items-center",children:"🗑️ مسح البيانات"})]})]})]})},no=[{id:1,title:"المعلومات الشخصية ووصف المشروع",component:l.jsx(E2,{})},{id:2,title:"دراسة السوق والمنافسين",component:l.jsx(R2,{})},{id:3,title:"التحليل الرباعي (SWOT)",component:l.jsx(A2,{})},{id:4,title:"المزيج التسويقي (4Ps + 1)",component:l.jsx(M2,{})},{id:5,title:"مستلزمات الإنتاج",component:l.jsx(F2,{})},{id:6,title:"الدراسة المالية",component:l.jsx(B2,{})},{id:7,title:"حفظ وتصدير",component:l.jsx(V2,{})}],H2=()=>{const{projectData:n,setCurrentStep:r,isDataValid:s,saveProject:a}=po(),c=n.currentStep,f=()=>{if(!s(c)){Ze({title:"بيانات غير مكتملة",description:"يرجى إكمال البيانات المطلوبة قبل الانتقال للخطوة التالية",variant:"destructive"});return}c<no.length-1&&(r(c+1),Ze({title:"تم الحفظ",description:"تم حفظ بيانات الخطوة الحالية تلقائياً"}))},p=()=>{c>0&&r(c-1)},h=()=>{if(!s(c)){Ze({title:"بيانات غير مكتملة",description:"يرجى إكمال البيانات المطلوبة قبل الإنهاء",variant:"destructive"});return}a(),Ze({title:"تم إنهاء المشروع بنجاح!",description:"تم حفظ جميع بيانات خطة العمل"})},m=no[c].component;return l.jsxs("div",{className:"w-full max-w-6xl mx-auto space-y-8 relative",children:[l.jsx("div",{className:"absolute -inset-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-2xl opacity-30"}),l.jsx("div",{className:"relative",children:l.jsx(Gj,{currentStep:c,totalSteps:no.length})}),l.jsxs(Kt,{className:"card-3d shadow-3d-hover border-glow relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-card"}),l.jsxs(Gt,{className:"relative z-10 border-b border-white/20 dark:border-slate-700/50",children:[l.jsxs(Yt,{className:"text-gradient-primary text-2xl md:text-3xl font-bold flex items-center gap-3",children:[l.jsx("div",{className:"w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full shadow-lg"}),no[c].title]}),l.jsxs(vg,{className:"text-lg text-gray-600 dark:text-gray-300 mt-2",children:["الخطوة ",c+1," من ",no.length," • ",Math.round((c+1)/no.length*100),"% مكتمل"]})]}),l.jsx(Xt,{className:"relative z-10 p-6 md:p-8",children:l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute -inset-2 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl"}),l.jsx("div",{className:"relative",children:m})]})}),l.jsxs(yg,{className:"relative z-10 flex justify-between items-center p-6 md:p-8 border-t border-white/20 dark:border-slate-700/50 bg-gradient-to-r from-white/50 to-gray-50/50 dark:from-slate-800/50 dark:to-slate-700/50",children:[l.jsxs(pn,{onClick:p,disabled:c===0,variant:"outline",className:"btn-3d shadow-3d border-2 border-white/30 dark:border-slate-600/30 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[l.jsx(ex,{className:"ml-2 h-4 w-4"}),"السابق"]}),l.jsx("div",{className:"flex items-center gap-2",children:!s(c)&&l.jsxs("div",{className:"flex items-center gap-2 px-3 py-2 rounded-full bg-amber-100 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700/50 shadow-lg backdrop-blur-sm",children:[l.jsx(lm,{className:"h-4 w-4 text-amber-600 dark:text-amber-400 animate-pulse"}),l.jsx("span",{className:"text-sm font-medium text-amber-700 dark:text-amber-300",children:"بيانات غير مكتملة"})]})}),c<no.length-1?l.jsxs(pn,{onClick:f,className:"btn-3d shadow-3d bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),l.jsxs("span",{className:"relative flex items-center",children:["التالي",l.jsx(Zy,{className:"mr-2 h-4 w-4"})]})]}):l.jsxs(pn,{onClick:h,className:"btn-3d shadow-3d bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 relative overflow-hidden animate-glow",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),l.jsxs("span",{className:"relative flex items-center",children:["إنهاء وحفظ",l.jsx(Nc,{className:"mr-2 h-4 w-4"})]})]})]})]})]})},W2=()=>l.jsx("div",{className:"p-4 text-center",children:l.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400 font-medium",children:"saif aldulaimi"})}),Q2=()=>l.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 mb-8",children:[l.jsxs("div",{className:"relative group",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300 animate-pulse"}),l.jsxs("div",{className:"relative w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 p-5 rounded-full shadow-3d group-hover:scale-110 transition-transform duration-300",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-full"}),l.jsx(cm,{className:"h-10 w-10 text-white relative z-10 group-hover:animate-pulse"}),l.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"}),l.jsx("div",{className:"absolute -bottom-1 -left-1 w-2 h-2 bg-blue-300 rounded-full animate-ping",style:{animationDelay:"0.5s"}})]})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("span",{className:"text-3xl md:text-4xl font-bold text-gradient-primary tracking-tight block",children:"خطة مشروعي"}),l.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 block",children:"Business Plan Builder"})]})]}),q2=()=>l.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 bg-animated opacity-5"}),l.jsx("div",{className:"absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"}),l.jsx("div",{className:"absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float",style:{animationDelay:"2s"}}),l.jsx("div",{className:"absolute -bottom-32 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float",style:{animationDelay:"4s"}}),l.jsxs("div",{className:"container mx-auto py-10 px-4 relative z-10",children:[l.jsxs("header",{className:"text-center mb-12",children:[l.jsxs("div",{className:"w-full max-w-4xl mx-auto mb-8 relative",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-20"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1556740738-b6a63e27c4df?q=80&w=2070&auto=format&fit=crop",alt:"Business Planning Session",className:"relative rounded-2xl shadow-3d object-cover w-full h-64 border-2 border-white/20 backdrop-blur-sm"}),l.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"})]}),l.jsx("div",{className:"mb-8 animate-float",children:l.jsx(Q2,{})}),l.jsx("h1",{className:"text-5xl md:text-6xl font-extrabold tracking-tight mb-6 text-gradient-primary animate-glow",children:"حوّل فكرتك إلى مشروع ناجح"}),l.jsx("p",{className:"text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"أداة متكاملة لمساعدتك على بناء خطة عمل احترافية خطوة بخطوة مع تقنيات حديثة وتصميم أنيق."}),l.jsx("div",{className:"mt-8 flex justify-center",children:l.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg"})})]}),l.jsxs("main",{className:"relative",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-3xl"}),l.jsx(H2,{})]}),l.jsx("footer",{className:"mt-16 text-center relative",children:l.jsx("div",{className:"inline-block p-4 rounded-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-3d border border-white/20",children:l.jsx(W2,{})})})]})]}),K2=()=>{const n=Uc(),r=Sj();x.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",n.pathname),(n.pathname==="/"||n.pathname==="")&&r("/",{replace:!0})},[n.pathname,r]);const s=()=>{r("/",{replace:!0})};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900",children:l.jsxs("div",{className:"text-center p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl shadow-3d border border-white/20",children:[l.jsx("h1",{className:"text-6xl font-bold mb-4 text-gradient-primary",children:"404"}),l.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-6",children:"عذراً! الصفحة غير موجودة"}),l.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-6",children:["المسار المطلوب: ",n.pathname]}),l.jsx("button",{onClick:s,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl",children:"العودة إلى الصفحة الرئيسية"})]})})},G2=new qb,Y2=()=>(x.useEffect(()=>{console.log("App component mounted"),console.log("Current URL:",window.location.href),console.log("Current hash:",window.location.hash)},[]),l.jsx(Gb,{client:G2,children:l.jsx(qj,{children:l.jsxs(kb,{children:[l.jsx(ow,{}),l.jsx(Iw,{}),l.jsx(Wj,{children:l.jsxs(Bj,{children:[l.jsx(hc,{path:"/",element:l.jsx(q2,{})}),l.jsx(hc,{path:"*",element:l.jsx(K2,{})})]})})]})})}));Zv.createRoot(document.getElementById("root")).render(l.jsx(Y2,{}));

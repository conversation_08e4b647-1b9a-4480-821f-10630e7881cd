@echo off
echo 📦 بناء مثبت تطبيق خطة مشروعي...
echo.

REM التحقق من Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

REM التحقق من التبعيات
if not exist node_modules (
    echo 📥 تثبيت التبعيات...
    npm install
    if errorlevel 1 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
)

echo ✅ التبعيات متوفرة

REM تنظيف الملفات السابقة
echo 🧹 تنظيف الملفات السابقة...
if exist dist rmdir /s /q dist
if exist dist-electron rmdir /s /q dist-electron
if exist dist-app rmdir /s /q dist-app

REM بناء تطبيق React
echo ⚛️ بناء تطبيق React...
npm run build
if errorlevel 1 (
    echo ❌ فشل في بناء تطبيق React
    pause
    exit /b 1
)

echo ✅ تم بناء تطبيق React بنجاح

REM بناء ملفات Electron
echo ⚡ بناء ملفات Electron...
npx tsc -p electron/tsconfig.json
if errorlevel 1 (
    echo ❌ فشل في بناء ملفات Electron
    pause
    exit /b 1
)

echo ✅ تم بناء ملفات Electron بنجاح

REM بناء المثبت
echo 📦 بناء المثبت النهائي...
echo 📝 ملاحظة: قد يستغرق هذا عدة دقائق...
npx electron-builder --win
if errorlevel 1 (
    echo ❌ فشل في بناء المثبت
    echo 💡 تأكد من اتصالك بالإنترنت لتحميل Electron
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المثبت بنجاح! 🎉
echo 📁 يمكنك العثور على المثبت في مجلد: dist-app
echo.

REM عرض الملفات المبنية
if exist dist-app (
    echo 📋 الملفات المبنية:
    dir dist-app /b
    echo.
    echo 💾 ملف المثبت: dist-app\*.exe
    echo 📂 مجلد التطبيق: dist-app\win-unpacked\
)

echo.
echo 🚀 يمكنك الآن توزيع ملف المثبت لتنصيب التطبيق على أي جهاز Windows
pause

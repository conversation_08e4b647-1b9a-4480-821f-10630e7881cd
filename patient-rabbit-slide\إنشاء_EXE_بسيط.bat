@echo off
echo 🎯 إنشاء مثبت EXE احترافي...
echo.

echo 📋 الطرق المتاحة لإنشاء مثبت EXE:
echo.
echo 1. 🔥 NSIS (الأفضل - مجاني)
echo 2. 🎨 Inno Setup (جميل - مجاني)  
echo 3. 🔄 Bat to EXE Converter (بسيط)
echo 4. 💎 Advanced Installer (احترافي)
echo.

REM التحقق من NSIS
where makensis >nul 2>nul
if %ERRORLEVEL% EQ 0 (
    echo ✅ NSIS متوفر - يمكن إنشاء EXE الآن!
    set /p use_nsis="هل تريد استخدام NSIS؟ (y/n): "
    if /i "%use_nsis%"=="y" (
        echo 🔨 بناء مثبت NSIS...
        makensis installer-final.nsi
        if exist "خطة-مشروعي-مثبت-1.0.0.exe" (
            echo ✅ تم إنشاء مثبت EXE بنجاح!
            echo 📁 الملف: خطة-مشروعي-مثبت-1.0.0.exe
            explorer /select,"خطة-مشروعي-مثبت-1.0.0.exe"
            goto end
        )
    )
)

REM التحقق من Inno Setup
where iscc >nul 2>nul
if %ERRORLEVEL% EQ 0 (
    echo ✅ Inno Setup متوفر - يمكن إنشاء EXE الآن!
    set /p use_inno="هل تريد استخدام Inno Setup؟ (y/n): "
    if /i "%use_inno%"=="y" (
        echo 🔨 بناء مثبت Inno Setup...
        iscc installer-inno.iss
        if exist "خطة-مشروعي-مثبت-inno-1.0.0.exe" (
            echo ✅ تم إنشاء مثبت EXE بنجاح!
            echo 📁 الملف: خطة-مشروعي-مثبت-inno-1.0.0.exe
            explorer /select,"خطة-مشروعي-مثبت-inno-1.0.0.exe"
            goto end
        )
    )
)

echo.
echo ⚠️ لم يتم العثور على أدوات إنشاء EXE مثبتة
echo.
echo 📥 يرجى تحميل وتثبيت إحدى الأدوات التالية:
echo.

echo 🔥 NSIS (الأفضل):
echo    📎 الرابط: https://nsis.sourceforge.io/Download
echo    💡 أو استخدم: choco install nsis
echo    ✅ مجاني، صغير الحجم، احترافي
echo.

echo 🎨 Inno Setup:
echo    📎 الرابط: https://jrsoftware.org/isinfo.php
echo    ✅ مجاني، واجهة جميلة، سهل الاستخدام
echo.

echo 🔄 Bat to EXE Converter:
echo    📎 الرابط: https://bat-to-exe-converter-x64.en.softonic.com/
echo    ✅ بسيط جداً، تحويل مباشر
echo.

echo 💎 Advanced Installer:
echo    📎 الرابط: https://www.advancedinstaller.com/
echo    ✅ احترافي جداً، مميزات متقدمة
echo.

echo 🎯 التوصية:
echo    1. حمل NSIS (الأسرع والأصغر)
echo    2. ثبته
echo    3. شغل هذا السكريبت مرة أخرى
echo.

echo 🔧 أو يمكنك استخدام المثبت الحالي:
echo    📁 مثبت_خطة_مشروعي.bat (يعمل بشكل ممتاز)
echo.

:end
echo.
echo 💡 نصائح:
echo    - مثبت BAT يعمل بشكل ممتاز ولا يحتاج EXE
echo    - EXE يبدو أكثر احترافية للمستخدمين
echo    - NSIS ينتج أصغر حجم ملف
echo    - Inno Setup له واجهة أجمل
echo.
pause

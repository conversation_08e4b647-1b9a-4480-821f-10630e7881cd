import { cn } from "@/lib/utils";
import { useProject } from "@/contexts/ProjectContext";
import { CheckCircle, Circle, AlertCircle } from "lucide-react";

interface FormProgressProps {
  currentStep: number;
  totalSteps: number;
}

export const FormProgress = ({ currentStep, totalSteps }: FormProgressProps) => {
  const { isDataValid } = useProject();
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  const stepNames = [
    "المعلومات الشخصية",
    "دراسة السوق",
    "تحليل SWOT",
    "المزيج التسويقي",
    "مستلزمات الإنتاج",
    "الدراسة المالية",
    "الإنهاء والحفظ"
  ];

  return (
    <div className="w-full space-y-6 p-6 rounded-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-3d border border-white/20 dark:border-slate-700/30">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg animate-pulse"></div>
          <span className="text-lg font-bold text-gradient-primary">التقدم</span>
        </div>
        <div className="px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-200 dark:border-blue-700/50">
          <span className="text-sm font-bold text-blue-700 dark:text-blue-300">
            الخطوة {currentStep + 1}/{totalSteps}
          </span>
        </div>
      </div>

      <div className="relative">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-700 ease-out shadow-lg relative overflow-hidden"
            style={{ width: `${progressPercentage}%` }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
          </div>
        </div>
        <div
          className="absolute top-0 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-blue-500 transition-all duration-700 ease-out -mt-0.5"
          style={{ left: `calc(${progressPercentage}% - 8px)` }}
        >
          <div className="w-2 h-2 bg-blue-500 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
      </div>

      {/* عرض حالة الخطوات */}
      <div className="hidden lg:flex justify-between items-center gap-2">
        {stepNames.map((stepName, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const isValid = isDataValid(index);

          return (
            <div key={index} className="flex flex-col items-center space-y-2 flex-1">
              <div className={cn(
                "relative flex items-center justify-center w-12 h-12 rounded-full border-3 transition-all duration-500 shadow-lg",
                isCompleted ? "bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 text-white shadow-green-200 dark:shadow-green-900/50 scale-110" :
                isCurrent ? (isValid ? "bg-gradient-to-r from-blue-500 to-purple-500 border-blue-400 text-white shadow-blue-200 dark:shadow-blue-900/50 scale-110 animate-glow" : "bg-gradient-to-r from-amber-500 to-orange-500 border-amber-400 text-white shadow-amber-200 dark:shadow-amber-900/50 scale-110 animate-pulse") :
                "bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
              )}>
                {/* تأثير الإضاءة الداخلية */}
                <div className="absolute inset-0 bg-gradient-to-t from-white/20 to-transparent rounded-full"></div>

                {isCompleted ? (
                  <CheckCircle className="w-6 h-6 relative z-10" />
                ) : isCurrent && !isValid ? (
                  <AlertCircle className="w-6 h-6 relative z-10" />
                ) : (
                  <div className="w-3 h-3 bg-current rounded-full relative z-10"></div>
                )}

                {/* تأثير النبضة للخطوة الحالية */}
                {isCurrent && (
                  <div className="absolute inset-0 rounded-full bg-current opacity-20 animate-ping"></div>
                )}
              </div>

              <span className={cn(
                "text-center text-xs font-medium leading-tight px-2 py-1 rounded-lg transition-all duration-300",
                isCurrent ? "text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 shadow-sm" :
                isCompleted ? "text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30" :
                "text-gray-500 dark:text-gray-400"
              )}>
                {stepName}
              </span>

              {/* خط الاتصال */}
              {index < stepNames.length - 1 && (
                <div className="absolute top-6 left-1/2 w-full h-0.5 -z-10">
                  <div className={cn(
                    "h-full transition-all duration-500",
                    isCompleted ? "bg-gradient-to-r from-green-400 to-emerald-400" :
                    index < currentStep ? "bg-gradient-to-r from-blue-400 to-purple-400" :
                    "bg-gray-300 dark:bg-gray-600"
                  )}></div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
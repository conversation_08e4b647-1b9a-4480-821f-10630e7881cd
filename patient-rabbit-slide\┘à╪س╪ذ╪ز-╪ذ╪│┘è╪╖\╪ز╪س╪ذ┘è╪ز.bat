@echo off 
echo 🚀 مثبت خطة مشروعي البسيط 
echo. 
echo 📁 اختر مجلد التثبيت: 
set /p install_dir="المسار (افتراضي: C:\خطة مشروعي): " 
if "%install_dir%"=="" set install_dir=C:\خطة مشروعي 
 
echo 📂 إنشاء مجلد التثبيت... 
if not exist "%install_dir%" mkdir "%install_dir%" 
 
echo 📋 نسخ ملفات التطبيق... 
xcopy "app" "%install_dir%" /E /I /Q 
 
echo 🔗 إنشاء اختصار سطح المكتب... 
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\خطة مشروعي.lnk'); $Shortcut.TargetPath = '%install_dir%\run-app.bat'; $Shortcut.IconLocation = '%install_dir%\icon.ico'; $Shortcut.Save()" 
 
echo ✅ تم التثبيت بنجاح! 
echo 🚀 يمكنك الآن تشغيل التطبيق من سطح المكتب 
pause 

const { spawn } = require('child_process');
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 بدء تطوير تطبيق Electron...');

// تنظيف مجلد dist-electron
if (fs.existsSync('dist-electron')) {
  fs.rmSync('dist-electron', { recursive: true, force: true });
}

// بناء ملفات Electron للتطوير
console.log('⚡ بناء ملفات Electron...');
try {
  execSync('npx tsc -p electron/tsconfig.json', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ خطأ في بناء ملفات Electron:', error.message);
  process.exit(1);
}

// تشغيل Vite dev server
console.log('⚛️ تشغيل خادم التطوير...');
const viteProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  shell: true
});

let viteReady = false;

viteProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(output);
  
  // انتظار حتى يصبح خادم Vite جاهزاً
  if (output.includes('Local:') && !viteReady) {
    viteReady = true;
    
    // تشغيل Electron بعد تأخير قصير
    setTimeout(() => {
      console.log('⚡ تشغيل Electron...');
      
      const electronProcess = spawn('npx', ['electron', '.'], {
        stdio: 'inherit',
        shell: true,
        env: {
          ...process.env,
          ELECTRON_RENDERER_URL: 'http://localhost:5173'
        }
      });

      electronProcess.on('close', () => {
        console.log('🔚 إغلاق التطبيق...');
        viteProcess.kill();
        process.exit(0);
      });

    }, 2000);
  }
});

viteProcess.stderr.on('data', (data) => {
  console.error(data.toString());
});

viteProcess.on('close', (code) => {
  console.log(`🔚 انتهى خادم التطوير بالكود: ${code}`);
});

// معالجة إشارات الإغلاق
process.on('SIGINT', () => {
  console.log('🛑 إيقاف التطوير...');
  viteProcess.kill();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('🛑 إنهاء التطوير...');
  viteProcess.kill();
  process.exit(0);
});

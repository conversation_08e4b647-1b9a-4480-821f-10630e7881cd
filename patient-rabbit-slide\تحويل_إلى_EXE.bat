@echo off
echo 🔄 تحويل المثبت إلى ملف EXE...
echo.

REM التحقق من وجود أدوات التحويل
echo 🔍 البحث عن أدوات التحويل...

REM الطريقة الأولى: استخدام PowerShell لإنشاء EXE
echo 📦 إنشاء مثبت EXE باستخدام PowerShell...

REM إنشاء سكريبت PowerShell للمثبت
echo 📝 إنشاء سكريبت PowerShell...
echo # مثبت خطة مشروعي - PowerShell > installer.ps1
echo Add-Type -AssemblyName System.Windows.Forms >> installer.ps1
echo Add-Type -AssemblyName System.Drawing >> installer.ps1
echo. >> installer.ps1
echo # واجهة المثبت >> installer.ps1
echo $form = New-Object System.Windows.Forms.Form >> installer.ps1
echo $form.Text = "مثبت خطة مشروعي v1.0.0" >> installer.ps1
echo $form.Size = New-Object System.Drawing.Size(500,400) >> installer.ps1
echo $form.StartPosition = "CenterScreen" >> installer.ps1
echo $form.FormBorderStyle = "FixedDialog" >> installer.ps1
echo $form.MaximizeBox = $false >> installer.ps1
echo. >> installer.ps1
echo # عنوان >> installer.ps1
echo $label = New-Object System.Windows.Forms.Label >> installer.ps1
echo $label.Location = New-Object System.Drawing.Point(50,20) >> installer.ps1
echo $label.Size = New-Object System.Drawing.Size(400,30) >> installer.ps1
echo $label.Text = "مرحباً بك في مثبت خطة مشروعي" >> installer.ps1
echo $label.Font = New-Object System.Drawing.Font("Arial",12,[System.Drawing.FontStyle]::Bold) >> installer.ps1
echo $form.Controls.Add($label) >> installer.ps1
echo. >> installer.ps1
echo # وصف >> installer.ps1
echo $desc = New-Object System.Windows.Forms.Label >> installer.ps1
echo $desc.Location = New-Object System.Drawing.Point(50,60) >> installer.ps1
echo $desc.Size = New-Object System.Drawing.Size(400,40) >> installer.ps1
echo $desc.Text = "تطبيق متكامل لإنشاء خطط الأعمال باللغة العربية`nالمطور: saif aldulaimi" >> installer.ps1
echo $form.Controls.Add($desc) >> installer.ps1
echo. >> installer.ps1
echo # مسار التثبيت >> installer.ps1
echo $pathLabel = New-Object System.Windows.Forms.Label >> installer.ps1
echo $pathLabel.Location = New-Object System.Drawing.Point(50,120) >> installer.ps1
echo $pathLabel.Size = New-Object System.Drawing.Size(100,20) >> installer.ps1
echo $pathLabel.Text = "مجلد التثبيت:" >> installer.ps1
echo $form.Controls.Add($pathLabel) >> installer.ps1
echo. >> installer.ps1
echo $pathBox = New-Object System.Windows.Forms.TextBox >> installer.ps1
echo $pathBox.Location = New-Object System.Drawing.Point(50,145) >> installer.ps1
echo $pathBox.Size = New-Object System.Drawing.Size(300,20) >> installer.ps1
echo $pathBox.Text = "C:\Program Files\خطة مشروعي" >> installer.ps1
echo $form.Controls.Add($pathBox) >> installer.ps1
echo. >> installer.ps1
echo # زر تصفح >> installer.ps1
echo $browseBtn = New-Object System.Windows.Forms.Button >> installer.ps1
echo $browseBtn.Location = New-Object System.Drawing.Point(360,144) >> installer.ps1
echo $browseBtn.Size = New-Object System.Drawing.Size(80,22) >> installer.ps1
echo $browseBtn.Text = "تصفح..." >> installer.ps1
echo $browseBtn.Add_Click({ >> installer.ps1
echo     $folderBrowser = New-Object System.Windows.Forms.FolderBrowserDialog >> installer.ps1
echo     if($folderBrowser.ShowDialog() -eq "OK"){ >> installer.ps1
echo         $pathBox.Text = $folderBrowser.SelectedPath + "\خطة مشروعي" >> installer.ps1
echo     } >> installer.ps1
echo }) >> installer.ps1
echo $form.Controls.Add($browseBtn) >> installer.ps1
echo. >> installer.ps1
echo # شريط التقدم >> installer.ps1
echo $progressBar = New-Object System.Windows.Forms.ProgressBar >> installer.ps1
echo $progressBar.Location = New-Object System.Drawing.Point(50,200) >> installer.ps1
echo $progressBar.Size = New-Object System.Drawing.Size(390,20) >> installer.ps1
echo $progressBar.Style = "Continuous" >> installer.ps1
echo $form.Controls.Add($progressBar) >> installer.ps1
echo. >> installer.ps1
echo # نص الحالة >> installer.ps1
echo $statusLabel = New-Object System.Windows.Forms.Label >> installer.ps1
echo $statusLabel.Location = New-Object System.Drawing.Point(50,230) >> installer.ps1
echo $statusLabel.Size = New-Object System.Drawing.Size(390,20) >> installer.ps1
echo $statusLabel.Text = "جاهز للتثبيت..." >> installer.ps1
echo $form.Controls.Add($statusLabel) >> installer.ps1
echo. >> installer.ps1
echo # زر التثبيت >> installer.ps1
echo $installBtn = New-Object System.Windows.Forms.Button >> installer.ps1
echo $installBtn.Location = New-Object System.Drawing.Point(250,280) >> installer.ps1
echo $installBtn.Size = New-Object System.Drawing.Size(80,30) >> installer.ps1
echo $installBtn.Text = "تثبيت" >> installer.ps1
echo $installBtn.BackColor = [System.Drawing.Color]::LightGreen >> installer.ps1
echo $installBtn.Add_Click({ >> installer.ps1
echo     $installBtn.Enabled = $false >> installer.ps1
echo     $statusLabel.Text = "جاري التثبيت..." >> installer.ps1
echo     $progressBar.Value = 20 >> installer.ps1
echo     Start-Sleep -Milliseconds 500 >> installer.ps1
echo. >> installer.ps1
echo     # إنشاء مجلد التثبيت >> installer.ps1
echo     $installPath = $pathBox.Text >> installer.ps1
echo     if(!(Test-Path $installPath)){ >> installer.ps1
echo         New-Item -ItemType Directory -Path $installPath -Force >> installer.ps1
echo     } >> installer.ps1
echo     $progressBar.Value = 40 >> installer.ps1
echo     $statusLabel.Text = "نسخ الملفات..." >> installer.ps1
echo     Start-Sleep -Milliseconds 500 >> installer.ps1
echo. >> installer.ps1
echo     # نسخ ملفات التطبيق >> installer.ps1
echo     if(Test-Path "business-plan-clean"){ >> installer.ps1
echo         Copy-Item "business-plan-clean\*" $installPath -Recurse -Force >> installer.ps1
echo     } >> installer.ps1
echo     $progressBar.Value = 70 >> installer.ps1
echo     $statusLabel.Text = "إنشاء الاختصارات..." >> installer.ps1
echo     Start-Sleep -Milliseconds 500 >> installer.ps1
echo. >> installer.ps1
echo     # إنشاء اختصار سطح المكتب >> installer.ps1
echo     $WshShell = New-Object -comObject WScript.Shell >> installer.ps1
echo     $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\خطة مشروعي.lnk") >> installer.ps1
echo     $Shortcut.TargetPath = "$installPath\run-app.bat" >> installer.ps1
echo     $Shortcut.WorkingDirectory = $installPath >> installer.ps1
echo     $Shortcut.IconLocation = "$installPath\icon.ico" >> installer.ps1
echo     $Shortcut.Save() >> installer.ps1
echo. >> installer.ps1
echo     $progressBar.Value = 100 >> installer.ps1
echo     $statusLabel.Text = "تم التثبيت بنجاح!" >> installer.ps1
echo     Start-Sleep -Milliseconds 1000 >> installer.ps1
echo. >> installer.ps1
echo     [System.Windows.Forms.MessageBox]::Show("تم تثبيت خطة مشروعي بنجاح!`n`nيمكنك الآن تشغيل التطبيق من سطح المكتب.","تم التثبيت","OK","Information") >> installer.ps1
echo     $form.Close() >> installer.ps1
echo }) >> installer.ps1
echo $form.Controls.Add($installBtn) >> installer.ps1
echo. >> installer.ps1
echo # زر الإلغاء >> installer.ps1
echo $cancelBtn = New-Object System.Windows.Forms.Button >> installer.ps1
echo $cancelBtn.Location = New-Object System.Drawing.Point(350,280) >> installer.ps1
echo $cancelBtn.Size = New-Object System.Drawing.Size(80,30) >> installer.ps1
echo $cancelBtn.Text = "إلغاء" >> installer.ps1
echo $cancelBtn.Add_Click({ $form.Close() }) >> installer.ps1
echo $form.Controls.Add($cancelBtn) >> installer.ps1
echo. >> installer.ps1
echo # عرض النافذة >> installer.ps1
echo $form.ShowDialog() >> installer.ps1

echo ✅ تم إنشاء سكريبت PowerShell

REM تحويل PowerShell إلى EXE باستخدام ps2exe
echo 🔄 تحويل إلى EXE...
powershell -Command "if(Get-Module -ListAvailable -Name ps2exe){Import-Module ps2exe; Invoke-ps2exe installer.ps1 'خطة-مشروعي-مثبت.exe' -iconFile 'resources\icon.ico' -title 'مثبت خطة مشروعي' -description 'مثبت تطبيق خطة مشروعي - saif aldulaimi' -company 'saif aldulaimi' -version '*******' -copyright 'saif aldulaimi 2025'} else {Write-Host 'تثبيت ps2exe...'; Install-Module ps2exe -Force; Import-Module ps2exe; Invoke-ps2exe installer.ps1 'خطة-مشروعي-مثبت.exe' -iconFile 'resources\icon.ico' -title 'مثبت خطة مشروعي' -description 'مثبت تطبيق خطة مشروعي - saif aldulaimi' -company 'saif aldulaimi' -version '*******' -copyright 'saif aldulaimi 2025'}"

if exist "خطة-مشروعي-مثبت.exe" (
    echo.
    echo ✅ تم إنشاء ملف EXE بنجاح! 🎉
    echo.
    echo 📁 ملف المثبت: خطة-مشروعي-مثبت.exe
    echo 📊 حجم الملف:
    for %%A in ("خطة-مشروعي-مثبت.exe") do echo    %%~zA bytes
    echo.
    echo 🎯 المثبت جاهز للتوزيع!
    echo.
    echo 💡 لاختبار المثبت:
    echo    انقر مرتين على خطة-مشروعي-مثبت.exe
    echo.
    
    REM فتح مجلد المثبت
    explorer /select,"خطة-مشروعي-مثبت.exe"
) else (
    echo.
    echo ⚠️ فشل في إنشاء EXE باستخدام ps2exe
    echo 💡 جرب الطرق البديلة أدناه...
)

echo.
echo 🔧 طرق بديلة لإنشاء EXE:
echo.
echo 1. استخدام Bat To Exe Converter:
echo    - حمل من: https://bat-to-exe-converter-x64.en.softonic.com/
echo    - افتح البرنامج
echo    - اختر ملف مثبت_خطة_مشروعي.bat
echo    - اضغط Compile
echo.
echo 2. استخدام Advanced BAT to EXE Converter:
echo    - حمل من: https://www.battoexeconverter.com/
echo    - أضف الملف والأيقونة
echo    - اضغط Convert
echo.
echo 3. استخدام NSIS (الأفضل):
echo    - شغل: بناء_المثبت_النهائي.bat
echo.
pause

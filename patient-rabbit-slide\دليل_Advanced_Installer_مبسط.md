# دليل Advanced Installer المبسط 🚀

## ✅ نعم! Advanced Installer 22.8 ممتاز جداً!

إنه **أفضل بكثير** من NSIS وأسهل في الاستخدام. دعني أوضح لك كيفية استخدامه:

## 🎯 الطريقة المبسطة (بدون ملفات معقدة):

### 1. تحضير الملفات الأساسية فقط

#### الملفات المطلوبة:
```
patient-rabbit-slide/
├── dist-electron/          (ملفات Electron المبنية)
├── resources/icon.ico      (الأيقونة)
├── package.json           (معلومات التطبيق)
└── run-app.bat            (ملف تشغيل بسيط)
```

#### إنشاء ملف تشغيل بسيط:
```batch
@echo off
echo 🚀 تشغيل خطة مشروعي...
cd /d "%~dp0"
if exist node_modules\electron\dist\electron.exe (
    node_modules\electron\dist\electron.exe .
) else (
    echo 📥 تثبيت Electron...
    npm install electron
    node_modules\electron\dist\electron.exe .
)
pause
```

### 2. خطوات Advanced Installer:

#### أ) إنشاء مشروع جديد:
1. افتح **Advanced Installer 22.8**
2. اختر **"Simple"** → **"New Project"**
3. اختر **"Generic Windows Installer"**

#### ب) إعداد معلومات التطبيق:
```
Product Name: خطة مشروعي
Version: 1.0.0
Publisher: saif aldulaimi
Install Folder: [ProgramFilesFolder]خطة مشروعي
```

#### ج) إضافة الملفات:
1. في تبويب **"Files and Folders"**
2. انقر بالزر الأيمن على **"Application Folder"**
3. اختر **"Add Files"** وأضف:
   - `dist-electron/` (كامل)
   - `resources/icon.ico`
   - `package.json`
   - `run-app.bat`

#### د) إعداد الاختصارات:
1. في تبويب **"Shortcuts"**
2. **Desktop Shortcut**:
   - Name: `خطة مشروعي`
   - Target: `[APPDIR]run-app.bat`
   - Icon: `[APPDIR]resources\icon.ico`

3. **Start Menu**:
   - نفس الإعدادات أعلاه

#### هـ) إعداد اللغة:
1. تبويب **"Translations"**
2. أضف **"Arabic"**

#### و) البناء:
1. تبويب **"Media"**
2. اختر **"Single EXE"**
3. **Build** → **"Build Solution"**

## 🔧 حل سريع - استخدام الملفات الموجودة:

### إذا كانت ملفات dist-electron موجودة:

```bash
# تحقق من الملفات
dir dist-electron
dir resources

# إذا كانت موجودة، استخدمها مباشرة في Advanced Installer
```

### إذا لم تكن موجودة:

```bash
# بناء ملفات Electron فقط
npx tsc -p electron/tsconfig.json

# إنشاء ملف تشغيل بسيط
echo @echo off > run-simple.bat
echo npx electron . >> run-simple.bat
```

## 📋 خطوات مبسطة للغاية:

### 1. افتح Advanced Installer
### 2. مشروع جديد → Generic Windows Installer
### 3. أضف هذه الملفات:
- `electron/main.ts` (أو dist-electron/)
- `resources/icon.ico`
- `package.json`

### 4. اضبط الاختصارات
### 5. ابني المثبت!

## 🎯 المميزات التي ستحصل عليها:

- ✅ **مثبت احترافي** بحجم صغير
- ✅ **واجهة عربية** كاملة
- ✅ **اختصارات تلقائية** مع أيقونات
- ✅ **إلغاء تثبيت نظيف**
- ✅ **تسجيل في Windows** (Add/Remove Programs)
- ✅ **شهادة رقمية** (إذا كان لديك)

## 💡 نصائح مهمة:

### للملف الرئيسي:
- استخدم `run-app.bat` أو `npx electron .`
- تأكد من وجود `package.json` مع `"main": "dist-electron/main.js"`

### للأيقونة:
- استخدم `resources/icon.ico` (موجودة بالفعل)
- أو حول `icon.svg` إلى `icon.ico`

### للاختصارات:
- Desktop: `خطة مشروعي`
- Start Menu: `خطة مشروعي`
- Icon: `resources\icon.ico`

## 🚀 ابدأ الآن!

**Advanced Installer 22.8** هو الخيار الأمثل! أسهل بكثير من NSIS وأكثر احترافية من electron-builder.

### الخطوة التالية:
1. افتح Advanced Installer
2. اتبع الخطوات أعلاه
3. في 10 دقائق ستحصل على مثبت احترافي!

---
**Advanced Installer = الحل الأمثل!** 🎉

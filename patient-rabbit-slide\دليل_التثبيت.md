# دليل تثبيت تطبيق خطة مشروعي 📦

## 🚀 إنشاء ملف التثبيت

### الطريقة الأولى: استخدام السكريبت الجاهز (الأسهل)
```bash
# شغل السكريبت الجاهز
.\build-installer.bat
```

### الطريقة الثانية: الخطوات اليدوية
```bash
# 1. تنظيف الملفات السابقة
rmdir /s /q dist
rmdir /s /q dist-electron
rmdir /s /q dist-app

# 2. بناء تطبيق React
npm run build

# 3. بناء ملفات Electron
npx tsc -p electron/tsconfig.json

# 4. بناء المثبت
npx electron-builder --win
```

## 📁 الملفات المبنية

بعد اكتمال البناء، ستجد في مجلد `dist-app`:

### ملف المثبت:
- `خطة مشروعي Setup 1.0.0.exe` - ملف التثبيت الرئيسي

### مجلد التطبيق:
- `win-unpacked/` - مجلد التطبيق المستخرج (للاستخدام المباشر)

## 💾 تثبيت التطبيق

### للمستخدم النهائي:
1. انقر مرتين على ملف `خطة مشروعي Setup 1.0.0.exe`
2. اتبع خطوات المعالج:
   - اختر مجلد التثبيت (افتراضي: `C:\Program Files\خطة مشروعي`)
   - اختر إنشاء اختصار على سطح المكتب
   - اختر إنشاء اختصار في قائمة ابدأ
3. انقر "تثبيت"
4. انتظر حتى اكتمال التثبيت
5. انقر "إنهاء" لتشغيل التطبيق

### ما يتم تثبيته:
- ✅ التطبيق الرئيسي
- ✅ اختصار على سطح المكتب باسم "خطة مشروعي"
- ✅ اختصار في قائمة ابدأ
- ✅ تسجيل نوع ملف خطط العمل (.businessplan)

## 🔧 إعدادات المثبت

### المميزات المضافة:
- **اسم المطور**: saif aldulaimi
- **اللغة**: دعم العربية والإنجليزية
- **الأيقونة**: أيقونة مخصصة للتطبيق
- **الاختصارات**: سطح المكتب + قائمة ابدأ
- **إلغاء التثبيت**: معالج إلغاء تثبيت كامل

### متطلبات النظام:
- **نظام التشغيل**: Windows 10/11 (64-bit)
- **المعالج**: Intel/AMD x64
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **المساحة**: 500 MB مساحة فارغة

## 🛠️ استكشاف الأخطاء

### مشكلة: فشل في بناء المثبت
**الحل**:
1. تأكد من اتصالك بالإنترنت
2. شغل `npm install` مرة أخرى
3. جرب `npm run build` منفصلاً

### مشكلة: ملف ICO مفقود
**الحل**:
1. شغل `.\create-icon.bat` لإنشاء أيقونة ICO
2. أو استخدم أداة تحويل عبر الإنترنت

### مشكلة: المثبت لا يعمل على أجهزة أخرى
**الحل**:
1. تأكد من أن الجهاز المستهدف يدعم Windows 10+
2. تأكد من تثبيت Visual C++ Redistributable

## 📋 قائمة التحقق قبل التوزيع

- [ ] تم اختبار التطبيق محلياً
- [ ] تم بناء المثبت بنجاح
- [ ] تم اختبار المثبت على جهاز نظيف
- [ ] تم التأكد من عمل الاختصارات
- [ ] تم التأكد من عمل إلغاء التثبيت

## 🎯 التوزيع

### طرق التوزيع:
1. **رفع على موقع**: رفع ملف .exe على موقعك
2. **مشاركة مباشرة**: إرسال الملف عبر البريد/التخزين السحابي
3. **متجر التطبيقات**: (يتطلب توقيع رقمي)

---
**تم إعداد هذا الدليل بواسطة: saif aldulaimi** 🚀

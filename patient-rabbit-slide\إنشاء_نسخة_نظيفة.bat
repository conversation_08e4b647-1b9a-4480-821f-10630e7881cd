@echo off
echo 🎯 إنشاء نسخة نظيفة للتوزيع...
echo.

REM إنشاء مجلد النسخة النظيفة
set CLEAN_DIR=خطة-مشروعي-نظيف
if exist "%CLEAN_DIR%" rmdir /s /q "%CLEAN_DIR%"
mkdir "%CLEAN_DIR%"

echo 📁 إنشاء مجلد النسخة النظيفة: %CLEAN_DIR%

REM نسخ الملفات الأساسية فقط
echo 📋 نسخ الملفات الضرورية...

REM 1. ملفات التطبيق الأساسية
if exist "dist-electron" (
    echo   ✅ dist-electron (ملفات التطبيق)
    xcopy "dist-electron" "%CLEAN_DIR%\dist-electron\" /E /I /Q
) else (
    echo   ⚠️ dist-electron غير موجود - سيتم بناؤه
    npx tsc -p electron/tsconfig.json
    xcopy "dist-electron" "%CLEAN_DIR%\dist-electron\" /E /I /Q
)

REM 2. الأيقونات
if exist "resources\icon.ico" (
    echo   ✅ resources\icon.ico (أيقونة التطبيق)
    mkdir "%CLEAN_DIR%\resources"
    copy "resources\icon.ico" "%CLEAN_DIR%\resources\"
) else (
    echo   ⚠️ أيقونة ICO مفقودة
)

REM 3. ملف package.json مبسط
echo   ✅ package.json (مبسط)
echo { > "%CLEAN_DIR%\package.json"
echo   "name": "business-plan-builder", >> "%CLEAN_DIR%\package.json"
echo   "version": "1.0.0", >> "%CLEAN_DIR%\package.json"
echo   "description": "تطبيق متكامل لإنشاء خطط الأعمال باللغة العربية", >> "%CLEAN_DIR%\package.json"
echo   "main": "dist-electron/main.js", >> "%CLEAN_DIR%\package.json"
echo   "author": "saif aldulaimi", >> "%CLEAN_DIR%\package.json"
echo   "scripts": { >> "%CLEAN_DIR%\package.json"
echo     "start": "electron ." >> "%CLEAN_DIR%\package.json"
echo   }, >> "%CLEAN_DIR%\package.json"
echo   "devDependencies": { >> "%CLEAN_DIR%\package.json"
echo     "electron": "^latest" >> "%CLEAN_DIR%\package.json"
echo   } >> "%CLEAN_DIR%\package.json"
echo } >> "%CLEAN_DIR%\package.json"

REM 4. ملف تشغيل محسن
echo   ✅ run-app.bat (ملف التشغيل)
echo @echo off > "%CLEAN_DIR%\run-app.bat"
echo echo 🚀 تشغيل خطة مشروعي... >> "%CLEAN_DIR%\run-app.bat"
echo cd /d "%%~dp0" >> "%CLEAN_DIR%\run-app.bat"
echo if not exist node_modules\electron ( >> "%CLEAN_DIR%\run-app.bat"
echo     echo 📥 تثبيت Electron... >> "%CLEAN_DIR%\run-app.bat"
echo     npm install electron >> "%CLEAN_DIR%\run-app.bat"
echo ^) >> "%CLEAN_DIR%\run-app.bat"
echo npx electron . >> "%CLEAN_DIR%\run-app.bat"
echo pause >> "%CLEAN_DIR%\run-app.bat"

REM 5. ملفات التوثيق الأساسية
if exist "LICENSE.txt" (
    echo   ✅ LICENSE.txt
    copy "LICENSE.txt" "%CLEAN_DIR%\"
)

if exist "README.md" (
    echo   ✅ README.md
    copy "README.md" "%CLEAN_DIR%\"
)

REM 6. دليل الاستخدام المبسط
echo   ✅ دليل الاستخدام
echo # تطبيق خطة مشروعي > "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo. >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo ## كيفية التشغيل: >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo 1. انقر مرتين على run-app.bat >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo 2. انتظر حتى يتم تحميل التطبيق >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo. >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo ## المتطلبات: >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo - Node.js مثبت على النظام >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo - اتصال بالإنترنت للتحميل الأول >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo. >> "%CLEAN_DIR%\دليل_الاستخدام.txt"
echo تم تطويره بواسطة: saif aldulaimi >> "%CLEAN_DIR%\دليل_الاستخدام.txt"

REM 7. ملف معلومات للمثبت
echo   ✅ معلومات المثبت
echo # معلومات للمثبت > "%CLEAN_DIR%\installer-info.txt"
echo. >> "%CLEAN_DIR%\installer-info.txt"
echo اسم التطبيق: خطة مشروعي >> "%CLEAN_DIR%\installer-info.txt"
echo الإصدار: 1.0.0 >> "%CLEAN_DIR%\installer-info.txt"
echo المطور: saif aldulaimi >> "%CLEAN_DIR%\installer-info.txt"
echo. >> "%CLEAN_DIR%\installer-info.txt"
echo الملف الرئيسي: run-app.bat >> "%CLEAN_DIR%\installer-info.txt"
echo الأيقونة: resources\icon.ico >> "%CLEAN_DIR%\installer-info.txt"
echo المجلد المستهدف: [ProgramFiles]\خطة مشروعي >> "%CLEAN_DIR%\installer-info.txt"

REM حساب الحجم
echo.
echo 📊 حساب حجم النسخة النظيفة...
for /f "tokens=3" %%a in ('dir "%CLEAN_DIR%" /-c /s 2^>nul ^| find "bytes"') do set clean_size=%%a

echo.
echo ✅ تم إنشاء النسخة النظيفة بنجاح! 🎉
echo.
echo 📁 المجلد: %CLEAN_DIR%\
echo 📊 الحجم: %clean_size% bytes
echo.
echo 📋 محتويات النسخة النظيفة:
dir "%CLEAN_DIR%" /b
echo.
echo 🎯 هذه النسخة جاهزة لـ:
echo   1. Advanced Installer (أضف المجلد كاملاً)
echo   2. التوزيع المباشر (انسخ المجلد)
echo   3. الضغط والمشاركة
echo.
echo 💡 الملف الرئيسي للتشغيل: run-app.bat
echo 🎨 الأيقونة للمثبت: resources\icon.ico
echo.
pause

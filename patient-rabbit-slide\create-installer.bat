@echo off
echo 📦 إنشاء مثبت تطبيق خطة مشروعي...
echo.

REM الخطوة 1: إنشاء النسخة المحمولة
echo 🔧 الخطوة 1: إنشاء النسخة المحمولة...
call create-portable.bat
if errorlevel 1 (
    echo ❌ فشل في إنشاء النسخة المحمولة
    pause
    exit /b 1
)

echo ✅ تم إنشاء النسخة المحمولة بنجاح

REM الخطوة 2: التحقق من وجود NSIS
echo 🔍 الخطوة 2: التحقق من NSIS...
where makensis >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم العثور على NSIS
    
    REM إنشاء المثبت باستخدام NSIS
    echo 📦 إنشاء المثبت...
    makensis installer-script.nsi
    if errorlevel 1 (
        echo ❌ فشل في إنشاء المثبت
        echo 💡 تحقق من ملف installer-script.nsi
    ) else (
        echo ✅ تم إنشاء المثبت بنجاح! 🎉
        echo 📁 ملف المثبت: خطة-مشروعي-مثبت-1.0.0.exe
    )
) else (
    echo ⚠️ NSIS غير مثبت
    echo.
    echo 📝 لإنشاء مثبت .exe، يرجى:
    echo    1. تحميل NSIS من: https://nsis.sourceforge.io/Download
    echo    2. تثبيت NSIS
    echo    3. إعادة تشغيل هذا السكريبت
    echo.
    echo 💡 أو يمكنك استخدام النسخة المحمولة من مجلد "خطة-مشروعي-محمول"
)

echo.
echo 📋 ملخص النتائج:
if exist "خطة-مشروعي-محمول" (
    echo ✅ النسخة المحمولة: خطة-مشروعي-محمول\
)
if exist "خطة-مشروعي-مثبت-1.0.0.exe" (
    echo ✅ ملف المثبت: خطة-مشروعي-مثبت-1.0.0.exe
)

echo.
echo 🚀 طرق التوزيع:
echo    1. النسخة المحمولة: انسخ مجلد "خطة-مشروعي-محمول" بالكامل
echo    2. ملف المثبت: وزع ملف "خطة-مشروعي-مثبت-1.0.0.exe"
echo.
pause

; تطبيق خطة مشروعي - مثبت Inno Setup
; Business Plan Builder - Inno Setup Installer
; المطور: sa<PERSON>

[Setup]
AppName=خطة مشروعي
AppVersion=1.0.0
AppPublisher=saif aldulaimi
AppPublisherURL=
AppSupportURL=
AppUpdatesURL=
DefaultDirName={autopf}\خطة مشروعي
DefaultGroupName=خطة مشروعي
AllowNoIcons=yes
LicenseFile=LICENSE.txt
OutputDir=.
OutputBaseFilename=خطة-مشروعي-مثبت-inno-1.0.0
SetupIconFile=resources\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
LanguageDetectionMethod=uilanguage
ShowLanguageDialog=auto
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
Source: "business-plan-clean\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\خطة مشروعي"; Filename: "{app}\run-app.bat"; IconFilename: "{app}\icon.ico"
Name: "{group}\{cm:UninstallProgram,خطة مشروعي}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\خطة مشروعي"; Filename: "{app}\run-app.bat"; IconFilename: "{app}\icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\خطة مشروعي"; Filename: "{app}\run-app.bat"; IconFilename: "{app}\icon.ico"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\run-app.bat"; Description: "{cm:LaunchProgram,خطة مشروعي}"; Flags: nowait postinstall skipifsilent

[Code]
function InitializeSetup(): Boolean;
var
  ResultCode: Integer;
begin
  // التحقق من وجود Node.js
  if not Exec('node', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) or (ResultCode <> 0) then
  begin
    if MsgBox('Node.js غير مثبت على النظام.' + #13#10 + #13#10 + 'هل تريد المتابعة؟ (ستحتاج لتثبيت Node.js لاحقاً)', mbConfirmation, MB_YESNO) = IDNO then
    begin
      Result := False;
      Exit;
    end;
  end;
  Result := True;
end;

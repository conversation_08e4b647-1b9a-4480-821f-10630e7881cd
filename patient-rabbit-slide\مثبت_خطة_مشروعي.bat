@echo off
title مثبت خطة مشروعي - saif al<PERSON>
color 0A
echo.
echo ===============================================
echo           مثبت خطة مشروعي v1.0.0
echo        تطبيق إنشاء خطط الأعمال باللغة العربية
echo           المطور: saif aldu<PERSON>mi
echo ===============================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم تشغيل المثبت بصلاحيات المدير
) else (
    echo ⚠️ يُنصح بتشغيل المثبت كمدير للحصول على أفضل النتائج
)

echo.
echo 🔍 التحقق من المتطلبات...

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org
    echo.
    set /p continue="هل تريد المتابعة بدون Node.js؟ (y/n): "
    if /i not "%continue%"=="y" (
        echo إلغاء التثبيت...
        pause
        exit /b 1
    )
) else (
    echo ✅ Node.js متوفر
)

echo.
echo 📁 اختيار مجلد التثبيت:
echo.
echo 1. C:\Program Files\خطة مشروعي (افتراضي)
echo 2. C:\خطة مشروعي
echo 3. مجلد مخصص
echo.
set /p choice="اختر رقم (1-3) أو اضغط Enter للافتراضي: "

if "%choice%"=="2" (
    set "install_dir=C:\خطة مشروعي"
) else if "%choice%"=="3" (
    set /p install_dir="أدخل المسار الكامل: "
) else (
    set "install_dir=C:\Program Files\خطة مشروعي"
)

echo.
echo 📂 مجلد التثبيت: %install_dir%
echo.

REM التحقق من وجود المجلد
if exist "%install_dir%" (
    echo ⚠️ المجلد موجود بالفعل
    set /p overwrite="هل تريد الكتابة فوقه؟ (y/n): "
    if /i not "%overwrite%"=="y" (
        echo إلغاء التثبيت...
        pause
        exit /b 1
    )
    echo 🗑️ حذف المجلد القديم...
    rmdir /s /q "%install_dir%" >nul 2>&1
)

echo 📂 إنشاء مجلد التثبيت...
mkdir "%install_dir%" >nul 2>&1
if not exist "%install_dir%" (
    echo ❌ فشل في إنشاء مجلد التثبيت
    echo تأكد من الصلاحيات أو اختر مجلد آخر
    pause
    exit /b 1
)

echo ✅ تم إنشاء مجلد التثبيت

echo.
echo 📋 نسخ ملفات التطبيق...

REM نسخ ملفات التطبيق
if exist "business-plan-clean" (
    echo 📁 نسخ من business-plan-clean...
    xcopy "business-plan-clean\*" "%install_dir%\" /E /I /Q /Y
) else (
    echo ❌ ملفات التطبيق غير موجودة
    echo تأكد من وجود مجلد business-plan-clean
    pause
    exit /b 1
)

echo ✅ تم نسخ ملفات التطبيق

echo.
echo 🔗 إنشاء الاختصارات...

REM إنشاء اختصار سطح المكتب
echo 🖥️ اختصار سطح المكتب...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\خطة مشروعي.lnk'); $Shortcut.TargetPath = '%install_dir%\run-app.bat'; $Shortcut.WorkingDirectory = '%install_dir%'; $Shortcut.IconLocation = '%install_dir%\icon.ico'; $Shortcut.Description = 'تطبيق إنشاء خطط الأعمال باللغة العربية'; $Shortcut.Save()"

REM إنشاء اختصار قائمة ابدأ
echo 📋 اختصار قائمة ابدأ...
if not exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\خطة مشروعي" (
    mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\خطة مشروعي"
)
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\خطة مشروعي\خطة مشروعي.lnk'); $Shortcut.TargetPath = '%install_dir%\run-app.bat'; $Shortcut.WorkingDirectory = '%install_dir%'; $Shortcut.IconLocation = '%install_dir%\icon.ico'; $Shortcut.Description = 'تطبيق إنشاء خطط الأعمال باللغة العربية'; $Shortcut.Save()"

echo ✅ تم إنشاء الاختصارات

echo.
echo 📝 تسجيل التطبيق في النظام...

REM تسجيل في Add/Remove Programs
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /v "DisplayName" /t REG_SZ /d "خطة مشروعي" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /v "Publisher" /t REG_SZ /d "saif aldulaimi" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /v "InstallLocation" /t REG_SZ /d "%install_dir%" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /v "UninstallString" /t REG_SZ /d "%install_dir%\إلغاء_التثبيت.bat" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /v "DisplayIcon" /t REG_SZ /d "%install_dir%\icon.ico" /f >nul 2>&1

REM إنشاء ملف إلغاء التثبيت
echo 🗑️ إنشاء ملف إلغاء التثبيت...
echo @echo off > "%install_dir%\إلغاء_التثبيت.bat"
echo title إلغاء تثبيت خطة مشروعي >> "%install_dir%\إلغاء_التثبيت.bat"
echo echo إلغاء تثبيت خطة مشروعي... >> "%install_dir%\إلغاء_التثبيت.bat"
echo echo. >> "%install_dir%\إلغاء_التثبيت.bat"
echo set /p confirm="هل أنت متأكد؟ (y/n): " >> "%install_dir%\إلغاء_التثبيت.bat"
echo if /i not "%%confirm%%"=="y" exit /b >> "%install_dir%\إلغاء_التثبيت.bat"
echo echo حذف الاختصارات... >> "%install_dir%\إلغاء_التثبيت.bat"
echo del "%%USERPROFILE%%\Desktop\خطة مشروعي.lnk" ^>nul 2^>^&1 >> "%install_dir%\إلغاء_التثبيت.bat"
echo rmdir /s /q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\خطة مشروعي" ^>nul 2^>^&1 >> "%install_dir%\إلغاء_التثبيت.bat"
echo echo حذف تسجيل النظام... >> "%install_dir%\إلغاء_التثبيت.bat"
echo reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\خطة مشروعي" /f ^>nul 2^>^&1 >> "%install_dir%\إلغاء_التثبيت.bat"
echo echo حذف ملفات التطبيق... >> "%install_dir%\إلغاء_التثبيت.bat"
echo cd /d "%%install_dir%%\.." >> "%install_dir%\إلغاء_التثبيت.bat"
echo rmdir /s /q "%install_dir%" >> "%install_dir%\إلغاء_التثبيت.bat"
echo echo تم إلغاء التثبيت بنجاح! >> "%install_dir%\إلغاء_التثبيت.bat"
echo pause >> "%install_dir%\إلغاء_التثبيت.bat"

echo ✅ تم تسجيل التطبيق في النظام

echo.
echo ===============================================
echo ✅ تم تثبيت خطة مشروعي بنجاح! 🎉
echo ===============================================
echo.
echo 📁 مجلد التثبيت: %install_dir%
echo 🖥️ اختصار سطح المكتب: خطة مشروعي
echo 📋 قائمة ابدأ: خطة مشروعي
echo 🗑️ إلغاء التثبيت: من Add/Remove Programs
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق من:
echo    - سطح المكتب
echo    - قائمة ابدأ
echo    - مجلد التثبيت مباشرة
echo.
set /p run_now="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%run_now%"=="y" (
    echo 🚀 تشغيل التطبيق...
    start "" "%install_dir%\run-app.bat"
)

echo.
echo شكراً لاستخدام خطة مشروعي!
echo المطور: saif aldulaimi
pause

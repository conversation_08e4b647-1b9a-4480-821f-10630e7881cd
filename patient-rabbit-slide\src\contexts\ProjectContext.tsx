import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { ProjectData, initialProjectData } from '@/types/project';
import { toast } from '@/hooks/use-toast';

// تحديد ما إذا كان التطبيق يعمل في Electron
const isElectron = typeof window !== 'undefined' && window.electronAPI;

// أنواع الإجراءات
type ProjectAction = 
  | { type: 'UPDATE_PERSONAL_INFO'; payload: Partial<ProjectData['personalInfo']> }
  | { type: 'UPDATE_PROJECT_DESCRIPTION'; payload: Partial<ProjectData['projectDescription']> }
  | { type: 'UPDATE_MARKET_STUDY'; payload: Partial<ProjectData['marketStudy']> }
  | { type: 'UPDATE_SWOT_ANALYSIS'; payload: Partial<ProjectData['swotAnalysis']> }
  | { type: 'UPDATE_MARKETING_MIX'; payload: Partial<ProjectData['marketingMix']> }
  | { type: 'UPDATE_PRODUCTION_REQUIREMENTS'; payload: Partial<ProjectData['productionRequirements']> }
  | { type: 'UPDATE_FINANCIAL_STUDY'; payload: Partial<ProjectData['financialStudy']> }
  | { type: 'SET_CURRENT_STEP'; payload: number }
  | { type: 'LOAD_PROJECT_DATA'; payload: ProjectData }
  | { type: 'RESET_PROJECT_DATA' }
  | { type: 'AUTO_SAVE' };

// Reducer لإدارة حالة المشروع
const projectReducer = (state: ProjectData, action: ProjectAction): ProjectData => {
  switch (action.type) {
    case 'UPDATE_PERSONAL_INFO':
      return {
        ...state,
        personalInfo: { ...state.personalInfo, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'UPDATE_PROJECT_DESCRIPTION':
      return {
        ...state,
        projectDescription: { ...state.projectDescription, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'UPDATE_MARKET_STUDY':
      return {
        ...state,
        marketStudy: { ...state.marketStudy, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'UPDATE_SWOT_ANALYSIS':
      return {
        ...state,
        swotAnalysis: { ...state.swotAnalysis, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'UPDATE_MARKETING_MIX':
      return {
        ...state,
        marketingMix: { ...state.marketingMix, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'UPDATE_PRODUCTION_REQUIREMENTS':
      return {
        ...state,
        productionRequirements: { ...state.productionRequirements, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'UPDATE_FINANCIAL_STUDY':
      return {
        ...state,
        financialStudy: { ...state.financialStudy, ...action.payload },
        lastUpdated: new Date(),
      };
    case 'SET_CURRENT_STEP':
      return {
        ...state,
        currentStep: action.payload,
        lastUpdated: new Date(),
      };
    case 'LOAD_PROJECT_DATA':
      return action.payload;
    case 'RESET_PROJECT_DATA':
      return initialProjectData;
    case 'AUTO_SAVE':
      return {
        ...state,
        lastUpdated: new Date(),
      };
    default:
      return state;
  }
};

// نوع السياق
interface ProjectContextType {
  projectData: ProjectData;
  updatePersonalInfo: (data: Partial<ProjectData['personalInfo']>) => void;
  updateProjectDescription: (data: Partial<ProjectData['projectDescription']>) => void;
  updateMarketStudy: (data: Partial<ProjectData['marketStudy']>) => void;
  updateSwotAnalysis: (data: Partial<ProjectData['swotAnalysis']>) => void;
  updateMarketingMix: (data: Partial<ProjectData['marketingMix']>) => void;
  updateProductionRequirements: (data: Partial<ProjectData['productionRequirements']>) => void;
  updateFinancialStudy: (data: Partial<ProjectData['financialStudy']>) => void;
  setCurrentStep: (step: number) => void;
  saveProject: () => void;
  saveProjectToFile: () => Promise<void>;
  loadProject: () => void;
  loadProjectFromFile: (data: ProjectData) => void;
  resetProject: () => void;
  isDataValid: (step: number) => boolean;
}

// إنشاء السياق
const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

// مفتاح التخزين المحلي
const STORAGE_KEY = 'project-business-plan-data';

// مزود السياق
export const ProjectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [projectData, dispatch] = useReducer(projectReducer, initialProjectData);

  // تحميل البيانات من التخزين المحلي عند بدء التطبيق
  useEffect(() => {
    loadProject();

    // إعداد مستمعي أحداث Electron
    if (isElectron) {
      window.electronAPI.onSaveProject(() => {
        saveProjectToFile();
      });

      window.electronAPI.onLoadProject((data: ProjectData) => {
        loadProjectFromFile(data);
      });

      window.electronAPI.onExportPDF(() => {
        // TODO: تنفيذ تصدير PDF
        toast({
          title: "تصدير PDF",
          description: "ميزة تصدير PDF ستكون متاحة قريباً",
        });
      });

      window.electronAPI.onExportExcel(() => {
        // TODO: تنفيذ تصدير Excel
        toast({
          title: "تصدير Excel",
          description: "ميزة تصدير Excel ستكون متاحة قريباً",
        });
      });
    }
  }, []);

  // حفظ تلقائي عند تغيير البيانات
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveProject();
    }, 1000); // حفظ بعد ثانية واحدة من آخر تغيير

    return () => clearTimeout(timeoutId);
  }, [projectData]);

  // وظائف التحديث
  const updatePersonalInfo = (data: Partial<ProjectData['personalInfo']>) => {
    dispatch({ type: 'UPDATE_PERSONAL_INFO', payload: data });
  };

  const updateProjectDescription = (data: Partial<ProjectData['projectDescription']>) => {
    dispatch({ type: 'UPDATE_PROJECT_DESCRIPTION', payload: data });
  };

  const updateMarketStudy = (data: Partial<ProjectData['marketStudy']>) => {
    dispatch({ type: 'UPDATE_MARKET_STUDY', payload: data });
  };

  const updateSwotAnalysis = (data: Partial<ProjectData['swotAnalysis']>) => {
    dispatch({ type: 'UPDATE_SWOT_ANALYSIS', payload: data });
  };

  const updateMarketingMix = (data: Partial<ProjectData['marketingMix']>) => {
    dispatch({ type: 'UPDATE_MARKETING_MIX', payload: data });
  };

  const updateProductionRequirements = (data: Partial<ProjectData['productionRequirements']>) => {
    dispatch({ type: 'UPDATE_PRODUCTION_REQUIREMENTS', payload: data });
  };

  const updateFinancialStudy = (data: Partial<ProjectData['financialStudy']>) => {
    dispatch({ type: 'UPDATE_FINANCIAL_STUDY', payload: data });
  };

  const setCurrentStep = (step: number) => {
    dispatch({ type: 'SET_CURRENT_STEP', payload: step });
  };

  // حفظ البيانات في التخزين المحلي
  const saveProject = () => {
    try {
      const dataToSave = {
        ...projectData,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ البيانات",
        variant: "destructive",
      });
    }
  };

  // حفظ المشروع في ملف (Electron)
  const saveProjectToFile = async () => {
    if (!isElectron) {
      toast({
        title: "غير متاح",
        description: "هذه الميزة متاحة فقط في تطبيق سطح المكتب",
        variant: "destructive",
      });
      return;
    }

    try {
      const dataToSave = {
        ...projectData,
        lastUpdated: new Date().toISOString(),
      };

      const result = await window.electronAPI.saveProjectFile(dataToSave);

      if (result.success) {
        toast({
          title: "تم الحفظ بنجاح",
          description: `تم حفظ المشروع في: ${result.path}`,
        });
      } else {
        toast({
          title: "خطأ في الحفظ",
          description: result.error || "حدث خطأ أثناء حفظ الملف",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('خطأ في حفظ الملف:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ الملف",
        variant: "destructive",
      });
    }
  };

  // تحميل البيانات من التخزين المحلي
  const loadProject = () => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        // تحويل التاريخ من string إلى Date
        parsedData.lastUpdated = new Date(parsedData.lastUpdated);
        dispatch({ type: 'LOAD_PROJECT_DATA', payload: parsedData });
        toast({
          title: "تم تحميل البيانات",
          description: "تم استرداد بيانات المشروع المحفوظة",
        });
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل البيانات المحفوظة",
        variant: "destructive",
      });
    }
  };

  // تحميل المشروع من ملف (Electron)
  const loadProjectFromFile = (data: ProjectData) => {
    try {
      // تحويل التاريخ من string إلى Date
      const processedData = {
        ...data,
        lastUpdated: new Date(data.lastUpdated),
      };

      dispatch({ type: 'LOAD_PROJECT_DATA', payload: processedData });

      toast({
        title: "تم تحميل المشروع",
        description: "تم تحميل بيانات المشروع من الملف بنجاح",
      });
    } catch (error) {
      console.error('خطأ في تحميل الملف:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات المشروع",
        variant: "destructive",
      });
    }
  };

  // إعادة تعيين البيانات
  const resetProject = () => {
    dispatch({ type: 'RESET_PROJECT_DATA' });
    localStorage.removeItem(STORAGE_KEY);
    toast({
      title: "تم إعادة التعيين",
      description: "تم مسح جميع بيانات المشروع",
    });
  };

  // التحقق من صحة البيانات لكل خطوة
  const isDataValid = (step: number): boolean => {
    switch (step) {
      case 0: // المعلومات الشخصية ووصف المشروع
        return !!(projectData.personalInfo.ownerName && 
                 projectData.personalInfo.phone && 
                 projectData.projectDescription.projectName);
      case 1: // دراسة السوق
        return !!(projectData.marketStudy.products || projectData.marketStudy.services);
      case 2: // SWOT
        return !!(projectData.swotAnalysis.strengths && 
                 projectData.swotAnalysis.weaknesses);
      case 3: // المزيج التسويقي
        return !!(projectData.marketingMix.product && 
                 projectData.marketingMix.price);
      case 4: // مستلزمات الإنتاج
        return !!(projectData.productionRequirements.equipment || 
                 projectData.productionRequirements.materials);
      case 5: // الدراسة المالية
        return projectData.financialStudy.fixedCosts.some(cost => cost.monthly > 0) ||
               projectData.financialStudy.variableCosts.some(cost => cost.monthly > 0);
      default:
        return true;
    }
  };

  const value: ProjectContextType = {
    projectData,
    updatePersonalInfo,
    updateProjectDescription,
    updateMarketStudy,
    updateSwotAnalysis,
    updateMarketingMix,
    updateProductionRequirements,
    updateFinancialStudy,
    setCurrentStep,
    saveProject,
    saveProjectToFile,
    loadProject,
    loadProjectFromFile,
    resetProject,
    isDataValid,
  };

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useProject = (): ProjectContextType => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};

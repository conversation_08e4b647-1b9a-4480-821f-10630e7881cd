@echo off
echo 🎨 إنشاء أيقونة ICO للتطبيق...
echo.

REM التحقق من وجود ImageMagick أو أي أداة تحويل
where magick >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم العثور على ImageMagick، جاري تحويل SVG إلى ICO...
    magick resources\icon.svg -resize 256x256 resources\icon.ico
    if exist resources\icon.ico (
        echo ✅ تم إنشاء ملف icon.ico بنجاح
    ) else (
        echo ❌ فشل في إنشاء ملف ICO
    )
) else (
    echo ⚠️ ImageMagick غير مثبت
    echo 📝 يمكنك تحويل ملف icon.svg إلى icon.ico يدوياً باستخدام:
    echo    - موقع convertio.co
    echo    - أو تثبيت ImageMagick من imagemagick.org
    echo.
    echo 💡 أو يمكنك استخدام ملف SVG مباشرة في التطبيق
)

echo.
pause

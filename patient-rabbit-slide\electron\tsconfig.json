{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "../dist-electron", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "noEmit": false, "declaration": false, "sourceMap": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist-electron"]}
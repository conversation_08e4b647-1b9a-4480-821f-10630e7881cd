import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileDown, FileText, Sheet, CheckCircle, Calendar, User } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";
import { toast } from "@/hooks/use-toast";

export const Step7_Final = () => {
  const { projectData, saveProject, resetProject } = useProject();

  const handleSaveProgress = () => {
    saveProject();
    toast({
      title: "تم الحفظ بنجاح!",
      description: "تم حفظ جميع بيانات المشروع في التخزين المحلي",
    });
  };

  const handleExportPDF = () => {
    // TODO: تنفيذ تصدير PDF
    toast({
      title: "قريباً",
      description: "ميزة تصدير PDF ستكون متاحة قريباً",
      variant: "destructive",
    });
  };

  const handleExportExcel = () => {
    // TODO: تنفيذ تصدير Excel
    toast({
      title: "قريباً",
      description: "ميزة تصدير Excel ستكون متاحة قريباً",
      variant: "destructive",
    });
  };

  const handleReset = () => {
    if (confirm("هل أنت متأكد من أنك تريد مسح جميع البيانات؟")) {
      resetProject();
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="space-y-8">
      <div className="text-center p-8 space-y-8 relative">
        {/* تأثيرات الاحتفال */}
        <div className="absolute inset-0 bg-gradient-to-r from-green-400/10 via-blue-400/10 to-purple-400/10 rounded-3xl animate-pulse"></div>

        <div className="flex justify-center relative z-10">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-xl opacity-50 animate-pulse"></div>
            <div className="relative w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-3d animate-glow">
              <CheckCircle className="h-12 w-12 text-white" />
            </div>
            {/* نجوم متحركة */}
            <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-ping"></div>
            <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-blue-400 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute top-2 -left-4 w-2 h-2 bg-purple-400 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
          </div>
        </div>

        <div className="space-y-4 relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-gradient-primary animate-glow">
            🎉 تهانينا! 🎉
          </h2>
          <h3 className="text-2xl md:text-3xl font-bold text-green-600 dark:text-green-400">
            لقد أكملت خطة المشروع بنجاح!
          </h3>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            عمل رائع! لقد قمت بإنشاء خطة عمل شاملة ومتكاملة لمشروعك.
            يمكنك الآن حفظ خطة عملك أو تصديرها كملف PDF أو Excel للمشاركة والعرض.
          </p>
        </div>

        {/* شريط زخرفي */}
        <div className="flex justify-center relative z-10">
          <div className="w-32 h-1 bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 rounded-full shadow-lg animate-pulse"></div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="card-3d shadow-3d-hover border-glow bg-gradient-to-br from-blue-50/90 to-indigo-50/90 dark:from-blue-900/30 dark:to-indigo-900/30 backdrop-blur-sm relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/5 to-indigo-400/5"></div>
          <CardHeader className="relative z-10 border-b border-blue-200/50 dark:border-blue-700/50">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg">
                <User className="h-5 w-5 text-white" />
              </div>
              <span className="text-gradient-primary">ملخص المشروع</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-6 relative z-10">
            <div className="p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30">
              <strong className="text-blue-700 dark:text-blue-300">اسم المشروع:</strong>
              <span className="mr-2 text-gray-700 dark:text-gray-300">{projectData.projectDescription.projectName || "غير محدد"}</span>
            </div>
            <div className="p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30">
              <strong className="text-blue-700 dark:text-blue-300">صاحب المشروع:</strong>
              <span className="mr-2 text-gray-700 dark:text-gray-300">{projectData.personalInfo.ownerName || "غير محدد"}</span>
            </div>
            <div className="p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30">
              <strong className="text-blue-700 dark:text-blue-300">موقع المشروع:</strong>
              <span className="mr-2 text-gray-700 dark:text-gray-300">{projectData.projectDescription.projectLocation || "غير محدد"}</span>
            </div>
          </CardContent>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 opacity-50"></div>
        </Card>

        <Card className="card-3d shadow-3d-hover border-glow bg-gradient-to-br from-green-50/90 to-emerald-50/90 dark:from-green-900/30 dark:to-emerald-900/30 backdrop-blur-sm relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-green-400/5 to-emerald-400/5"></div>
          <CardHeader className="relative z-10 border-b border-green-200/50 dark:border-green-700/50">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <span className="text-gradient-primary">معلومات الحفظ</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-6 relative z-10">
            <div className="p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-green-200/30 dark:border-green-700/30">
              <strong className="text-green-700 dark:text-green-300">آخر تحديث:</strong>
              <span className="mr-2 text-gray-700 dark:text-gray-300">{formatDate(projectData.lastUpdated)}</span>
            </div>
            <div className="p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-green-200/30 dark:border-green-700/30">
              <strong className="text-green-700 dark:text-green-300">الخطوة الحالية:</strong>
              <span className="mr-2 text-gray-700 dark:text-gray-300">{projectData.currentStep + 1} من 7</span>
            </div>
            <div className="p-3 rounded-lg bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 border border-green-200 dark:border-green-700/50">
              <strong className="text-green-700 dark:text-green-300">حالة الإكمال:</strong>
              <span className="mr-2 text-green-600 dark:text-green-400 font-bold">✅ مكتمل 100%</span>
            </div>
          </CardContent>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500 opacity-50"></div>
        </Card>
      </div>

      <div className="flex flex-wrap justify-center gap-6 pt-8">
        <Button
          size="lg"
          variant="outline"
          onClick={handleSaveProgress}
          className="btn-3d shadow-3d border-2 border-blue-200 dark:border-blue-700 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-blue-50 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300"
        >
          <FileDown className="ml-2 h-5 w-5" />
          حفظ التقدم
        </Button>

        <Button
          size="lg"
          onClick={handleExportPDF}
          className="btn-3d shadow-3d bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white border-0 relative overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
          <span className="relative flex items-center">
            <FileText className="ml-2 h-5 w-5" />
            تصدير PDF
          </span>
        </Button>

        <Button
          size="lg"
          onClick={handleExportExcel}
          className="btn-3d shadow-3d bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 relative overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
          <span className="relative flex items-center">
            <Sheet className="ml-2 h-5 w-5" />
            تصدير Excel
          </span>
        </Button>

        <Button
          size="lg"
          variant="destructive"
          onClick={handleReset}
          className="btn-3d shadow-3d bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white border-0 relative overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
          <span className="relative flex items-center">
            🗑️ مسح البيانات
          </span>
        </Button>
      </div>
    </div>
  );
};
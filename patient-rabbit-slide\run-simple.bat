@echo off
echo 🚀 تشغيل تطبيق خطة مشروعي...
echo.

REM الانتقال إلى مجلد التطبيق
cd /d "%~dp0"

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

REM التحقق من وجود ملفات Electron
if exist "dist-electron\main.js" (
    echo ✅ ملفات Electron موجودة
) else (
    echo ⚡ بناء ملفات Electron...
    npx tsc -p electron/tsconfig.json
    if errorlevel 1 (
        echo ❌ فشل في بناء ملفات Electron
        pause
        exit /b 1
    )
)

REM التحقق من وجود Electron
if exist "node_modules\electron\dist\electron.exe" (
    echo ✅ Electron مثبت
    echo 🚀 تشغيل التطبيق...
    node_modules\electron\dist\electron.exe .
) else (
    echo 📥 تثبيت Electron...
    npm install electron
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Electron
        echo 💡 تأكد من اتصالك بالإنترنت
        pause
        exit /b 1
    )
    echo 🚀 تشغيل التطبيق...
    node_modules\electron\dist\electron.exe .
)

REM إذا وصلنا هنا، فقد تم إغلاق التطبيق
echo.
echo 🔚 تم إغلاق التطبيق
pause

{"name": "business-plan-builder", "productName": "خطة مشروعي - Business Plan Builder", "description": "تطبيق متكامل لإنشاء خطط الأعمال باللغة العربية", "version": "1.0.0", "author": "<PERSON><PERSON>", "main": "dist-electron/main.js", "homepage": "./", "type": "module", "scripts": {"dev": "vite", "build": "node --max-old-space-size=4096 ./node_modules/vite/bin/vite.js build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron:dev": "node scripts/dev-electron.js", "electron:build": "node scripts/build-electron.js", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "build:electron": "npm run build && tsc -p electron/tsconfig.json && electron-builder", "dist": "npm run electron:build", "start": "npm run electron:dev"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@dyad-sh/react-vite-component-tagger": "^0.8.0", "@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "electron": "^28.3.3", "electron-builder": "^24.13.3", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.4", "wait-on": "^7.2.0"}, "build": {"appId": "com.saif.business-plan-builder", "productName": "خطة مشروعي", "copyright": "Copyright © 2024 <PERSON><PERSON>", "directories": {"output": "dist-app"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*"], "extraResources": [{"from": "resources/", "to": "resources/"}], "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "resources/icon.svg", "target": [{"target": "nsis", "arch": ["x64"]}], "publisherName": "<PERSON><PERSON>"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "خطة مشروعي", "installerIcon": "resources/icon.svg", "uninstallerIcon": "resources/icon.svg", "installerHeaderIcon": "resources/icon.svg", "deleteAppDataOnUninstall": false, "displayLanguageSelector": false, "language": "1025", "include": "installer.nsh"}}}
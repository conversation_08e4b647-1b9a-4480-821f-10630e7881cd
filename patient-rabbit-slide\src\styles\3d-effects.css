/* تأثيرات 3D وتحسينات بصرية */

/* تأثيرات الظلال المتقدمة */
.shadow-3d {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-3d-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.shadow-3d-hover:hover {
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

/* تأثيرات الخلفية المتدرجة */
.bg-gradient-3d {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(0, 0, 0, 0.05) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.bg-gradient-card {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
}

.dark .bg-gradient-card {
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(15, 23, 42, 0.9) 100%
  );
}

/* تأثيرات النصوص المتدرجة */
.text-gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تأثيرات الأزرار ثلاثية الأبعاد */
.btn-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-3d:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
  border-radius: inherit;
  z-index: -1;
}

.btn-3d:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 6px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.btn-3d:active {
  transform: translateY(0) scale(0.98);
}

/* تأثيرات الكروت المحسنة */
.card-3d {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .card-3d {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-3d:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.dark .card-3d:hover {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* تأثيرات الإدخال المحسنة */
.input-3d {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-3d:focus {
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.06),
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.dark .input-3d {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تأثيرات الحركة */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

/* تأثيرات الخلفية المتحركة */
.bg-animated {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* تأثيرات الحدود المضيئة */
.border-glow {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4) border-box;
}

.dark .border-glow {
  background: linear-gradient(rgb(30, 41, 59), rgb(30, 41, 59)) padding-box,
              linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4) border-box;
}

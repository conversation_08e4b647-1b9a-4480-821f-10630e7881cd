# خطة مشروعي - Business Plan Builder 📊

تطبيق سطح مكتب متكامل لمساعدة رواد الأعمال على إنشاء خطط أعمال احترافية خطوة بخطوة باللغة العربية.

## 🖥️ تطبيق سطح المكتب

التطبيق متاح الآن كتطبيق سطح مكتب مستقل يعمل على:
- ✅ **Windows** (Windows 10/11)
- ✅ **macOS** (macOS 10.14+)
- ✅ **Linux** (Ubuntu 18.04+)

## ✨ المميزات الجديدة

### 🔄 إدارة البيانات المركزية
- **حفظ تلقائي**: يتم حفظ البيانات تلقائياً عند كل تغيير
- **استرداد البيانات**: يتم استرداد البيانات المحفوظة عند إعادة فتح التطبيق
- **التحقق من صحة البيانات**: فحص البيانات المطلوبة قبل الانتقال بين الخطوات

### 📊 حسابات مالية تفاعلية
- **تحديث فوري**: تتحدث جميع الحسابات المالية تلقائياً عند تغيير البيانات
- **نقطة التعادل**: حساب نقطة التعادل بناءً على التكاليف والأسعار
- **تحليل الربحية**: عرض تفصيلي للأرباح والخسائر

### 🎯 تتبع التقدم المحسن
- **شريط تقدم تفاعلي**: يظهر حالة كل خطوة (مكتملة، حالية، غير مكتملة)
- **تنبيهات البيانات**: تحذيرات عند وجود بيانات ناقصة
- **ملخص المشروع**: عرض ملخص شامل في النهاية

### 💾 وظائف الحفظ والتصدير
- **حفظ محلي**: حفظ البيانات في التطبيق
- **حفظ في ملف**: حفظ المشروع كملف JSON
- **فتح من ملف**: تحميل مشروع محفوظ
- **تصدير PDF**: (قريباً) تصدير خطة العمل كملف PDF
- **تصدير Excel**: (قريباً) تصدير البيانات المالية كجدول بيانات

### 🖥️ مميزات تطبيق سطح المكتب
- **عمل بدون إنترنت**: لا يحتاج اتصال بالإنترنت
- **قوائم عربية**: قوائم مخصصة باللغة العربية
- **اختصارات لوحة المفاتيح**:
  - `Ctrl+S` / `Cmd+S`: حفظ المشروع
  - `Ctrl+O` / `Cmd+O`: فتح مشروع
  - `Ctrl+N` / `Cmd+N`: مشروع جديد
  - `F11`: الشاشة الكاملة
- **نوافذ متعددة**: إمكانية فتح عدة مشاريع
- **تحديثات تلقائية**: (قريباً) تحديث التطبيق تلقائياً

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **Electron** لتطبيق سطح المكتب
- **React 18** + **TypeScript** للواجهة الأمامية
- **Vite** كأداة بناء سريعة
- **Tailwind CSS** للتصميم مع تأثيرات 3D
- **Shadcn/UI** لمكونات الواجهة
- **React Hook Form** + **Zod** للنماذج والتحقق
- **React Query** لإدارة الحالة
- **Context API** لإدارة البيانات المركزية
- **Electron Builder** لبناء وتوزيع التطبيق

### هيكل المشروع
```
├── electron/                  # ملفات Electron
│   ├── main.ts               # العملية الرئيسية
│   ├── preload.ts            # سكريبت الأمان
│   └── tsconfig.json         # إعدادات TypeScript
├── src/                      # كود React
│   ├── components/
│   │   ├── ui/               # مكونات Shadcn/UI
│   │   ├── project-form/     # نماذج المشروع
│   │   ├── wizard-steps/     # خطوات المعالج
│   │   └── ProjectFormWizard.tsx
│   ├── contexts/
│   │   └── ProjectContext.tsx # إدارة البيانات المركزية
│   ├── types/
│   │   └── project.ts        # أنواع البيانات
│   ├── styles/
│   │   └── 3d-effects.css    # تأثيرات ثلاثية الأبعاد
│   └── pages/
│       ├── Index.tsx         # الصفحة الرئيسية
│       └── NotFound.tsx      # صفحة 404
├── scripts/                  # سكريبتات البناء
│   ├── build-electron.js     # بناء التطبيق
│   └── dev-electron.js       # تطوير التطبيق
├── resources/                # موارد التطبيق
│   └── icon.svg              # أيقونة التطبيق
└── dist-app/                 # التطبيق المبني
```

## 🚀 التشغيل والتطوير

### تطوير التطبيق
```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير (Electron)
npm run electron:dev
# أو
npm start

# تشغيل تطبيق الويب فقط
npm run dev
```

### بناء التطبيق
```bash
# بناء تطبيق سطح المكتب
npm run electron:build

# بناء تطبيق الويب فقط
npm run build

# بناء وتوزيع التطبيق
npm run dist
```

### تشغيل التطبيق المبني
```bash
# تشغيل Electron مباشرة
npm run electron
```

## 📋 خطوات إنشاء خطة العمل

1. **المعلومات الشخصية ووصف المشروع**
   - بيانات صاحب المشروع
   - وصف شامل للمشروع وأهدافه

2. **دراسة السوق والمنافسين**
   - تحليل المنتجات والخدمات
   - دراسة المنافسة والأسعار

3. **التحليل الرباعي (SWOT)**
   - نقاط القوة والضعف
   - الفرص والتهديدات

4. **المزيج التسويقي (4Ps + 1)**
   - المنتج، السعر، المكان، الترويج، الأشخاص

5. **مستلزمات الإنتاج**
   - المعدات والمواد المطلوبة
   - الموارد البشرية والتراخيص

6. **الدراسة المالية**
   - التكاليف الثابتة والمتغيرة
   - تحليل الربحية ونقطة التعادل
   - توقعات المبيعات السنوية

7. **الإنهاء والحفظ**
   - ملخص المشروع
   - خيارات الحفظ والتصدير

## 🔧 التحسينات المضافة

### إدارة البيانات
- ✅ نظام إدارة بيانات مركزي باستخدام Context API
- ✅ حفظ تلقائي في Local Storage
- ✅ استرداد البيانات عند إعادة التحميل
- ✅ التحقق من صحة البيانات لكل خطوة

### تجربة المستخدم
- ✅ تنبيهات وإشعارات تفاعلية
- ✅ شريط تقدم محسن مع حالة الخطوات
- ✅ منع الانتقال مع بيانات ناقصة
- ✅ ملخص شامل للمشروع

### الحسابات المالية
- ✅ تحديث تلقائي للحسابات
- ✅ حساب نقطة التعادل
- ✅ تحليل الربحية التفاعلي
- ✅ جداول مالية شاملة

## 🎯 الميزات القادمة

- 📄 تصدير PDF احترافي
- 📊 تصدير Excel مع الرسوم البيانية
- 🌐 حفظ سحابي
- 📱 تطبيق موبايل
- 🤖 مساعد ذكي لتحليل البيانات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

---

**تم تطوير هذا التطبيق بواسطة: saif aldulaimi** 🚀

# دليل استخدام Advanced Installer لتطبيق خطة مشروعي 📦

## 🎯 لماذا Advanced Installer ممتاز؟

### المميزات:
- ✅ **واجهة مرئية سهلة** - لا يح<PERSON>اج كتابة كود
- ✅ **دعم العربية** - واجهة وتثبيت باللغة العربية
- ✅ **مثبتات احترافية** - مع شهادات رقمية
- ✅ **تحديثات تلقائية** - نظام تحديث مدمج
- ✅ **اختصارات ذكية** - سطح المكتب وقائمة ابدأ
- ✅ **إلغاء تثبيت نظيف** - حذف كامل للملفات

## 🚀 خطوات إنشاء المثبت:

### 1. تحضير الملفات
```bash
# أولاً، أنشئ النسخة المحمولة
.\create-portable.bat

# تأكد من وجود:
# - مجلد "خطة-مشروعي-محمول"
# - ملف "تشغيل التطبيق.bat"
# - مجلد "resources" مع الأيقونات
```

### 2. فتح Advanced Installer
1. افتح **Advanced Installer 22.8**
2. اختر **"Simple"** أو **"Professional"**
3. اختر **"New Project"**
4. اختر **"Generic Windows Installer"**

### 3. إعداد معلومات التطبيق

#### في تبويب **"Product Details"**:
- **Product Name**: `خطة مشروعي`
- **Product Version**: `1.0.0`
- **Publisher**: `saif aldulaimi`
- **Product Website**: (موقعك إن وجد)
- **Support URL**: (رابط الدعم)

#### في تبويب **"Install Parameters"**:
- **Application Folder**: `[ProgramFilesFolder]خطة مشروعي`
- **Application Shortcut Folder**: `خطة مشروعي`

### 4. إضافة الملفات

#### في تبويب **"Files and Folders"**:
1. انقر بالزر الأيمن على **"Application Folder"**
2. اختر **"Add Folder"**
3. اختر مجلد **"خطة-مشروعي-محمول"**
4. تأكد من إضافة جميع الملفات:
   - `dist-electron/`
   - `resources/`
   - `package.json`
   - `تشغيل التطبيق.bat`

### 5. إعداد الأيقونات

#### في تبويب **"Shortcuts"**:
1. **اختصار سطح المكتب**:
   - Name: `خطة مشروعي`
   - Target: `[APPDIR]تشغيل التطبيق.bat`
   - Icon: `[APPDIR]resources\icon.ico`
   - Description: `تطبيق إنشاء خطط الأعمال`

2. **اختصار قائمة ابدأ**:
   - Name: `خطة مشروعي`
   - Target: `[APPDIR]تشغيل التطبيق.bat`
   - Icon: `[APPDIR]resources\icon.ico`

### 6. إعداد اللغة العربية

#### في تبويب **"Translations"**:
1. انقر **"Add"**
2. اختر **"Arabic"** من القائمة
3. أو استخدم **"Arabic (Saudi Arabia)"**

#### تخصيص النصوص:
- **Welcome Message**: `مرحباً بك في معالج تثبيت خطة مشروعي`
- **Finish Message**: `تم تثبيت خطة مشروعي بنجاح`
- **License Agreement**: (أضف نص الترخيص بالعربية)

### 7. إعداد متطلبات النظام

#### في تبويب **"Launch Conditions"**:
1. **نظام التشغيل**: Windows 10 أو أحدث
2. **المعالج**: x64
3. **الذاكرة**: 4 GB RAM (اختياري)
4. **المساحة**: 500 MB

#### إضافة شرط Node.js:
1. انقر **"Add Launch Condition"**
2. اختر **"Software"**
3. أضف شرط للتحقق من Node.js

### 8. إعداد إلغاء التثبيت

#### في تبويب **"Uninstall"**:
- ✅ **Remove application folder**
- ✅ **Remove shortcuts**
- ✅ **Remove registry entries**
- ❌ **Remove user data** (اتركها للمستخدم)

### 9. بناء المثبت

#### في تبويب **"Media"**:
1. اختر **"Single MSI"** أو **"Single EXE"**
2. **Package Name**: `خطة-مشروعي-مثبت-1.0.0`
3. **Output Folder**: `dist-installer`

#### إعدادات البناء:
- **Compression**: `LZMA` (أفضل ضغط)
- **Package Type**: `EXE` (أسهل للمستخدمين)

### 10. بناء المثبت النهائي
1. انقر **"Build"** → **"Build Solution"**
2. انتظر حتى اكتمال البناء
3. ستجد المثبت في مجلد `dist-installer`

## 🎨 تخصيصات إضافية:

### إضافة صورة خلفية:
1. تبويب **"Dialogs"**
2. اختر **"Welcome Dialog"**
3. أضف صورة خلفية مخصصة

### إضافة اتفاقية ترخيص:
1. تبويب **"Dialogs"**
2. اختر **"License Agreement"**
3. أضف ملف `LICENSE.txt`

### إعداد التحديثات التلقائية:
1. تبويب **"Updater"**
2. فعل **"Enable Updater"**
3. أضف رابط التحديثات

## 🔧 سكريبت تحضير الملفات:

```bash
# prepare-for-advanced-installer.bat
@echo off
echo 📦 تحضير الملفات لـ Advanced Installer...

REM إنشاء النسخة المحمولة
call create-portable.bat

REM إنشاء مجلد المثبت
if not exist "installer-files" mkdir "installer-files"

REM نسخ الملفات المطلوبة
xcopy "خطة-مشروعي-محمول" "installer-files\" /E /I /Y
copy "LICENSE.txt" "installer-files\"
copy "README.md" "installer-files\"

echo ✅ الملفات جاهزة في مجلد: installer-files
echo 📋 الخطوة التالية:
echo    1. افتح Advanced Installer
echo    2. أنشئ مشروع جديد
echo    3. أضف مجلد installer-files
echo    4. اتبع الدليل أعلاه

pause
```

## 📋 قائمة التحقق:

- [ ] تم إنشاء النسخة المحمولة
- [ ] تم إعداد معلومات التطبيق
- [ ] تم إضافة جميع الملفات
- [ ] تم إعداد الاختصارات مع الأيقونات
- [ ] تم إعداد اللغة العربية
- [ ] تم اختبار المثبت على جهاز نظيف
- [ ] تم اختبار إلغاء التثبيت

## 🎯 النتيجة النهائية:

ستحصل على:
- ✅ **ملف مثبت احترافي** (.exe)
- ✅ **واجهة عربية كاملة**
- ✅ **اختصارات مع أيقونات**
- ✅ **إلغاء تثبيت نظيف**
- ✅ **حجم محسن** (أصغر من Electron Builder)

---
**Advanced Installer خيار ممتاز! ابدأ الآن** 🚀

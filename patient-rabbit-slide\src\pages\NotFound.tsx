import { useLocation, useNavigate } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname,
    );

    // إذا كان المسار فارغ أو "/" فقط، انتقل إلى الصفحة الرئيسية
    if (location.pathname === "/" || location.pathname === "") {
      navigate("/", { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleGoHome = () => {
    navigate("/", { replace: true });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="text-center p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl shadow-3d border border-white/20">
        <h1 className="text-6xl font-bold mb-4 text-gradient-primary">404</h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">عذراً! الصفحة غير موجودة</p>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">المسار المطلوب: {location.pathname}</p>
        <button
          onClick={handleGoHome}
          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          العودة إلى الصفحة الرئيسية
        </button>
      </div>
    </div>
  );
};

export default NotFound;

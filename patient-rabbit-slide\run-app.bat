@echo off
echo 🚀 تشغيل تطبيق خطة مشروعي...
echo.

REM التحقق من وجود ملفات Electron المبنية
if not exist "dist-electron\main.js" (
    echo ⚡ بناء ملفات Electron...
    npx tsc -p electron/tsconfig.json
    if errorlevel 1 (
        echo ❌ فشل في بناء ملفات Electron
        pause
        exit /b 1
    )
)

REM التحقق من وجود ملفات React المبنية
if not exist "dist\index.html" (
    echo ⚛️ بناء تطبيق React...
    npm run build
    if errorlevel 1 (
        echo ❌ فشل في بناء تطبيق React
        pause
        exit /b 1
    )
)

echo ✅ تشغيل التطبيق...
echo 📝 ملاحظة: إذا ظهرت صفحة 404، تأكد من أن التطبيق تم بناؤه بشكل صحيح
npx electron .

echo.
echo 🔚 تم إغلاق التطبيق
pause

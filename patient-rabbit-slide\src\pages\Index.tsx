import { ProjectFormWizard } from "@/components/ProjectFormWizard";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { Logo } from "@/components/Logo";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden">
      {/* خلفية متحركة */}
      <div className="absolute inset-0 bg-animated opacity-5"></div>

      {/* عناصر زخرفية */}
      <div className="absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
      <div className="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '2s'}}></div>
      <div className="absolute -bottom-32 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{animationDelay: '4s'}}></div>

      <div className="container mx-auto py-10 px-4 relative z-10">
        <header className="text-center mb-12">
          <div className="w-full max-w-4xl mx-auto mb-8 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-20"></div>
            <img
              src="https://images.unsplash.com/photo-1556740738-b6a63e27c4df?q=80&w=2070&auto=format&fit=crop"
              alt="Business Planning Session"
              className="relative rounded-2xl shadow-3d object-cover w-full h-64 border-2 border-white/20 backdrop-blur-sm"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
          </div>

          <div className="mb-8 animate-float">
            <Logo />
          </div>

          <h1 className="text-5xl md:text-6xl font-extrabold tracking-tight mb-6 text-gradient-primary animate-glow">
            حوّل فكرتك إلى مشروع ناجح
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            أداة متكاملة لمساعدتك على بناء خطة عمل احترافية خطوة بخطوة مع تقنيات حديثة وتصميم أنيق.
          </p>

          {/* شريط زخرفي */}
          <div className="mt-8 flex justify-center">
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg"></div>
          </div>
        </header>

        <main className="relative">
          {/* تأثير الإضاءة خلف النموذج */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-3xl"></div>
          <ProjectFormWizard />
        </main>

        <footer className="mt-16 text-center relative">
          <div className="inline-block p-4 rounded-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-3d border border-white/20">
            <MadeWithDyad />
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
@echo off
echo 🧹 تنظيف الملفات غير الضرورية...
echo.

REM حفظ نسخة احتياطية من الملفات المهمة
echo 💾 إنشاء نسخة احتياطية...
if not exist "backup" mkdir "backup"
copy "package.json" "backup\" >nul 2>&1
copy "package-lock.json" "backup\" >nul 2>&1

echo 📊 حجم المجلد قبل التنظيف:
for /f "tokens=3" %%a in ('dir /-c /s 2^>nul ^| find "bytes"') do set size_before=%%a

echo.
echo 🗑️ حذف الملفات غير الضرورية...

REM 1. حذف ملفات التطوير غير المطلوبة
echo   - ملفات التطوير...
if exist "node_modules" (
    echo     * node_modules (سيتم إعادة تثبيتها عند الحاجة)
    rmdir /s /q "node_modules" >nul 2>&1
)

if exist ".git" rmdir /s /q ".git" >nul 2>&1
if exist ".vscode" rmdir /s /q ".vscode" >nul 2>&1
if exist ".idea" rmdir /s /q ".idea" >nul 2>&1

REM 2. حذف ملفات البناء المؤقتة
echo   - ملفات البناء المؤقتة...
if exist "dist" rmdir /s /q "dist" >nul 2>&1
if exist "dist-app" rmdir /s /q "dist-app" >nul 2>&1

REM 3. حذف ملفات التوثيق الزائدة (الاحتفاظ بالأساسية فقط)
echo   - ملفات التوثيق الزائدة...
del "تحويل_إلى_PWA.md" >nul 2>&1
del "تحويل_إلى_Tauri.md" >nul 2>&1
del "تشخيص_المشاكل.md" >nul 2>&1
del "تغييرات_المطور.md" >nul 2>&1
del "دليل_Advanced_Installer.md" >nul 2>&1
del "دليل_التثبيت.md" >nul 2>&1
del "دليل_التوزيع_الشامل.md" >nul 2>&1
del "مواقع_الأيقونات.md" >nul 2>&1

REM 4. حذف سكريبتات البناء الزائدة (الاحتفاظ بالأساسية فقط)
echo   - سكريبتات البناء الزائدة...
del "build-installer.bat" >nul 2>&1
del "build-simple.bat" >nul 2>&1
del "create-icon.bat" >nul 2>&1
del "create-installer.bat" >nul 2>&1
del "create-portable.bat" >nul 2>&1
del "dev-app.bat" >nul 2>&1
del "create-ico-icon.bat" >nul 2>&1
del "installer-script.nsi" >nul 2>&1
del "installer.nsh" >nul 2>&1

REM 5. حذف مجلدات التثبيت المؤقتة
echo   - مجلدات التثبيت المؤقتة...
if exist "installer-files" rmdir /s /q "installer-files" >nul 2>&1
if exist "خطة-مشروعي-محمول" rmdir /s /q "خطة-مشروعي-محمول" >nul 2>&1

REM 6. حذف ملفات التكوين غير الضرورية للإنتاج
echo   - ملفات التكوين الزائدة...
del "eslint.config.js" >nul 2>&1
del "tsconfig.app.json" >nul 2>&1
del "tsconfig.node.json" >nul 2>&1
del "postcss.config.js" >nul 2>&1
del "tailwind.config.ts" >nul 2>&1
del "vite.config.ts" >nul 2>&1

REM 7. حذف ملفات الأيقونات الزائدة (الاحتفاظ بـ ICO فقط)
echo   - ملفات الأيقونات الزائدة...
if exist "public\favicon.ico" del "public\favicon.ico" >nul 2>&1
if exist "public\placeholder.svg" del "public\placeholder.svg" >nul 2>&1

REM 8. حذف ملفات المصدر (src) إذا كان dist-electron موجود
if exist "dist-electron\main.js" (
    echo   - ملفات المصدر (موجودة في dist-electron)...
    if exist "src" rmdir /s /q "src" >nul 2>&1
    if exist "electron" rmdir /s /q "electron" >nul 2>&1
    if exist "scripts" rmdir /s /q "scripts" >nul 2>&1
)

REM 9. حذف ملفات أخرى غير ضرورية
echo   - ملفات متنوعة...
del "AI_RULES.md" >nul 2>&1
del "components.json" >nul 2>&1
del "index.html" >nul 2>&1
del "App.css" >nul 2>&1

REM 10. تنظيف ملفات النظام المؤقتة
echo   - ملفات النظام المؤقتة...
del "Thumbs.db" >nul 2>&1
del "*.tmp" >nul 2>&1
del "*.log" >nul 2>&1

echo.
echo ✅ تم التنظيف بنجاح!

echo 📊 حجم المجلد بعد التنظيف:
for /f "tokens=3" %%a in ('dir /-c /s 2^>nul ^| find "bytes"') do set size_after=%%a

echo.
echo 📋 الملفات المتبقية (الضرورية فقط):
echo   ✅ dist-electron\          - ملفات التطبيق المبنية
echo   ✅ resources\icon.ico      - أيقونة التطبيق
echo   ✅ package.json           - معلومات التطبيق
echo   ✅ run-simple.bat         - ملف التشغيل
echo   ✅ LICENSE.txt            - ترخيص التطبيق
echo   ✅ README.md              - معلومات أساسية
echo   ✅ دليل_الاستخدام.md      - دليل المستخدم
echo   ✅ دليل_Advanced_Installer_مبسط.md - دليل التثبيت

echo.
echo 💡 ملاحظات مهمة:
echo   - تم حذف node_modules (سيتم إعادة تثبيتها عند الحاجة)
echo   - تم حذف ملفات المصدر (موجودة في dist-electron)
echo   - تم الاحتفاظ بالملفات الضرورية للتشغيل فقط
echo   - النسخة الاحتياطية في مجلد backup\

echo.
echo 🚀 التطبيق الآن جاهز لـ Advanced Installer!
echo    الملفات المطلوبة:
echo    - dist-electron\ (التطبيق)
echo    - resources\icon.ico (الأيقونة)
echo    - run-simple.bat (التشغيل)

pause

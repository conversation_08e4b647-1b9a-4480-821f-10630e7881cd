# مواقع أيقونات التطبيق 🎨

## 📍 الأيقونات الموجودة:

### 1. أيقونة SVG (للتطبيق)
- **الموقع**: `resources/icon.svg`
- **الاستخدام**: التطبيق نفسه، واجهة Electron
- **المميزات**: جودة عالية، قابلة للتكبير
- **الحجم**: متجه (Vector)

### 2. أيقونة ICO (للمثبت) ⭐
- **الموقع**: `resources/icon.ico`
- **الاستخدام**: مثبت Windows، اختصارات سطح المكتب
- **المميزات**: متوافقة مع Windows
- **الحجم**: 256x256 بكسل

## 🔧 كيفية استخدام الأيقونات:

### في التطبيق (Electron):
```typescript
// في electron/main.ts
icon: join(__dirname, '../resources/icon.svg')
```

### في المثبت (NSIS):
```nsis
; في installer-script.nsi
!define MUI_ICON "resources\icon.ico"
!define MUI_UNICON "resources\icon.ico"
```

### في Electron Builder:
```json
// في package.json
"win": {
  "icon": "resources/icon.ico"
}
```

## 📋 الملفات التي تستخدم الأيقونات:

### 1. ملفات التطبيق:
- `electron/main.ts` → `icon.svg`
- `package.json` → `icon.ico` (للبناء)

### 2. ملفات المثبت:
- `installer-script.nsi` → `icon.ico`
- `create-portable.bat` → ينسخ كلا الأيقونتين

### 3. الاختصارات:
- اختصار سطح المكتب → `icon.ico`
- اختصار قائمة ابدأ → `icon.ico`

## 🎨 تفاصيل التصميم:

### الأيقونة الحالية:
- **الشكل**: مصباح كهربائي (رمز الأفكار)
- **الألوان**: متدرج أزرق إلى بنفسجي
- **النص**: "خطة مشروعي" و "Business Plan Builder"
- **التأثيرات**: ظلال ثلاثية الأبعاد

### العناصر المرئية:
- 🔵 خلفية دائرية متدرجة
- 💡 مصباح كهربائي ذهبي
- ✨ خطوط إضاءة متحركة
- 📝 نص عربي وإنجليزي

## 🔄 تحديث الأيقونات:

### لتغيير الأيقونة:
1. **استبدال SVG**: ضع أيقونة جديدة في `resources/icon.svg`
2. **إنشاء ICO جديدة**: شغل `.\create-ico-icon.bat`
3. **إعادة البناء**: شغل `.\create-portable.bat`

### أدوات التحويل:
- **ImageMagick**: للتحويل المحلي
- **Convertio.co**: للتحويل عبر الإنترنت
- **GIMP/Photoshop**: للتحرير المتقدم

## 📦 في النسخة المحمولة:

عند تشغيل `create-portable.bat`، يتم نسخ:
- `resources/icon.svg` → للتطبيق
- `resources/icon.ico` → للاختصارات

## 🚀 في المثبت النهائي:

عند تشغيل `create-installer.bat`، يتم استخدام:
- `icon.ico` → أيقونة المثبت
- `icon.ico` → أيقونة الاختصارات
- `icon.ico` → أيقونة إلغاء التثبيت

## ✅ التحقق من الأيقونات:

```bash
# التحقق من وجود الأيقونات
dir resources\*.ico
dir resources\*.svg

# إنشاء أيقونة ICO جديدة
.\create-ico-icon.bat

# اختبار المثبت
.\create-installer.bat
```

---
**جميع الأيقونات جاهزة ومُحدثة!** 🎉

**الموقع الرئيسي**: `patient-rabbit-slide/resources/`
- `icon.svg` (للتطبيق)
- `icon.ico` (للمثبت والاختصارات)

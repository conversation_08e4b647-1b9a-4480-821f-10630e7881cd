"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const fs_1 = __importDefault(require("fs"));
// تحديد ما إذا كان التطبيق في وضع التطوير
const isDev = process.env.NODE_ENV === 'development' || process.env.ELECTRON_IS_DEV === 'true';
// إعداد المتغيرات العامة
let mainWindow = null;
// إنشاء النافذة الرئيسية
function createWindow() {
    // إنشاء نافذة المتصفح
    mainWindow = new electron_1.BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        show: false,
        autoHideMenuBar: false,
        titleBarStyle: 'default',
        icon: (0, path_1.join)(__dirname, '../resources/icon.svg'),
        webPreferences: {
            preload: (0, path_1.join)(__dirname, 'preload.js'),
            sandbox: false,
            contextIsolation: true,
            nodeIntegration: false
        },
        title: 'خطة مشروعي - Business Plan Builder'
    });
    // إعداد الأحداث
    mainWindow.on('ready-to-show', () => {
        if (mainWindow) {
            mainWindow.show();
            // فتح أدوات المطور في وضع التطوير
            if (isDev) {
                mainWindow.webContents.openDevTools();
            }
        }
    });
    mainWindow.webContents.setWindowOpenHandler((details) => {
        electron_1.shell.openExternal(details.url);
        return { action: 'deny' };
    });
    // تحميل التطبيق
    if (isDev && process.env['ELECTRON_RENDERER_URL']) {
        mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL']);
    }
    else {
        mainWindow.loadFile((0, path_1.join)(__dirname, '../dist/index.html'));
    }
}
// إعداد القائمة العربية
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'مشروع جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        // إعادة تحميل التطبيق لبدء مشروع جديد
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                {
                    label: 'حفظ المشروع',
                    accelerator: 'CmdOrCtrl+S',
                    click: async () => {
                        // حفظ بيانات المشروع
                        if (mainWindow) {
                            mainWindow.webContents.send('save-project');
                        }
                    }
                },
                {
                    label: 'فتح مشروع',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        const result = await electron_1.dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'ملفات خطة العمل', extensions: ['json'] },
                                { name: 'جميع الملفات', extensions: ['*'] }
                            ]
                        });
                        if (!result.canceled && result.filePaths.length > 0) {
                            try {
                                const data = fs_1.default.readFileSync(result.filePaths[0], 'utf8');
                                if (mainWindow) {
                                    mainWindow.webContents.send('load-project', JSON.parse(data));
                                }
                            }
                            catch (error) {
                                electron_1.dialog.showErrorBox('خطأ', 'فشل في فتح الملف');
                            }
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'تصدير PDF',
                    accelerator: 'CmdOrCtrl+E',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('export-pdf');
                        }
                    }
                },
                {
                    label: 'تصدير Excel',
                    accelerator: 'CmdOrCtrl+Shift+E',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.send('export-excel');
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        electron_1.app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' },
                { label: 'تحديد الكل', accelerator: 'CmdOrCtrl+A', role: 'selectAll' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        electron_1.dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'خطة مشروعي - Business Plan Builder',
                            detail: 'تطبيق متكامل لإنشاء خطط الأعمال باللغة العربية\nالإصدار 1.0.0\n\nتم تطويره بواسطة: saif aldulaimi'
                        });
                    }
                }
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    electron_1.Menu.setApplicationMenu(menu);
}
// معالجة أحداث IPC
electron_1.ipcMain.handle('save-project-file', async (event, data) => {
    const result = await electron_1.dialog.showSaveDialog(mainWindow, {
        defaultPath: 'خطة-المشروع.json',
        filters: [
            { name: 'ملفات خطة العمل', extensions: ['json'] },
            { name: 'جميع الملفات', extensions: ['*'] }
        ]
    });
    if (!result.canceled && result.filePath) {
        try {
            fs_1.default.writeFileSync(result.filePath, JSON.stringify(data, null, 2));
            return { success: true, path: result.filePath };
        }
        catch (error) {
            return { success: false, error: 'فشل في حفظ الملف' };
        }
    }
    return { success: false, error: 'تم إلغاء العملية' };
});
// تجهيز التطبيق
electron_1.app.whenReady().then(() => {
    // تحسين التطبيق للنوافذ والماك
    electron_1.app.setAppUserModelId('com.dyad.business-plan-builder');
    createWindow();
    createMenu();
    electron_1.app.on('activate', function () {
        if (electron_1.BrowserWindow.getAllWindows().length === 0)
            createWindow();
    });
});
// إغلاق التطبيق عند إغلاق جميع النوافذ
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin')
        electron_1.app.quit();
});
// معالجة الأمان - منع التنقل غير المرغوب فيه
electron_1.app.on('web-contents-created', (_, contents) => {
    contents.on('will-navigate', (navigationEvent, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        if (parsedUrl.origin !== 'http://localhost:5173' && parsedUrl.origin !== 'file://') {
            navigationEvent.preventDefault();
        }
    });
});
//# sourceMappingURL=main.js.map
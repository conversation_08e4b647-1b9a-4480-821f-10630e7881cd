@echo off
echo 🎨 إنشاء أيقونة ICO لتطبيق خطة مشروعي...
echo.

REM التحقق من وجود ImageMagick
where magick >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم العثور على ImageMagick
    echo 🔄 تحويل SVG إلى ICO...
    magick resources\icon.svg -background transparent -resize 256x256 resources\icon.ico
    if exist resources\icon.ico (
        echo ✅ تم إنشاء icon.ico بنجاح!
        echo 📁 الموقع: resources\icon.ico
    ) else (
        echo ❌ فشل في إنشاء ملف ICO
    )
) else (
    echo ⚠️ ImageMagick غير مثبت
    echo.
    echo 📝 طرق إنشاء أيقونة ICO:
    echo.
    echo 1. تثبيت ImageMagick:
    echo    - اذهب إلى: https://imagemagick.org/script/download.php#windows
    echo    - حمل وثبت ImageMagick
    echo    - أعد تشغيل هذا السكريبت
    echo.
    echo 2. استخدام موقع تحويل عبر الإنترنت:
    echo    - اذهب إلى: https://convertio.co/svg-ico/
    echo    - ارفع ملف resources\icon.svg
    echo    - حمل الملف المحول واحفظه كـ resources\icon.ico
    echo.
    echo 3. استخدام برنامج تحرير الصور:
    echo    - افتح resources\icon.svg في GIMP أو Photoshop
    echo    - صدر كـ ICO بحجم 256x256
    echo    - احفظ في resources\icon.ico
    echo.
    
    REM إنشاء أيقونة ICO بسيطة باستخدام PowerShell (حل بديل)
    echo 🔧 محاولة إنشاء أيقونة بسيطة...
    powershell -Command "Add-Type -AssemblyName System.Drawing; $bitmap = New-Object System.Drawing.Bitmap(256, 256); $graphics = [System.Drawing.Graphics]::FromImage($bitmap); $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Blue); $graphics.FillEllipse($brush, 50, 50, 156, 156); $font = New-Object System.Drawing.Font('Arial', 24, [System.Drawing.FontStyle]::Bold); $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White); $graphics.DrawString('خ', $font, $textBrush, 110, 110); $bitmap.Save('resources\icon.ico', [System.Drawing.Imaging.ImageFormat]::Icon); $graphics.Dispose(); $bitmap.Dispose()"
    
    if exist resources\icon.ico (
        echo ✅ تم إنشاء أيقونة بسيطة بنجاح!
        echo 📁 الموقع: resources\icon.ico
    ) else (
        echo ❌ فشل في إنشاء الأيقونة البسيطة
        echo 💡 يرجى استخدام إحدى الطرق المذكورة أعلاه
    )
)

echo.
echo 📋 ملخص أيقونات التطبيق:
if exist resources\icon.svg (
    echo ✅ أيقونة SVG: resources\icon.svg
)
if exist resources\icon.ico (
    echo ✅ أيقونة ICO: resources\icon.ico
)

echo.
echo 💡 ملاحظة: ملف ICO مطلوب للمثبتات، بينما SVG يعمل مع التطبيق نفسه
pause

@echo off
echo 🎯 Creating Clean Version for Distribution...
echo.

REM Create clean directory
set CLEAN_DIR=business-plan-clean
if exist "%CLEAN_DIR%" rmdir /s /q "%CLEAN_DIR%"
mkdir "%CLEAN_DIR%"

echo 📁 Creating clean directory: %CLEAN_DIR%

REM Copy essential files only
echo 📋 Copying essential files...

REM 1. Application files
if exist "dist-electron" (
    echo   ✅ dist-electron (application files)
    xcopy "dist-electron" "%CLEAN_DIR%\dist-electron\" /E /I /Q
) else (
    echo   ⚠️ dist-electron not found - building...
    npx tsc -p electron/tsconfig.json
    xcopy "dist-electron" "%CLEAN_DIR%\dist-electron\" /E /I /Q
)

REM 2. Icons
if exist "resources\icon.ico" (
    echo   ✅ resources\icon.ico (application icon)
    mkdir "%CLEAN_DIR%\resources"
    copy "resources\icon.ico" "%CLEAN_DIR%\resources\"
)

REM 3. Simplified package.json
echo   ✅ package.json (simplified)
echo { > "%CLEAN_DIR%\package.json"
echo   "name": "business-plan-builder", >> "%CLEAN_DIR%\package.json"
echo   "version": "1.0.0", >> "%CLEAN_DIR%\package.json"
echo   "description": "Business Plan Builder in Arabic", >> "%CLEAN_DIR%\package.json"
echo   "main": "dist-electron/main.js", >> "%CLEAN_DIR%\package.json"
echo   "author": "saif aldulaimi", >> "%CLEAN_DIR%\package.json"
echo   "scripts": { >> "%CLEAN_DIR%\package.json"
echo     "start": "electron ." >> "%CLEAN_DIR%\package.json"
echo   }, >> "%CLEAN_DIR%\package.json"
echo   "devDependencies": { >> "%CLEAN_DIR%\package.json"
echo     "electron": "^latest" >> "%CLEAN_DIR%\package.json"
echo   } >> "%CLEAN_DIR%\package.json"
echo } >> "%CLEAN_DIR%\package.json"

REM 4. Run script
echo   ✅ run-app.bat (startup script)
echo @echo off > "%CLEAN_DIR%\run-app.bat"
echo echo Starting Business Plan Builder... >> "%CLEAN_DIR%\run-app.bat"
echo cd /d "%%~dp0" >> "%CLEAN_DIR%\run-app.bat"
echo if not exist node_modules\electron ( >> "%CLEAN_DIR%\run-app.bat"
echo     echo Installing Electron... >> "%CLEAN_DIR%\run-app.bat"
echo     npm install electron >> "%CLEAN_DIR%\run-app.bat"
echo ^) >> "%CLEAN_DIR%\run-app.bat"
echo npx electron . >> "%CLEAN_DIR%\run-app.bat"
echo pause >> "%CLEAN_DIR%\run-app.bat"

REM 5. Documentation
if exist "LICENSE.txt" (
    echo   ✅ LICENSE.txt
    copy "LICENSE.txt" "%CLEAN_DIR%\"
)

echo   ✅ User Guide
echo # Business Plan Builder > "%CLEAN_DIR%\README.txt"
echo. >> "%CLEAN_DIR%\README.txt"
echo How to run: >> "%CLEAN_DIR%\README.txt"
echo 1. Double-click run-app.bat >> "%CLEAN_DIR%\README.txt"
echo 2. Wait for the application to load >> "%CLEAN_DIR%\README.txt"
echo. >> "%CLEAN_DIR%\README.txt"
echo Requirements: >> "%CLEAN_DIR%\README.txt"
echo - Node.js installed on the system >> "%CLEAN_DIR%\README.txt"
echo - Internet connection for first run >> "%CLEAN_DIR%\README.txt"
echo. >> "%CLEAN_DIR%\README.txt"
echo Developed by: saif aldulaimi >> "%CLEAN_DIR%\README.txt"

echo.
echo ✅ Clean version created successfully! 🎉
echo.
echo 📁 Directory: %CLEAN_DIR%\
echo.
echo 📋 Contents:
dir "%CLEAN_DIR%" /b
echo.
echo 🎯 Ready for:
echo   1. Advanced Installer (add entire folder)
echo   2. Direct distribution (copy folder)
echo   3. Compression and sharing
echo.
echo 💡 Main executable: run-app.bat
echo 🎨 Icon for installer: resources\icon.ico
echo.
pause

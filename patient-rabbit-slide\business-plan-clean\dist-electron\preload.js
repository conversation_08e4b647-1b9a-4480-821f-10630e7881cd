"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// تعريف API في السياق الآمن
const electronAPI = {
    // حفظ ملف المشروع
    saveProjectFile: (data) => electron_1.ipcRenderer.invoke('save-project-file', data),
    // مستمعي الأحداث من القائمة
    onSaveProject: (callback) => {
        electron_1.ipcRenderer.on('save-project', callback);
    },
    onLoadProject: (callback) => {
        electron_1.ipcRenderer.on('load-project', (_, data) => callback(data));
    },
    onExportPDF: (callback) => {
        electron_1.ipcRenderer.on('export-pdf', callback);
    },
    onExportExcel: (callback) => {
        electron_1.ipcRenderer.on('export-excel', callback);
    },
    // معلومات النظام
    platform: process.platform,
    // وظائف النافذة
    minimize: () => electron_1.ipcRenderer.send('window-minimize'),
    maximize: () => electron_1.ipcRenderer.send('window-maximize'),
    close: () => electron_1.ipcRenderer.send('window-close')
};
// تصدير API للتطبيق
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
// إضافة مستمع لأحداث DOM
window.addEventListener('DOMContentLoaded', () => {
    // إضافة معلومات النظام للعنصر الجذر
    const root = document.getElementById('root');
    if (root) {
        root.setAttribute('data-platform', process.platform);
    }
    // إضافة فئة CSS للنظام
    document.body.classList.add(`platform-${process.platform}`);
    // إعداد معالجات اختصارات لوحة المفاتيح
    document.addEventListener('keydown', (event) => {
        // Ctrl/Cmd + S للحفظ
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            electron_1.ipcRenderer.send('save-project');
        }
        // Ctrl/Cmd + O لفتح ملف
        if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
            event.preventDefault();
            // سيتم التعامل معه في القائمة
        }
        // F11 للشاشة الكاملة
        if (event.key === 'F11') {
            event.preventDefault();
            electron_1.ipcRenderer.send('toggle-fullscreen');
        }
    });
});
// معالجة أخطاء غير متوقعة
window.addEventListener('error', (event) => {
    console.error('خطأ في التطبيق:', event.error);
});
window.addEventListener('unhandledrejection', (event) => {
    console.error('رفض غير معالج:', event.reason);
});
// تصدير النوع للاستخدام في TypeScript
// export type { ElectronAPI };
//# sourceMappingURL=preload.js.map
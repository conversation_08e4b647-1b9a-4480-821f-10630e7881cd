@echo off
echo 📦 إنشاء نسخة محمولة من تطبيق خطة مشروعي...
echo.

REM إنشاء مجلد النسخة المحمولة
set PORTABLE_DIR=خطة-مشروعي-محمول
if exist "%PORTABLE_DIR%" rmdir /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

echo 📁 إنشاء مجلد النسخة المحمولة...

REM نسخ الملفات الأساسية
echo 📋 نسخ الملفات الأساسية...

REM نسخ package.json مع التعديلات
echo {"name":"business-plan-builder","version":"1.0.0","description":"تطبيق متكامل لإنشاء خطط الأعمال باللغة العربية","main":"dist-electron/main.js","author":"sa<PERSON>mi","scripts":{"start":"electron ."}} > "%PORTABLE_DIR%\package.json"

REM نسخ مجلد electron المبني
if exist dist-electron (
    echo ✅ نسخ ملفات Electron...
    xcopy dist-electron "%PORTABLE_DIR%\dist-electron\" /E /I /Q
) else (
    echo ⚡ بناء ملفات Electron...
    npx tsc -p electron/tsconfig.json
    if errorlevel 1 (
        echo ❌ فشل في بناء ملفات Electron
        pause
        exit /b 1
    )
    xcopy dist-electron "%PORTABLE_DIR%\dist-electron\" /E /I /Q
)

REM نسخ مجلد الموارد
if exist resources (
    echo ✅ نسخ الموارد...
    xcopy resources "%PORTABLE_DIR%\resources\" /E /I /Q
)

REM نسخ مجلد dist إذا كان موجوداً، وإلا إنشاء نسخة مبسطة
if exist dist (
    echo ✅ نسخ ملفات التطبيق المبنية...
    xcopy dist "%PORTABLE_DIR%\dist\" /E /I /Q
) else (
    echo 🔧 إنشاء ملفات HTML مبسطة...
    mkdir "%PORTABLE_DIR%\dist"
    
    REM إنشاء ملف HTML مبسط
    echo ^<!DOCTYPE html^> > "%PORTABLE_DIR%\dist\index.html"
    echo ^<html lang="ar" dir="rtl"^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^<head^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^<meta charset="UTF-8"^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^<title^>خطة مشروعي^</title^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^</head^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^<body^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^<div id="root"^>^</div^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^<script^>document.getElementById('root').innerHTML = '^<h1 style="text-align:center;font-family:Arial;color:#333;margin-top:50px;"^>تطبيق خطة مشروعي^<br^>^<small^>تم تطويره بواسطة: saif aldulaimi^</small^>^</h1^>';^</script^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^</body^> >> "%PORTABLE_DIR%\dist\index.html"
    echo ^</html^> >> "%PORTABLE_DIR%\dist\index.html"
)

REM إنشاء ملف تشغيل
echo @echo off > "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo echo 🚀 تشغيل تطبيق خطة مشروعي... >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo echo. >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo if not exist node_modules\electron ( >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo     echo 📥 تثبيت Electron... >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo     npm install electron >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo ^) >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo npx electron . >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"
echo pause >> "%PORTABLE_DIR%\تشغيل التطبيق.bat"

REM إنشاء ملف README
echo # تطبيق خطة مشروعي - النسخة المحمولة > "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo ## كيفية التشغيل: >> "%PORTABLE_DIR%\README.md"
echo 1. انقر مرتين على "تشغيل التطبيق.bat" >> "%PORTABLE_DIR%\README.md"
echo 2. انتظر حتى يتم تحميل التطبيق >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo ## المتطلبات: >> "%PORTABLE_DIR%\README.md"
echo - Node.js مثبت على النظام >> "%PORTABLE_DIR%\README.md"
echo - اتصال بالإنترنت للتحميل الأول >> "%PORTABLE_DIR%\README.md"
echo. >> "%PORTABLE_DIR%\README.md"
echo تم تطويره بواسطة: saif aldulaimi >> "%PORTABLE_DIR%\README.md"

echo.
echo ✅ تم إنشاء النسخة المحمولة بنجاح! 🎉
echo 📁 المجلد: %PORTABLE_DIR%
echo.
echo 🚀 لتشغيل التطبيق:
echo    1. ادخل إلى مجلد "%PORTABLE_DIR%"
echo    2. انقر مرتين على "تشغيل التطبيق.bat"
echo.
echo 📦 يمكنك نسخ مجلد "%PORTABLE_DIR%" بالكامل إلى أي جهاز آخر
pause

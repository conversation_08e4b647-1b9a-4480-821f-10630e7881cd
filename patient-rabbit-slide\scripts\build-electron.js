import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 بدء بناء تطبيق Electron...');

// تنظيف المجلدات السابقة
console.log('🧹 تنظيف الملفات السابقة...');
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}
if (fs.existsSync('dist-electron')) {
  fs.rmSync('dist-electron', { recursive: true, force: true });
}
if (fs.existsSync('dist-app')) {
  fs.rmSync('dist-app', { recursive: true, force: true });
}

try {
  // بناء تطبيق React
  console.log('⚛️ بناء تطبيق React...');
  execSync('npm run build', { stdio: 'inherit' });

  // بناء ملفات Electron
  console.log('⚡ بناء ملفات Electron...');
  execSync('npx tsc -p electron/tsconfig.json', { stdio: 'inherit' });

  // نسخ الملفات المطلوبة
  console.log('📁 نسخ الملفات...');
  
  // إنشاء مجلد dist-electron إذا لم يكن موجوداً
  if (!fs.existsSync('dist-electron')) {
    fs.mkdirSync('dist-electron', { recursive: true });
  }

  // نسخ package.json مع التعديلات المطلوبة
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const electronPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    description: packageJson.description,
    main: 'main.js',
    author: packageJson.author || 'Dyad Platform',
    license: packageJson.license || 'MIT'
  };
  
  fs.writeFileSync(
    path.join('dist-electron', 'package.json'),
    JSON.stringify(electronPackageJson, null, 2)
  );

  // بناء التطبيق النهائي
  console.log('📦 بناء التطبيق النهائي...');
  execSync('npx electron-builder', { stdio: 'inherit' });

  console.log('✅ تم بناء التطبيق بنجاح!');
  console.log('📁 يمكنك العثور على التطبيق في مجلد dist-app');

} catch (error) {
  console.error('❌ خطأ في بناء التطبيق:', error.message);
  process.exit(1);
}

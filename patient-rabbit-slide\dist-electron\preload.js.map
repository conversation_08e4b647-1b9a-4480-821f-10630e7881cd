{"version": 3, "file": "preload.js", "sourceRoot": "", "sources": ["../electron/preload.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AAsBtD,4BAA4B;AAC5B,MAAM,WAAW,GAAgB;IAC/B,kBAAkB;IAClB,eAAe,EAAE,CAAC,IAAS,EAAE,EAAE,CAAC,sBAAW,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAE7E,4BAA4B;IAC5B,aAAa,EAAE,CAAC,QAAoB,EAAE,EAAE;QACtC,sBAAW,CAAC,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,aAAa,EAAE,CAAC,QAA6B,EAAE,EAAE;QAC/C,sBAAW,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,WAAW,EAAE,CAAC,QAAoB,EAAE,EAAE;QACpC,sBAAW,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED,aAAa,EAAE,CAAC,QAAoB,EAAE,EAAE;QACtC,sBAAW,CAAC,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,iBAAiB;IACjB,QAAQ,EAAE,OAAO,CAAC,QAAQ;IAE1B,gBAAgB;IAChB,QAAQ,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACnD,QAAQ,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACnD,KAAK,EAAE,GAAG,EAAE,CAAC,sBAAW,CAAC,IAAI,CAAC,cAAc,CAAC;CAC9C,CAAC;AAEF,oBAAoB;AACpB,wBAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAS5D,yBAAyB;AACzB,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC/C,oCAAoC;IACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED,uBAAuB;IACvB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE5D,uCAAuC;IACvC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAoB,EAAE,EAAE;QAC5D,qBAAqB;QACrB,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;YAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,sBAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnC,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;YAC1D,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,8BAA8B;QAChC,CAAC;QAED,qBAAqB;QACrB,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,EAAE,CAAC;YACxB,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,sBAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxC,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,0BAA0B;AAC1B,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAiB,EAAE,EAAE;IACrD,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,KAA4B,EAAE,EAAE;IAC7E,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC;AAEH,sCAAsC;AACtC,+BAA+B"}
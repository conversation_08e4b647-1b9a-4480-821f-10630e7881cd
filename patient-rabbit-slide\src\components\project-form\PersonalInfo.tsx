import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";

interface FormFieldProps {
  label: string;
  id: keyof import("@/types/project").PersonalInfo;
  type?: string;
}

const FormField = ({ label, id, type = "text" }: FormFieldProps) => {
  const { projectData, updatePersonalInfo } = useProject();
  const value = projectData.personalInfo[id];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = type === "number" ? Number(e.target.value) : e.target.value;
    updatePersonalInfo({ [id]: newValue });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 items-center gap-4 group">
      <Label
        htmlFor={id}
        className="md:text-right font-semibold text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300"
      >
        {label}
      </Label>
      <Input
        id={id}
        type={type}
        value={value || ''}
        onChange={handleChange}
        className="col-span-1 md:col-span-2 input-3d shadow-lg border-2 border-white/50 dark:border-slate-600/50 focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-300 hover:shadow-xl"
      />
    </div>
  );
};

export const PersonalInfo = () => {
  return (
    <Card className="w-full card-3d shadow-3d-hover border-glow bg-gradient-to-br from-sky-50/90 via-blue-50/90 to-indigo-50/90 dark:from-sky-900/30 dark:via-blue-900/30 dark:to-indigo-900/30 backdrop-blur-sm relative overflow-hidden">
      {/* تأثير الخلفية المتحركة */}
      <div className="absolute inset-0 bg-gradient-to-r from-sky-400/5 to-blue-400/5 animate-pulse"></div>

      <CardHeader className="border-b border-sky-200/50 dark:border-sky-700/50 relative z-10 bg-gradient-to-r from-white/50 to-sky-50/50 dark:from-slate-800/50 dark:to-sky-900/50">
        <CardTitle className="flex items-center gap-4 text-2xl md:text-3xl font-bold">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full blur opacity-50"></div>
            <div className="relative w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
              <User className="h-6 w-6 text-white" />
            </div>
          </div>
          <span className="text-gradient-primary">المعلومات الشخصية</span>
        </CardTitle>
        <div className="mt-2 w-16 h-1 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full shadow-lg"></div>
      </CardHeader>

      <CardContent className="pt-8 pb-6 relative z-10">
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField label="إسم صاحب/ة المشروع" id="ownerName" />
            <FormField label="العمر" id="age" type="number" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField label="الحالة الإجتماعية" id="maritalStatus" />
            <FormField label="عدد أفراد الأسرة" id="familySize" type="number" />
          </div>
          <FormField label="المؤهل العلمي" id="education" />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField label="رقم الهاتف" id="phone" />
            <FormField label="رقم هاتف شخص معرف" id="refereePhone" />
          </div>
          <FormField label="مكان السكن" id="address" />
        </div>
      </CardContent>

      {/* تأثير الإضاءة السفلية */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-sky-500 to-blue-500 opacity-50"></div>
    </Card>
  );
};
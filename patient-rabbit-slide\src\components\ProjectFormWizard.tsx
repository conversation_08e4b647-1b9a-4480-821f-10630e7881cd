import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, CheckCircle, AlertCircle } from "lucide-react";
import { FormProgress } from "./FormProgress";
import { useProject } from "@/contexts/ProjectContext";
import { toast } from "@/hooks/use-toast";

// استيراد مكونات الخطوات
import { Step1_Intro } from "./wizard-steps/Step1_Intro";
import { Step2_MarketAnalysis } from "./wizard-steps/Step2_MarketAnalysis";
import { Step3_SwotAnalysis } from "./wizard-steps/Step3_SwotAnalysis";
import { Step4_MarketingMix } from "./wizard-steps/Step4_MarketingMix";
import { Step5_ProductionReqs } from "./wizard-steps/Step5_ProductionReqs";
import { Step6_FinancialStudy } from "./wizard-steps/Step6_FinancialStudy";
import { Step7_Final } from "./wizard-steps/Step7_Final";

const steps = [
  { id: 1, title: "المعلومات الشخصية ووصف المشروع", component: <Step1_Intro /> },
  { id: 2, title: "دراسة السوق والمنافسين", component: <Step2_MarketAnalysis /> },
  { id: 3, title: "التحليل الرباعي (SWOT)", component: <Step3_SwotAnalysis /> },
  { id: 4, title: "المزيج التسويقي (4Ps + 1)", component: <Step4_MarketingMix /> },
  { id: 5, title: "مستلزمات الإنتاج", component: <Step5_ProductionReqs /> },
  { id: 6, title: "الدراسة المالية", component: <Step6_FinancialStudy /> },
  { id: 7, title: "حفظ وتصدير", component: <Step7_Final /> },
];

export const ProjectFormWizard = () => {
  const { projectData, setCurrentStep, isDataValid, saveProject } = useProject();
  const currentStep = projectData.currentStep;

  const goToNext = () => {
    // التحقق من صحة البيانات قبل الانتقال
    if (!isDataValid(currentStep)) {
      toast({
        title: "بيانات غير مكتملة",
        description: "يرجى إكمال البيانات المطلوبة قبل الانتقال للخطوة التالية",
        variant: "destructive",
      });
      return;
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      toast({
        title: "تم الحفظ",
        description: "تم حفظ بيانات الخطوة الحالية تلقائياً",
      });
    }
  };

  const goToPrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinish = () => {
    if (!isDataValid(currentStep)) {
      toast({
        title: "بيانات غير مكتملة",
        description: "يرجى إكمال البيانات المطلوبة قبل الإنهاء",
        variant: "destructive",
      });
      return;
    }

    saveProject();
    toast({
      title: "تم إنهاء المشروع بنجاح!",
      description: "تم حفظ جميع بيانات خطة العمل",
    });
  };

  const CurrentComponent = steps[currentStep].component;

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8 relative">
      {/* تأثير الإضاءة المحيطة */}
      <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-2xl opacity-30"></div>

      <div className="relative">
        <FormProgress currentStep={currentStep} totalSteps={steps.length} />
      </div>

      <Card className="card-3d shadow-3d-hover border-glow relative overflow-hidden">
        {/* تأثير الخلفية المتدرجة */}
        <div className="absolute inset-0 bg-gradient-card"></div>

        <CardHeader className="relative z-10 border-b border-white/20 dark:border-slate-700/50">
          <CardTitle className="text-gradient-primary text-2xl md:text-3xl font-bold flex items-center gap-3">
            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full shadow-lg"></div>
            {steps[currentStep].title}
          </CardTitle>
          <CardDescription className="text-lg text-gray-600 dark:text-gray-300 mt-2">
            الخطوة {currentStep + 1} من {steps.length} • {Math.round(((currentStep + 1) / steps.length) * 100)}% مكتمل
          </CardDescription>
        </CardHeader>

        <CardContent className="relative z-10 p-6 md:p-8">
          <div className="relative">
            {/* تأثير الإضاءة الداخلية */}
            <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl"></div>
            <div className="relative">
              {CurrentComponent}
            </div>
          </div>
        </CardContent>
        <CardFooter className="relative z-10 flex justify-between items-center p-6 md:p-8 border-t border-white/20 dark:border-slate-700/50 bg-gradient-to-r from-white/50 to-gray-50/50 dark:from-slate-800/50 dark:to-slate-700/50">
          <Button
            onClick={goToPrevious}
            disabled={currentStep === 0}
            variant="outline"
            className="btn-3d shadow-3d border-2 border-white/30 dark:border-slate-600/30 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowRight className="ml-2 h-4 w-4" />
            السابق
          </Button>

          <div className="flex items-center gap-2">
            {!isDataValid(currentStep) && (
              <div className="flex items-center gap-2 px-3 py-2 rounded-full bg-amber-100 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700/50 shadow-lg backdrop-blur-sm">
                <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400 animate-pulse" />
                <span className="text-sm font-medium text-amber-700 dark:text-amber-300">بيانات غير مكتملة</span>
              </div>
            )}
          </div>

          {currentStep < steps.length - 1 ? (
            <Button
              onClick={goToNext}
              className="btn-3d shadow-3d bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
              <span className="relative flex items-center">
                التالي
                <ArrowLeft className="mr-2 h-4 w-4" />
              </span>
            </Button>
          ) : (
            <Button
              onClick={handleFinish}
              className="btn-3d shadow-3d bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 relative overflow-hidden animate-glow"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
              <span className="relative flex items-center">
                إنهاء وحفظ
                <CheckCircle className="mr-2 h-4 w-4" />
              </span>
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};
; تخصيص مثبت NSIS لتطبيق خطة مشروعي
; Custom NSIS installer script for Business Plan Builder

; إعدادات اللغة العربية
!define MUI_LANGDLL_ALLLANGUAGES

; رسائل مخصصة
LangString welcome ${LANG_ARABIC} "مرحباً بك في معالج تثبيت تطبيق خطة مشروعي"
LangString welcome ${LANG_ENGLISH} "Welcome to Business Plan Builder Setup Wizard"

LangString finish ${LANG_ARABIC} "تم تثبيت تطبيق خطة مشروعي بنجاح"
LangString finish ${LANG_ENGLISH} "Business Plan Builder has been successfully installed"

; إضافة معلومات إضافية للمثبت
!macro customInstall
  ; إنشاء اختصار على سطح المكتب مع وصف عربي
  CreateShortCut "$DESKTOP\خطة مشروعي.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\icon.ico" 0 SW_SHOWNORMAL "" "تطبيق إنشاء خطط الأعمال"
  
  ; إنشاء اختصار في قائمة ابدأ
  CreateDirectory "$SMPROGRAMS\خطة مشروعي"
  CreateShortCut "$SMPROGRAMS\خطة مشروعي\خطة مشروعي.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\icon.ico" 0 SW_SHOWNORMAL "" "تطبيق إنشاء خطط الأعمال"
  CreateShortCut "$SMPROGRAMS\خطة مشروعي\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall ${APP_EXECUTABLE_FILENAME}.exe"
  
  ; تسجيل نوع الملف .json لخطط العمل
  WriteRegStr HKCR ".businessplan" "" "BusinessPlanFile"
  WriteRegStr HKCR "BusinessPlanFile" "" "ملف خطة العمل"
  WriteRegStr HKCR "BusinessPlanFile\DefaultIcon" "" "$INSTDIR\resources\icon.ico"
  WriteRegStr HKCR "BusinessPlanFile\shell\open\command" "" '"$INSTDIR\${APP_EXECUTABLE_FILENAME}" "%1"'
!macroend

; إضافة معلومات إضافية لإلغاء التثبيت
!macro customUnInstall
  ; حذف الاختصارات
  Delete "$DESKTOP\خطة مشروعي.lnk"
  RMDir /r "$SMPROGRAMS\خطة مشروعي"
  
  ; حذف تسجيل نوع الملف
  DeleteRegKey HKCR ".businessplan"
  DeleteRegKey HKCR "BusinessPlanFile"
!macroend

; رسائل مخصصة للتثبيت
!macro customHeader
  !system "echo 'Building installer for Business Plan Builder by saif aldulaimi'"
!macroend

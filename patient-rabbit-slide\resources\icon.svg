<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- خلفية دائرية متدرجة -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- دائرة داخلية للعمق -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  
  <!-- أيقونة المصباح الكهربائي -->
  <g transform="translate(256,256)">
    <!-- جسم المصباح -->
    <path d="M-40,-80 C-40,-110 -20,-130 0,-130 C20,-130 40,-110 40,-80 C40,-60 35,-45 30,-30 L-30,-30 C-35,-45 -40,-60 -40,-80 Z" 
          fill="url(#iconGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    
    <!-- خطوط الإضاءة -->
    <g stroke="#fbbf24" stroke-width="3" stroke-linecap="round" opacity="0.8">
      <line x1="-70" y1="-80" x2="-55" y2="-80"/>
      <line x1="55" y1="-80" x2="70" y2="-80"/>
      <line x1="-50" y1="-110" x2="-40" y2="-100"/>
      <line x1="40" y1="-100" x2="50" y2="-110"/>
      <line x1="-50" y1="-50" x2="-40" y2="-60"/>
      <line x1="40" y1="-60" x2="50" y2="-50"/>
      <line x1="0" y1="-150" x2="0" y2="-135"/>
    </g>
    
    <!-- قاعدة المصباح -->
    <rect x="-25" y="-30" width="50" height="15" fill="#64748b" rx="3"/>
    <rect x="-20" y="-15" width="40" height="8" fill="#475569" rx="2"/>
    <rect x="-15" y="-7" width="30" height="6" fill="#334155" rx="1"/>
    
    <!-- نقاط مضيئة للتأثير -->
    <circle cx="-15" cy="-70" r="3" fill="#fef3c7" opacity="0.8"/>
    <circle cx="10" cy="-90" r="2" fill="#fef3c7" opacity="0.6"/>
    <circle cx="20" cy="-60" r="2.5" fill="#fef3c7" opacity="0.7"/>
  </g>
  
  <!-- نص عربي أنيق -->
  <text x="256" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="white" opacity="0.9">
    خطة مشروعي
  </text>
  
  <!-- نص إنجليزي -->
  <text x="256" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="rgba(255,255,255,0.7)">
    Business Plan Builder
  </text>
  
  <!-- تأثير لمعان -->
  <ellipse cx="200" cy="180" rx="60" ry="30" fill="rgba(255,255,255,0.2)" transform="rotate(-30 200 180)"/>
</svg>

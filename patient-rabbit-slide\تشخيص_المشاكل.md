# تشخيص مشاكل التطبيق 🔧

## مشكلة 404 - الصفحة غير موجودة

### الأسباب المحتملة:
1. **مشكلة في التوجيه (Routing)**: تم حلها بتغيير `BrowserRouter` إلى `HashRouter`
2. **ملفات غير مبنية**: تأكد من بناء التطبيق قبل التشغيل
3. **مسارات خاطئة**: تحقق من مسارات الملفات في Electron

### الحلول:

#### 1. إعادة بناء التطبيق
```bash
# احذف الملفات القديمة
rmdir /s /q dist
rmdir /s /q dist-electron

# أعد بناء التطبيق
npm run build
npx tsc -p electron/tsconfig.json

# شغل التطبيق
npx electron .
```

#### 2. تشغيل في وضع التطوير
```bash
# إذا كان التطبيق المبني لا يعمل، جرب وضع التطوير
.\dev-app.bat
```

#### 3. فحص الملفات
تأكد من وجود هذه الملفات:
- `dist/index.html`
- `dist/assets/index-*.js`
- `dist/assets/index-*.css`
- `dist-electron/main.js`
- `dist-electron/preload.js`

### تشخيص متقدم:

#### فتح أدوات المطور
1. شغل التطبيق
2. اضغط `F12` لفتح أدوات المطور
3. تحقق من:
   - رسائل الخطأ في Console
   - طلبات الشبكة في Network
   - حالة التطبيق في Application

#### فحص المسارات
```javascript
// في Console، اكتب:
console.log("Current URL:", window.location.href);
console.log("Current hash:", window.location.hash);
console.log("Base URL:", document.baseURI);
```

## مشاكل أخرى شائعة

### التطبيق لا يفتح
1. تأكد من تثبيت Node.js
2. شغل `npm install`
3. تحقق من أن المنفذ 5173 غير مستخدم (في وضع التطوير)

### أخطاء في البناء
1. احذف `node_modules` و `package-lock.json`
2. شغل `npm install`
3. شغل `npm run build`

### مشاكل في الأداء
1. أغلق أدوات المطور إذا لم تكن تحتاجها
2. تأكد من أن الجهاز يحتوي على ذاكرة كافية
3. أغلق التطبيقات الأخرى غير الضرورية

## معلومات تقنية

### هيكل التوجيه الحالي:
- `/` → الصفحة الرئيسية (Index.tsx)
- `/*` → صفحة 404 (NotFound.tsx)

### التقنيات المستخدمة:
- React Router مع HashRouter
- Electron للتطبيق
- Vite للبناء
- TypeScript للكتابة الآمنة

---
**إذا استمرت المشاكل، تواصل مع الدعم التقني** 📞

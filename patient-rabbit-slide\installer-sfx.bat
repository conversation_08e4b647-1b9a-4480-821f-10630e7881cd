@echo off
echo 🎯 إنشاء مثبت EXE ذاتي الاستخراج...
echo.

REM إنشاء مجلد مؤقت للمثبت
if exist "temp-installer" rmdir /s /q "temp-installer"
mkdir "temp-installer"

echo 📦 تحضير ملفات المثبت...

REM نسخ النسخة النظيفة
if exist "business-plan-clean" (
    xcopy "business-plan-clean" "temp-installer\app\" /E /I /Q
) else (
    echo ❌ النسخة النظيفة غير موجودة
    echo 🔧 إنشاء النسخة النظيفة...
    call create-clean-version.bat
    xcopy "business-plan-clean" "temp-installer\app\" /E /I /Q
)

REM نسخ سكريبت التثبيت
copy "مثبت_خطة_مشروعي.bat" "temp-installer\install.bat"

REM إنشاء سكريبت التشغيل التلقائي
echo 📝 إنشاء سكريبت التشغيل التلقائي...
echo @echo off > "temp-installer\autorun.bat"
echo title مثبت خطة مشروعي >> "temp-installer\autorun.bat"
echo cd /d "%%~dp0" >> "temp-installer\autorun.bat"
echo call install.bat >> "temp-installer\autorun.bat"

REM إنشاء ملف معلومات
echo 📋 إنشاء ملف المعلومات...
echo مثبت خطة مشروعي v1.0.0 > "temp-installer\info.txt"
echo تطبيق إنشاء خطط الأعمال باللغة العربية >> "temp-installer\info.txt"
echo المطور: saif aldulaimi >> "temp-installer\info.txt"
echo. >> "temp-installer\info.txt"
echo للتثبيت: شغل autorun.bat >> "temp-installer\info.txt"

REM إنشاء مثبت ذاتي الاستخراج باستخدام PowerShell
echo 🔄 إنشاء ملف EXE ذاتي الاستخراج...

REM إنشاء سكريبت PowerShell لإنشاء SFX
echo # مثبت ذاتي الاستخراج > create-sfx.ps1
echo Add-Type -AssemblyName System.IO.Compression.FileSystem >> create-sfx.ps1
echo Add-Type -AssemblyName System.Windows.Forms >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # ضغط الملفات >> create-sfx.ps1
echo $source = "temp-installer" >> create-sfx.ps1
echo $destination = "installer-archive.zip" >> create-sfx.ps1
echo if(Test-Path $destination) { Remove-Item $destination } >> create-sfx.ps1
echo [System.IO.Compression.ZipFile]::CreateFromDirectory($source, $destination) >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # قراءة محتوى ZIP كـ bytes >> create-sfx.ps1
echo $zipBytes = [System.IO.File]::ReadAllBytes($destination) >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # إنشاء سكريبت PowerShell للاستخراج والتشغيل >> create-sfx.ps1
echo $extractorScript = @' >> create-sfx.ps1
echo Add-Type -AssemblyName System.IO.Compression.FileSystem >> create-sfx.ps1
echo Add-Type -AssemblyName System.Windows.Forms >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # البيانات المضغوطة (سيتم استبدالها) >> create-sfx.ps1
echo $zipData = "DATA_PLACEHOLDER" >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # تحويل من Base64 >> create-sfx.ps1
echo $zipBytes = [System.Convert]::FromBase64String($zipData) >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # إنشاء مجلد مؤقت >> create-sfx.ps1
echo $tempDir = [System.IO.Path]::GetTempPath() + "BusinessPlanInstaller_" + [System.Guid]::NewGuid().ToString() >> create-sfx.ps1
echo New-Item -ItemType Directory -Path $tempDir -Force ^| Out-Null >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # كتابة وفك ضغط الملفات >> create-sfx.ps1
echo $zipPath = Join-Path $tempDir "installer.zip" >> create-sfx.ps1
echo [System.IO.File]::WriteAllBytes($zipPath, $zipBytes) >> create-sfx.ps1
echo [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $tempDir) >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # تشغيل المثبت >> create-sfx.ps1
echo $installerPath = Join-Path $tempDir "autorun.bat" >> create-sfx.ps1
echo if(Test-Path $installerPath) { >> create-sfx.ps1
echo     Start-Process -FilePath $installerPath -Wait >> create-sfx.ps1
echo } else { >> create-sfx.ps1
echo     [System.Windows.Forms.MessageBox]::Show("خطأ في استخراج ملفات المثبت", "خطأ", "OK", "Error") >> create-sfx.ps1
echo } >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # تنظيف الملفات المؤقتة >> create-sfx.ps1
echo Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue >> create-sfx.ps1
echo '@ >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # تحويل ZIP إلى Base64 >> create-sfx.ps1
echo $base64 = [System.Convert]::ToBase64String($zipBytes) >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # استبدال البيانات في السكريبت >> create-sfx.ps1
echo $finalScript = $extractorScript -replace "DATA_PLACEHOLDER", $base64 >> create-sfx.ps1
echo. >> create-sfx.ps1
echo # حفظ السكريبت النهائي >> create-sfx.ps1
echo $finalScript ^| Out-File -FilePath "installer-extractor.ps1" -Encoding UTF8 >> create-sfx.ps1
echo. >> create-sfx.ps1
echo Write-Host "تم إنشاء installer-extractor.ps1" >> create-sfx.ps1

echo 🔨 تشغيل سكريبت الإنشاء...
powershell -ExecutionPolicy Bypass -File create-sfx.ps1

if exist "installer-extractor.ps1" (
    echo ✅ تم إنشاء مثبت PowerShell
    
    REM محاولة تحويل PowerShell إلى EXE إذا كان ps2exe متوفر
    powershell -Command "if(Get-Module -ListAvailable -Name ps2exe){Import-Module ps2exe; Invoke-ps2exe installer-extractor.ps1 'خطة-مشروعي-مثبت.exe' -iconFile 'resources\icon.ico' -title 'مثبت خطة مشروعي' -noConsole} else {Write-Host 'ps2exe غير متوفر'}"
    
    if exist "خطة-مشروعي-مثبت.exe" (
        echo ✅ تم إنشاء مثبت EXE بنجاح! 🎉
        echo 📁 الملف: خطة-مشروعي-مثبت.exe
        explorer /select,"خطة-مشروعي-مثبت.exe"
    ) else (
        echo ⚠️ تم إنشاء مثبت PowerShell فقط
        echo 📁 الملف: installer-extractor.ps1
        echo 💡 لتشغيله: انقر بالزر الأيمن → Run with PowerShell
    )
) else (
    echo ❌ فشل في إنشاء المثبت
)

REM تنظيف الملفات المؤقتة
echo 🧹 تنظيف الملفات المؤقتة...
if exist "temp-installer" rmdir /s /q "temp-installer"
if exist "installer-archive.zip" del "installer-archive.zip"
if exist "create-sfx.ps1" del "create-sfx.ps1"

echo.
echo 🎯 النتيجة:
if exist "خطة-مشروعي-مثبت.exe" (
    echo ✅ مثبت EXE: خطة-مشروعي-مثبت.exe
) else if exist "installer-extractor.ps1" (
    echo ✅ مثبت PowerShell: installer-extractor.ps1
) else (
    echo ❌ لم يتم إنشاء مثبت
)

echo.
echo 💡 بدائل أخرى:
echo    1. استخدم مثبت_خطة_مشروعي.bat (يعمل ممتاز)
echo    2. حمل NSIS وشغل بناء_المثبت_النهائي.bat
echo    3. استخدم Bat to EXE Converter
echo.
pause

@echo off
echo 🚀 تشغيل تطبيق خطة مشروعي في وضع التطوير...
echo.

REM بناء ملفات Electron
echo ⚡ بناء ملفات Electron...
npx tsc -p electron/tsconfig.json
if errorlevel 1 (
    echo ❌ فشل في بناء ملفات Electron
    pause
    exit /b 1
)

echo ✅ تم بناء ملفات Electron بنجاح

REM تشغيل خادم التطوير وElectron
echo 🌐 تشغيل خادم التطوير...
echo 📝 ملاحظة: سيتم فتح التطبيق تلقائياً عند جاهزية الخادم
echo.

start /B npm run dev

REM انتظار قصير لبدء الخادم
timeout /t 5 /nobreak > nul

echo ⚡ تشغيل Electron...
set ELECTRON_RENDERER_URL=http://localhost:5173
npx electron .

echo.
echo 🔚 تم إغلاق التطبيق
pause

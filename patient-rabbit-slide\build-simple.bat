@echo off
echo 📦 بناء مثبت مبسط لتطبيق خطة مشروعي...
echo.

REM استخدام الملفات المبنية الموجودة
if not exist dist\index.html (
    echo ❌ ملفات React غير موجودة
    echo 💡 يرجى بناء التطبيق أولاً باستخدام وضع التطوير
    echo    أو استخدام ملفات مبنية مسبقاً
    pause
    exit /b 1
)

echo ✅ ملفات React موجودة

REM بناء ملفات Electron
echo ⚡ بناء ملفات Electron...
npx tsc -p electron/tsconfig.json
if errorlevel 1 (
    echo ❌ فشل في بناء ملفات Electron
    pause
    exit /b 1
)

echo ✅ تم بناء ملفات Electron بنجاح

REM بناء المثبت فقط للمجلد (أسرع)
echo 📦 بناء مجلد التطبيق...
npx electron-builder --dir
if errorlevel 1 (
    echo ❌ فشل في بناء مجلد التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء مجلد التطبيق بنجاح! 🎉
echo 📁 يمكنك العثور على التطبيق في: dist-app\win-unpacked\
echo.

REM عرض الملفات المبنية
if exist dist-app\win-unpacked (
    echo 📋 محتويات مجلد التطبيق:
    dir dist-app\win-unpacked /b
    echo.
    echo 🚀 يمكنك تشغيل التطبيق من: dist-app\win-unpacked\خطة مشروعي.exe
    echo 📦 أو نسخ مجلد win-unpacked بالكامل لتوزيعه
)

echo.
echo 💡 لإنشاء مثبت .exe، استخدم:
echo    npx electron-builder --win --publish=never
pause

@echo off
echo 📦 تحضير الملفات لـ Advanced Installer...
echo.

REM إنشاء النسخة المحمولة أولاً
echo 🔧 إنشاء النسخة المحمولة...
if not exist "خطة-مشروعي-محمول" (
    call create-portable.bat
    if errorlevel 1 (
        echo ❌ فشل في إنشاء النسخة المحمولة
        pause
        exit /b 1
    )
)

echo ✅ النسخة المحمولة جاهزة

REM إنشاء مجلد خاص لـ Advanced Installer
echo 📁 إنشاء مجلد installer-files...
if exist "installer-files" rmdir /s /q "installer-files"
mkdir "installer-files"

REM نسخ النسخة المحمولة
echo 📋 نسخ ملفات التطبيق...
xcopy "خطة-مشروعي-محمول" "installer-files\app\" /E /I /Y /Q

REM نسخ الملفات التوثيقية
echo 📄 نسخ الملفات التوثيقية...
if exist "LICENSE.txt" copy "LICENSE.txt" "installer-files\"
if exist "README.md" copy "README.md" "installer-files\"
if exist "دليل_الاستخدام.md" copy "دليل_الاستخدام.md" "installer-files\"

REM إنشاء ملف معلومات للمثبت
echo 📝 إنشاء ملف معلومات المثبت...
echo # معلومات المثبت > "installer-files\installer-info.txt"
echo. >> "installer-files\installer-info.txt"
echo اسم التطبيق: خطة مشروعي >> "installer-files\installer-info.txt"
echo الإصدار: 1.0.0 >> "installer-files\installer-info.txt"
echo المطور: saif aldulaimi >> "installer-files\installer-info.txt"
echo التاريخ: %date% >> "installer-files\installer-info.txt"
echo. >> "installer-files\installer-info.txt"
echo الملفات المطلوبة: >> "installer-files\installer-info.txt"
echo - app\ : ملفات التطبيق الرئيسية >> "installer-files\installer-info.txt"
echo - app\تشغيل التطبيق.bat : ملف التشغيل >> "installer-files\installer-info.txt"
echo - app\resources\icon.ico : أيقونة التطبيق >> "installer-files\installer-info.txt"

REM إنشاء ملف تعليمات Advanced Installer
echo 📋 إنشاء تعليمات Advanced Installer...
echo # تعليمات Advanced Installer > "installer-files\advanced-installer-steps.txt"
echo. >> "installer-files\advanced-installer-steps.txt"
echo 1. افتح Advanced Installer 22.8 >> "installer-files\advanced-installer-steps.txt"
echo 2. اختر "New Project" ^> "Generic Windows Installer" >> "installer-files\advanced-installer-steps.txt"
echo 3. في Product Details: >> "installer-files\advanced-installer-steps.txt"
echo    - Product Name: خطة مشروعي >> "installer-files\advanced-installer-steps.txt"
echo    - Version: 1.0.0 >> "installer-files\advanced-installer-steps.txt"
echo    - Publisher: saif aldulaimi >> "installer-files\advanced-installer-steps.txt"
echo 4. في Files and Folders: >> "installer-files\advanced-installer-steps.txt"
echo    - أضف مجلد app\ كاملاً >> "installer-files\advanced-installer-steps.txt"
echo 5. في Shortcuts: >> "installer-files\advanced-installer-steps.txt"
echo    - Desktop: خطة مشروعي ^> app\تشغيل التطبيق.bat >> "installer-files\advanced-installer-steps.txt"
echo    - Start Menu: خطة مشروعي ^> app\تشغيل التطبيق.bat >> "installer-files\advanced-installer-steps.txt"
echo    - Icon: app\resources\icon.ico >> "installer-files\advanced-installer-steps.txt"
echo 6. في Translations: أضف Arabic >> "installer-files\advanced-installer-steps.txt"
echo 7. Build ^> Build Solution >> "installer-files\advanced-installer-steps.txt"

REM التحقق من الملفات المطلوبة
echo 🔍 التحقق من الملفات المطلوبة...
set MISSING_FILES=0

if not exist "installer-files\app\تشغيل التطبيق.bat" (
    echo ❌ ملف التشغيل مفقود
    set MISSING_FILES=1
)

if not exist "installer-files\app\resources\icon.ico" (
    echo ❌ أيقونة ICO مفقودة
    set MISSING_FILES=1
)

if not exist "installer-files\app\dist-electron\main.js" (
    echo ❌ ملفات Electron مفقودة
    set MISSING_FILES=1
)

if %MISSING_FILES%==1 (
    echo.
    echo ❌ بعض الملفات المطلوبة مفقودة!
    echo 💡 تأكد من تشغيل create-portable.bat أولاً
    pause
    exit /b 1
)

echo.
echo ✅ تم تحضير جميع الملفات بنجاح! 🎉
echo.
echo 📁 مجلد الملفات: installer-files\
echo 📋 محتويات المجلد:
dir "installer-files" /b
echo.
echo 📋 الخطوات التالية:
echo    1. افتح Advanced Installer 22.8
echo    2. أنشئ مشروع جديد (Generic Windows Installer)
echo    3. اتبع التعليمات في ملف: installer-files\advanced-installer-steps.txt
echo    4. أضف مجلد installer-files\app\ كاملاً
echo    5. اضبط الاختصارات والأيقونات
echo    6. ابني المثبت!
echo.
echo 🎯 نصائح:
echo    - استخدم app\تشغيل التطبيق.bat كملف التشغيل الرئيسي
echo    - استخدم app\resources\icon.ico للأيقونات
echo    - فعل اللغة العربية في Translations
echo.
pause

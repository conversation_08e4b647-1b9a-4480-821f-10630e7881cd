import { contextBridge, ipcRenderer } from 'electron';

// تعريف واجهة API الآمنة
export interface ElectronAPI {
  // وظائف حفظ وتحميل المشروع
  saveProjectFile: (data: any) => Promise<{ success: boolean; path?: string; error?: string }>;
  
  // مستمعي الأحداث
  onSaveProject: (callback: () => void) => void;
  onLoadProject: (callback: (data: any) => void) => void;
  onExportPDF: (callback: () => void) => void;
  onExportExcel: (callback: () => void) => void;
  
  // معلومات النظام
  platform: string;
  
  // وظائف النافذة
  minimize: () => void;
  maximize: () => void;
  close: () => void;
}

// تعريف API في السياق الآمن
const electronAPI: ElectronAPI = {
  // حفظ ملف المشروع
  saveProjectFile: (data: any) => ipcRenderer.invoke('save-project-file', data),
  
  // مستمعي الأحداث من القائمة
  onSaveProject: (callback: () => void) => {
    ipcRenderer.on('save-project', callback);
  },
  
  onLoadProject: (callback: (data: any) => void) => {
    ipcRenderer.on('load-project', (_, data) => callback(data));
  },
  
  onExportPDF: (callback: () => void) => {
    ipcRenderer.on('export-pdf', callback);
  },
  
  onExportExcel: (callback: () => void) => {
    ipcRenderer.on('export-excel', callback);
  },
  
  // معلومات النظام
  platform: process.platform,
  
  // وظائف النافذة
  minimize: () => ipcRenderer.send('window-minimize'),
  maximize: () => ipcRenderer.send('window-maximize'),
  close: () => ipcRenderer.send('window-close')
};

// تصدير API للتطبيق
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// تعريف النوع العام للنافذة
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// إضافة مستمع لأحداث DOM
window.addEventListener('DOMContentLoaded', () => {
  // إضافة معلومات النظام للعنصر الجذر
  const root = document.getElementById('root');
  if (root) {
    root.setAttribute('data-platform', process.platform);
  }
  
  // إضافة فئة CSS للنظام
  document.body.classList.add(`platform-${process.platform}`);
  
  // إعداد معالجات اختصارات لوحة المفاتيح
  document.addEventListener('keydown', (event) => {
    // Ctrl/Cmd + S للحفظ
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      ipcRenderer.send('save-project');
    }
    
    // Ctrl/Cmd + O لفتح ملف
    if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
      event.preventDefault();
      // سيتم التعامل معه في القائمة
    }
    
    // F11 للشاشة الكاملة
    if (event.key === 'F11') {
      event.preventDefault();
      ipcRenderer.send('toggle-fullscreen');
    }
  });
});

// معالجة أخطاء غير متوقعة
window.addEventListener('error', (event) => {
  console.error('خطأ في التطبيق:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('رفض غير معالج:', event.reason);
});

// تصدير النوع للاستخدام في TypeScript
export type { ElectronAPI };

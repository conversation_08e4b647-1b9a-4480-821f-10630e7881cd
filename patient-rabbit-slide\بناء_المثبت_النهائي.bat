@echo off
echo 🚀 بناء مثبت خطة مشروعي النهائي...
echo.

REM التحقق من وجود NSIS
where makensis >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ NSIS غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت NSIS من:
    echo https://nsis.sourceforge.io/Download
    echo.
    echo أو استخدم Chocolatey:
    echo choco install nsis
    echo.
    pause
    exit /b 1
)

echo ✅ NSIS متوفر

REM التحقق من وجود النسخة النظيفة
if not exist "business-plan-clean" (
    echo ⚠️ النسخة النظيفة غير موجودة
    echo 🔧 إنشاء النسخة النظيفة...
    call create-clean-version.bat
    if errorlevel 1 (
        echo ❌ فشل في إنشاء النسخة النظيفة
        pause
        exit /b 1
    )
)

echo ✅ النسخة النظيفة موجودة

REM التحقق من الملفات المطلوبة
echo 🔍 التحقق من الملفات المطلوبة...

if not exist "business-plan-clean\run-app.bat" (
    echo ❌ ملف التشغيل مفقود
    echo 🔧 إنشاء ملف التشغيل...
    echo @echo off > "business-plan-clean\run-app.bat"
    echo echo 🚀 تشغيل خطة مشروعي... >> "business-plan-clean\run-app.bat"
    echo cd /d "%%~dp0" >> "business-plan-clean\run-app.bat"
    echo if not exist node_modules\electron ( >> "business-plan-clean\run-app.bat"
    echo     echo 📥 تثبيت Electron... >> "business-plan-clean\run-app.bat"
    echo     npm install electron >> "business-plan-clean\run-app.bat"
    echo ^) >> "business-plan-clean\run-app.bat"
    echo npx electron . >> "business-plan-clean\run-app.bat"
    echo pause >> "business-plan-clean\run-app.bat"
)

if not exist "business-plan-clean\icon.ico" (
    echo ⚠️ أيقونة ICO مفقودة
    if exist "resources\icon.ico" (
        echo 🔧 نسخ الأيقونة...
        copy "resources\icon.ico" "business-plan-clean\"
    ) else (
        echo ❌ لم يتم العثور على أيقونة
        echo 💡 سيتم استخدام أيقونة افتراضية
    )
)

if not exist "business-plan-clean\dist-electron" (
    echo ⚠️ ملفات Electron مفقودة
    if exist "dist-electron" (
        echo 🔧 نسخ ملفات Electron...
        xcopy "dist-electron" "business-plan-clean\dist-electron\" /E /I /Q
    ) else (
        echo 🔧 بناء ملفات Electron...
        npx tsc -p electron/tsconfig.json
        xcopy "dist-electron" "business-plan-clean\dist-electron\" /E /I /Q
    )
)

echo ✅ جميع الملفات جاهزة

REM بناء المثبت
echo.
echo 🔨 بناء المثبت باستخدام NSIS...
makensis installer-final.nsi

if %ERRORLEVEL% EQ 0 (
    echo.
    echo ✅ تم بناء المثبت بنجاح! 🎉
    echo.
    echo 📁 ملف المثبت: خطة-مشروعي-مثبت-1.0.0.exe
    echo 📊 حجم المثبت:
    for %%A in ("خطة-مشروعي-مثبت-1.0.0.exe") do echo    %%~zA bytes
    echo.
    echo 🎯 المثبت جاهز للتوزيع!
    echo.
    echo 💡 لاختبار المثبت:
    echo    1. انقر مرتين على الملف
    echo    2. اتبع خطوات التثبيت
    echo    3. اختبر التطبيق
    echo    4. اختبر إلغاء التثبيت
    echo.
    
    REM فتح مجلد المثبت
    if exist "خطة-مشروعي-مثبت-1.0.0.exe" (
        echo 📂 فتح مجلد المثبت...
        explorer /select,"خطة-مشروعي-مثبت-1.0.0.exe"
    )
) else (
    echo.
    echo ❌ فشل في بناء المثبت
    echo 💡 تحقق من:
    echo    - وجود جميع الملفات المطلوبة
    echo    - صحة ملف installer-final.nsi
    echo    - عدم وجود أخطاء في NSIS
)

echo.
pause

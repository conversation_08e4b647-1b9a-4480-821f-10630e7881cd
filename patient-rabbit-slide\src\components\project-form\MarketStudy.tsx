import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { BarChart3 } from "lucide-react";

const RadioField = ({ label, id, value }: { label: string; id: string; value: string; }) => (
    <Label htmlFor={id} className="font-normal flex items-center justify-start gap-2 cursor-pointer">
        <RadioGroupItem value={value} id={id} />
        <span>{label}</span>
    </Label>
);

const CheckboxField = ({ label, id }: { label: string; id: string; }) => (
    <Label htmlFor={id} className="font-normal flex items-center justify-start gap-2 cursor-pointer">
        <Checkbox id={id} />
        <span>{label}</span>
    </Label>
);

export const MarketStudy = () => {
  return (
    <Card className="w-full bg-teal-50 dark:bg-teal-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
          <BarChart3 className="h-6 w-6 text-primary" />
          دراسة السوق والمنافسين
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-8">
        <div className="space-y-3">
            <Label className="text-base font-semibold">ماذا ستقدم في مشروعك؟</Label>
            <div className="pr-4 space-y-4">
                <div>
                    <Label htmlFor="products" className="font-normal text-muted-foreground">◼️ منتجات (اذكرها):</Label>
                    <Textarea id="products" className="mt-1" />
                </div>
                <div>
                    <Label htmlFor="services" className="font-normal text-muted-foreground">◼️ خدمات (اذكرها):</Label>
                    <Textarea id="services" className="mt-1" />
                </div>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">هل يوجد منافسين يبيعون نفس المنتج أو منتج شبيه في منطقة مشروعك؟</Label>
            <div className="pr-4">
                <RadioGroup defaultValue="no" className="flex items-center flex-wrap gap-x-6 gap-y-2">
                    <div className="flex items-center gap-2">
                        <RadioField label="نعم – كم عددهم؟" id="competitorsYes" value="yes" />
                        <Input id="competitorsCount" type="number" className="w-24 h-8" />
                    </div>
                    <RadioField label="لا" id="competitorsNo" value="no" />
                </RadioGroup>
            </div>
        </div>

        <div className="space-y-3">
            <Label htmlFor="competitorProducts" className="text-base font-semibold">ما هي المنتجات المنافسة أو الشبيهة التي يبيعها المنافسون؟</Label>
            <div className="pr-4">
                <Textarea id="competitorProducts" />
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">كيف يحاول المنافسون أن يحققوا الربح؟</Label>
            <div className="pr-4 space-y-2">
                <CheckboxField label="من خلال السعر المنخفض" id="profitPrice" />
                <CheckboxField label="من خلال جودة المنتج" id="profitQuality" />
                <CheckboxField label="من خلال الخدمة المميزة" id="profitService" />
                <CheckboxField label="من خلال التكلفة المنخفضة" id="profitCost" />
                <div className="flex items-center gap-2 w-full max-w-sm">
                    <CheckboxField label="أخرى (اذكرها):" id="profitOther" />
                    <Input id="profitOtherText" className="flex-1 h-8" />
                </div>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">ما هي أسعار المنافسين مقارنة بالسعر الذي تنوي البيع به؟</Label>
            <div className="pr-4">
                <RadioGroup className="space-y-2">
                    <RadioField label="نفس سعر البيع الذي سأبيع به تقريبًا" id="priceSame" value="same" />
                    <RadioField label="أسعار المنافسين أعلى من سعري" id="priceHigher" value="higher" />
                    <RadioField label="أسعار المنافسين أقل من سعري" id="priceLower" value="lower" />
                </RadioGroup>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">كيف يبيع ويُروّج المنافسون منتجاتهم؟</Label>
            <div className="pr-4 space-y-2">
                <CheckboxField label="من خلال الإنترنت ووسائل التواصل الاجتماعي" id="promoSocial" />
                <CheckboxField label="من خلال البيع المباشر للناس" id="promoDirect" />
                <CheckboxField label="من خلال زيارة الزبائن والترويج للمنتج" id="promoVisits" />
                <div className="flex items-center gap-2 w-full max-w-sm">
                    <CheckboxField label="الإعلان – وسيلة الإعلان:" id="promoAd" />
                    <Input id="promoAdText" className="flex-1 h-8" />
                </div>
                <div className="flex items-center gap-2 w-full max-w-sm">
                    <CheckboxField label="أخرى (اذكرها):" id="promoOther" />
                    <Input id="promoOtherText" className="flex-1 h-8" />
                </div>
            </div>
        </div>

        <div className="space-y-3">
            <Label className="text-base font-semibold">قم بمراقبة اثنين من المنافسين وقدر عدد الزبائن الذين يترددون عليهم في اليوم الواحد:</Label>
            <div className="pr-4 space-y-3">
                <div className="flex items-center gap-2">
                    <Label className="font-normal w-48">◼️ عدد الزبائن عند المنافس 1:</Label>
                    <Input type="number" className="w-24 h-8" />
                </div>
                <div className="flex items-center gap-2">
                    <Label className="font-normal w-48">◼️ عدد الزبائن عند المنافس 2:</Label>
                    <Input type="number" className="w-24 h-8" />
                </div>
            </div>
        </div>
      </CardContent>
    </Card>
  );
};
var vp=n=>{throw TypeError(n)};var Fu=(n,r,s)=>r.has(n)||vp("Cannot "+s);var B=(n,r,s)=>(Fu(n,r,"read from private field"),s?s.call(n):r.get(n)),_e=(n,r,s)=>r.has(n)?vp("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(n):r.set(n,s),we=(n,r,s,l)=>(Fu(n,r,"write to private field"),l?l.call(n,s):r.set(n,s),s),pt=(n,r,s)=>(Fu(n,r,"access private method"),s);var wl=(n,r,s,l)=>({set _(c){we(n,r,c,s)},get _(){return B(n,r,l)}});function Uv(n,r){for(var s=0;s<r.length;s++){const l=r[s];if(typeof l!="string"&&!Array.isArray(l)){for(const c in l)if(c!=="default"&&!(c in n)){const f=Object.getOwnPropertyDescriptor(l,c);f&&Object.defineProperty(n,c,f.get?f:{enumerable:!0,get:()=>l[c]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))l(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const p of f.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&l(p)}).observe(document,{childList:!0,subtree:!0});function s(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function l(c){if(c.ep)return;c.ep=!0;const f=s(c);fetch(c.href,f)}})();function Nh(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Lu={exports:{}},Vs={},zu={exports:{}},Ne={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp;function $v(){if(yp)return Ne;yp=1;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.provider"),p=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),b=Symbol.iterator;function S(R){return R===null||typeof R!="object"?null:(R=b&&R[b]||R["@@iterator"],typeof R=="function"?R:null)}var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,k={};function j(R,L,ee){this.props=R,this.context=L,this.refs=k,this.updater=ee||y}j.prototype.isReactComponent={},j.prototype.setState=function(R,L){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,L,"setState")},j.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function _(){}_.prototype=j.prototype;function P(R,L,ee){this.props=R,this.context=L,this.refs=k,this.updater=ee||y}var A=P.prototype=new _;A.constructor=P,E(A,j.prototype),A.isPureReactComponent=!0;var z=Array.isArray,O=Object.prototype.hasOwnProperty,F={current:null},$={key:!0,ref:!0,__self:!0,__source:!0};function G(R,L,ee){var re,he={},be=null,ce=null;if(L!=null)for(re in L.ref!==void 0&&(ce=L.ref),L.key!==void 0&&(be=""+L.key),L)O.call(L,re)&&!$.hasOwnProperty(re)&&(he[re]=L[re]);var Ce=arguments.length-2;if(Ce===1)he.children=ee;else if(1<Ce){for(var Pe=Array(Ce),Ge=0;Ge<Ce;Ge++)Pe[Ge]=arguments[Ge+2];he.children=Pe}if(R&&R.defaultProps)for(re in Ce=R.defaultProps,Ce)he[re]===void 0&&(he[re]=Ce[re]);return{$$typeof:n,type:R,key:be,ref:ce,props:he,_owner:F.current}}function Z(R,L){return{$$typeof:n,type:R.type,key:L,ref:R.ref,props:R.props,_owner:R._owner}}function J(R){return typeof R=="object"&&R!==null&&R.$$typeof===n}function pe(R){var L={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(ee){return L[ee]})}var te=/\/+/g;function ge(R,L){return typeof R=="object"&&R!==null&&R.key!=null?pe(""+R.key):L.toString(36)}function X(R,L,ee,re,he){var be=typeof R;(be==="undefined"||be==="boolean")&&(R=null);var ce=!1;if(R===null)ce=!0;else switch(be){case"string":case"number":ce=!0;break;case"object":switch(R.$$typeof){case n:case r:ce=!0}}if(ce)return ce=R,he=he(ce),R=re===""?"."+ge(ce,0):re,z(he)?(ee="",R!=null&&(ee=R.replace(te,"$&/")+"/"),X(he,L,ee,"",function(Ge){return Ge})):he!=null&&(J(he)&&(he=Z(he,ee+(!he.key||ce&&ce.key===he.key?"":(""+he.key).replace(te,"$&/")+"/")+R)),L.push(he)),1;if(ce=0,re=re===""?".":re+":",z(R))for(var Ce=0;Ce<R.length;Ce++){be=R[Ce];var Pe=re+ge(be,Ce);ce+=X(be,L,ee,Pe,he)}else if(Pe=S(R),typeof Pe=="function")for(R=Pe.call(R),Ce=0;!(be=R.next()).done;)be=be.value,Pe=re+ge(be,Ce++),ce+=X(be,L,ee,Pe,he);else if(be==="object")throw L=String(R),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.");return ce}function ve(R,L,ee){if(R==null)return R;var re=[],he=0;return X(R,re,"","",function(be){return L.call(ee,be,he++)}),re}function se(R){if(R._status===-1){var L=R._result;L=L(),L.then(function(ee){(R._status===0||R._status===-1)&&(R._status=1,R._result=ee)},function(ee){(R._status===0||R._status===-1)&&(R._status=2,R._result=ee)}),R._status===-1&&(R._status=0,R._result=L)}if(R._status===1)return R._result.default;throw R._result}var ae={current:null},U={transition:null},V={ReactCurrentDispatcher:ae,ReactCurrentBatchConfig:U,ReactCurrentOwner:F};function K(){throw Error("act(...) is not supported in production builds of React.")}return Ne.Children={map:ve,forEach:function(R,L,ee){ve(R,function(){L.apply(this,arguments)},ee)},count:function(R){var L=0;return ve(R,function(){L++}),L},toArray:function(R){return ve(R,function(L){return L})||[]},only:function(R){if(!J(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},Ne.Component=j,Ne.Fragment=s,Ne.Profiler=c,Ne.PureComponent=P,Ne.StrictMode=l,Ne.Suspense=g,Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,Ne.act=K,Ne.cloneElement=function(R,L,ee){if(R==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+R+".");var re=E({},R.props),he=R.key,be=R.ref,ce=R._owner;if(L!=null){if(L.ref!==void 0&&(be=L.ref,ce=F.current),L.key!==void 0&&(he=""+L.key),R.type&&R.type.defaultProps)var Ce=R.type.defaultProps;for(Pe in L)O.call(L,Pe)&&!$.hasOwnProperty(Pe)&&(re[Pe]=L[Pe]===void 0&&Ce!==void 0?Ce[Pe]:L[Pe])}var Pe=arguments.length-2;if(Pe===1)re.children=ee;else if(1<Pe){Ce=Array(Pe);for(var Ge=0;Ge<Pe;Ge++)Ce[Ge]=arguments[Ge+2];re.children=Ce}return{$$typeof:n,type:R.type,key:he,ref:be,props:re,_owner:ce}},Ne.createContext=function(R){return R={$$typeof:p,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},R.Provider={$$typeof:f,_context:R},R.Consumer=R},Ne.createElement=G,Ne.createFactory=function(R){var L=G.bind(null,R);return L.type=R,L},Ne.createRef=function(){return{current:null}},Ne.forwardRef=function(R){return{$$typeof:h,render:R}},Ne.isValidElement=J,Ne.lazy=function(R){return{$$typeof:x,_payload:{_status:-1,_result:R},_init:se}},Ne.memo=function(R,L){return{$$typeof:v,type:R,compare:L===void 0?null:L}},Ne.startTransition=function(R){var L=U.transition;U.transition={};try{R()}finally{U.transition=L}},Ne.unstable_act=K,Ne.useCallback=function(R,L){return ae.current.useCallback(R,L)},Ne.useContext=function(R){return ae.current.useContext(R)},Ne.useDebugValue=function(){},Ne.useDeferredValue=function(R){return ae.current.useDeferredValue(R)},Ne.useEffect=function(R,L){return ae.current.useEffect(R,L)},Ne.useId=function(){return ae.current.useId()},Ne.useImperativeHandle=function(R,L,ee){return ae.current.useImperativeHandle(R,L,ee)},Ne.useInsertionEffect=function(R,L){return ae.current.useInsertionEffect(R,L)},Ne.useLayoutEffect=function(R,L){return ae.current.useLayoutEffect(R,L)},Ne.useMemo=function(R,L){return ae.current.useMemo(R,L)},Ne.useReducer=function(R,L,ee){return ae.current.useReducer(R,L,ee)},Ne.useRef=function(R){return ae.current.useRef(R)},Ne.useState=function(R){return ae.current.useState(R)},Ne.useSyncExternalStore=function(R,L,ee){return ae.current.useSyncExternalStore(R,L,ee)},Ne.useTransition=function(){return ae.current.useTransition()},Ne.version="18.3.1",Ne}var xp;function hc(){return xp||(xp=1,zu.exports=$v()),zu.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wp;function Bv(){if(wp)return Vs;wp=1;var n=hc(),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,c=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(h,g,v){var x,b={},S=null,y=null;v!==void 0&&(S=""+v),g.key!==void 0&&(S=""+g.key),g.ref!==void 0&&(y=g.ref);for(x in g)l.call(g,x)&&!f.hasOwnProperty(x)&&(b[x]=g[x]);if(h&&h.defaultProps)for(x in g=h.defaultProps,g)b[x]===void 0&&(b[x]=g[x]);return{$$typeof:r,type:h,key:S,ref:y,props:b,_owner:c.current}}return Vs.Fragment=s,Vs.jsx=p,Vs.jsxs=p,Vs}var bp;function Vv(){return bp||(bp=1,Lu.exports=Bv()),Lu.exports}var a=Vv(),bl={},Uu={exports:{}},Pt={},$u={exports:{}},Bu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jp;function Hv(){return jp||(jp=1,function(n){function r(U,V){var K=U.length;U.push(V);e:for(;0<K;){var R=K-1>>>1,L=U[R];if(0<c(L,V))U[R]=V,U[K]=L,K=R;else break e}}function s(U){return U.length===0?null:U[0]}function l(U){if(U.length===0)return null;var V=U[0],K=U.pop();if(K!==V){U[0]=K;e:for(var R=0,L=U.length,ee=L>>>1;R<ee;){var re=2*(R+1)-1,he=U[re],be=re+1,ce=U[be];if(0>c(he,K))be<L&&0>c(ce,he)?(U[R]=ce,U[be]=K,R=be):(U[R]=he,U[re]=K,R=re);else if(be<L&&0>c(ce,K))U[R]=ce,U[be]=K,R=be;else break e}}return V}function c(U,V){var K=U.sortIndex-V.sortIndex;return K!==0?K:U.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var p=Date,h=p.now();n.unstable_now=function(){return p.now()-h}}var g=[],v=[],x=1,b=null,S=3,y=!1,E=!1,k=!1,j=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,P=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function A(U){for(var V=s(v);V!==null;){if(V.callback===null)l(v);else if(V.startTime<=U)l(v),V.sortIndex=V.expirationTime,r(g,V);else break;V=s(v)}}function z(U){if(k=!1,A(U),!E)if(s(g)!==null)E=!0,se(O);else{var V=s(v);V!==null&&ae(z,V.startTime-U)}}function O(U,V){E=!1,k&&(k=!1,_(G),G=-1),y=!0;var K=S;try{for(A(V),b=s(g);b!==null&&(!(b.expirationTime>V)||U&&!pe());){var R=b.callback;if(typeof R=="function"){b.callback=null,S=b.priorityLevel;var L=R(b.expirationTime<=V);V=n.unstable_now(),typeof L=="function"?b.callback=L:b===s(g)&&l(g),A(V)}else l(g);b=s(g)}if(b!==null)var ee=!0;else{var re=s(v);re!==null&&ae(z,re.startTime-V),ee=!1}return ee}finally{b=null,S=K,y=!1}}var F=!1,$=null,G=-1,Z=5,J=-1;function pe(){return!(n.unstable_now()-J<Z)}function te(){if($!==null){var U=n.unstable_now();J=U;var V=!0;try{V=$(!0,U)}finally{V?ge():(F=!1,$=null)}}else F=!1}var ge;if(typeof P=="function")ge=function(){P(te)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,ve=X.port2;X.port1.onmessage=te,ge=function(){ve.postMessage(null)}}else ge=function(){j(te,0)};function se(U){$=U,F||(F=!0,ge())}function ae(U,V){G=j(function(){U(n.unstable_now())},V)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(U){U.callback=null},n.unstable_continueExecution=function(){E||y||(E=!0,se(O))},n.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<U?Math.floor(1e3/U):5},n.unstable_getCurrentPriorityLevel=function(){return S},n.unstable_getFirstCallbackNode=function(){return s(g)},n.unstable_next=function(U){switch(S){case 1:case 2:case 3:var V=3;break;default:V=S}var K=S;S=V;try{return U()}finally{S=K}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(U,V){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var K=S;S=U;try{return V()}finally{S=K}},n.unstable_scheduleCallback=function(U,V,K){var R=n.unstable_now();switch(typeof K=="object"&&K!==null?(K=K.delay,K=typeof K=="number"&&0<K?R+K:R):K=R,U){case 1:var L=-1;break;case 2:L=250;break;case 5:L=**********;break;case 4:L=1e4;break;default:L=5e3}return L=K+L,U={id:x++,callback:V,priorityLevel:U,startTime:K,expirationTime:L,sortIndex:-1},K>R?(U.sortIndex=K,r(v,U),s(g)===null&&U===s(v)&&(k?(_(G),G=-1):k=!0,ae(z,K-R))):(U.sortIndex=L,r(g,U),E||y||(E=!0,se(O))),U},n.unstable_shouldYield=pe,n.unstable_wrapCallback=function(U){var V=S;return function(){var K=S;S=V;try{return U.apply(this,arguments)}finally{S=K}}}}(Bu)),Bu}var kp;function Wv(){return kp||(kp=1,$u.exports=Hv()),$u.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Np;function Qv(){if(Np)return Pt;Np=1;var n=hc(),r=Wv();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,o=1;o<arguments.length;o++)t+="&args[]="+encodeURIComponent(arguments[o]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,c={};function f(e,t){p(e,t),p(e+"Capture",t)}function p(e,t){for(c[e]=t,e=0;e<t.length;e++)l.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),g=Object.prototype.hasOwnProperty,v=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,x={},b={};function S(e){return g.call(b,e)?!0:g.call(x,e)?!1:v.test(e)?b[e]=!0:(x[e]=!0,!1)}function y(e,t,o,i){if(o!==null&&o.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return i?!1:o!==null?!o.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function E(e,t,o,i){if(t===null||typeof t>"u"||y(e,t,o,i))return!0;if(i)return!1;if(o!==null)switch(o.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function k(e,t,o,i,u,d,m){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=i,this.attributeNamespace=u,this.mustUseProperty=o,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=m}var j={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){j[e]=new k(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];j[t]=new k(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){j[e]=new k(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){j[e]=new k(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){j[e]=new k(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){j[e]=new k(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){j[e]=new k(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){j[e]=new k(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){j[e]=new k(e,5,!1,e.toLowerCase(),null,!1,!1)});var _=/[\-:]([a-z])/g;function P(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_,P);j[t]=new k(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_,P);j[t]=new k(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_,P);j[t]=new k(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){j[e]=new k(e,1,!1,e.toLowerCase(),null,!1,!1)}),j.xlinkHref=new k("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){j[e]=new k(e,1,!1,e.toLowerCase(),null,!0,!0)});function A(e,t,o,i){var u=j.hasOwnProperty(t)?j[t]:null;(u!==null?u.type!==0:i||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(E(t,o,u,i)&&(o=null),i||u===null?S(t)&&(o===null?e.removeAttribute(t):e.setAttribute(t,""+o)):u.mustUseProperty?e[u.propertyName]=o===null?u.type===3?!1:"":o:(t=u.attributeName,i=u.attributeNamespace,o===null?e.removeAttribute(t):(u=u.type,o=u===3||u===4&&o===!0?"":""+o,i?e.setAttributeNS(i,t,o):e.setAttribute(t,o))))}var z=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,O=Symbol.for("react.element"),F=Symbol.for("react.portal"),$=Symbol.for("react.fragment"),G=Symbol.for("react.strict_mode"),Z=Symbol.for("react.profiler"),J=Symbol.for("react.provider"),pe=Symbol.for("react.context"),te=Symbol.for("react.forward_ref"),ge=Symbol.for("react.suspense"),X=Symbol.for("react.suspense_list"),ve=Symbol.for("react.memo"),se=Symbol.for("react.lazy"),ae=Symbol.for("react.offscreen"),U=Symbol.iterator;function V(e){return e===null||typeof e!="object"?null:(e=U&&e[U]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,R;function L(e){if(R===void 0)try{throw Error()}catch(o){var t=o.stack.trim().match(/\n( *(at )?)/);R=t&&t[1]||""}return`
`+R+e}var ee=!1;function re(e,t){if(!e||ee)return"";ee=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(I){var i=I}Reflect.construct(e,[],t)}else{try{t.call()}catch(I){i=I}e.call(t.prototype)}else{try{throw Error()}catch(I){i=I}e()}}catch(I){if(I&&i&&typeof I.stack=="string"){for(var u=I.stack.split(`
`),d=i.stack.split(`
`),m=u.length-1,N=d.length-1;1<=m&&0<=N&&u[m]!==d[N];)N--;for(;1<=m&&0<=N;m--,N--)if(u[m]!==d[N]){if(m!==1||N!==1)do if(m--,N--,0>N||u[m]!==d[N]){var C=`
`+u[m].replace(" at new "," at ");return e.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",e.displayName)),C}while(1<=m&&0<=N);break}}}finally{ee=!1,Error.prepareStackTrace=o}return(e=e?e.displayName||e.name:"")?L(e):""}function he(e){switch(e.tag){case 5:return L(e.type);case 16:return L("Lazy");case 13:return L("Suspense");case 19:return L("SuspenseList");case 0:case 2:case 15:return e=re(e.type,!1),e;case 11:return e=re(e.type.render,!1),e;case 1:return e=re(e.type,!0),e;default:return""}}function be(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $:return"Fragment";case F:return"Portal";case Z:return"Profiler";case G:return"StrictMode";case ge:return"Suspense";case X:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case pe:return(e.displayName||"Context")+".Consumer";case J:return(e._context.displayName||"Context")+".Provider";case te:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ve:return t=e.displayName||null,t!==null?t:be(e.type)||"Memo";case se:t=e._payload,e=e._init;try{return be(e(t))}catch{}}return null}function ce(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return be(t);case 8:return t===G?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ce(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ge(e){var t=Pe(e)?"checked":"value",o=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof o<"u"&&typeof o.get=="function"&&typeof o.set=="function"){var u=o.get,d=o.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(m){i=""+m,d.call(this,m)}}),Object.defineProperty(e,t,{enumerable:o.enumerable}),{getValue:function(){return i},setValue:function(m){i=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _t(e){e._valueTracker||(e._valueTracker=Ge(e))}function Fn(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var o=t.getValue(),i="";return e&&(i=Pe(e)?e.checked?"true":"false":e.value),e=i,e!==o?(t.setValue(e),!0):!1}function At(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function nr(e,t){var o=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:o??e._wrapperState.initialChecked})}function fo(e,t){var o=t.defaultValue==null?"":t.defaultValue,i=t.checked!=null?t.checked:t.defaultChecked;o=Ce(t.value!=null?t.value:o),e._wrapperState={initialChecked:i,initialValue:o,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ur(e,t){t=t.checked,t!=null&&A(e,"checked",t,!1)}function $r(e,t){Ur(e,t);var o=Ce(t.value),i=t.type;if(o!=null)i==="number"?(o===0&&e.value===""||e.value!=o)&&(e.value=""+o):e.value!==""+o&&(e.value=""+o);else if(i==="submit"||i==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vn(e,t.type,o):t.hasOwnProperty("defaultValue")&&vn(e,t.type,Ce(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rr(e,t,o){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var i=t.type;if(!(i!=="submit"&&i!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,o||t===e.value||(e.value=t),e.defaultValue=t}o=e.name,o!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,o!==""&&(e.name=o)}function vn(e,t,o){(t!=="number"||At(e.ownerDocument)!==e)&&(o==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+o&&(e.defaultValue=""+o))}var or=Array.isArray;function Ut(e,t,o,i){if(e=e.options,t){t={};for(var u=0;u<o.length;u++)t["$"+o[u]]=!0;for(o=0;o<e.length;o++)u=t.hasOwnProperty("$"+e[o].value),e[o].selected!==u&&(e[o].selected=u),u&&i&&(e[o].defaultSelected=!0)}else{for(o=""+Ce(o),t=null,u=0;u<e.length;u++){if(e[u].value===o){e[u].selected=!0,i&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function po(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function yn(e,t){var o=t.value;if(o==null){if(o=t.children,t=t.defaultValue,o!=null){if(t!=null)throw Error(s(92));if(or(o)){if(1<o.length)throw Error(s(93));o=o[0]}t=o}t==null&&(t=""),o=t}e._wrapperState={initialValue:Ce(o)}}function li(e,t){var o=Ce(t.value),i=Ce(t.defaultValue);o!=null&&(o=""+o,o!==e.value&&(e.value=o),t.defaultValue==null&&e.defaultValue!==o&&(e.defaultValue=o)),i!=null&&(e.defaultValue=""+i)}function ai(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function mt(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function xn(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?mt(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ho,ui=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,o,i,u){MSApp.execUnsafeLocalFunction(function(){return e(t,o,i,u)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ho=ho||document.createElement("div"),ho.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ho.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function wn(e,t){if(t){var o=e.firstChild;if(o&&o===e.lastChild&&o.nodeType===3){o.nodeValue=t;return}}e.textContent=t}var Br={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ci=["Webkit","ms","Moz","O"];Object.keys(Br).forEach(function(e){ci.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Br[t]=Br[e]})});function mo(e,t,o){return t==null||typeof t=="boolean"||t===""?"":o||typeof t!="number"||t===0||Br.hasOwnProperty(e)&&Br[e]?(""+t).trim():t+"px"}function Ln(e,t){e=e.style;for(var o in t)if(t.hasOwnProperty(o)){var i=o.indexOf("--")===0,u=mo(o,t[o],i);o==="float"&&(o="cssFloat"),i?e.setProperty(o,u):e[o]=u}}var di=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function bn(e,t){if(t){if(di[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function ls(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var as=null;function go(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var vo=null,sr=null,jn=null;function Zt(e){if(e=Ps(e)){if(typeof vo!="function")throw Error(s(280));var t=e.stateNode;t&&(t=Di(t),vo(e.stateNode,e.type,t))}}function fi(e){sr?jn?jn.push(e):jn=[e]:sr=e}function xe(){if(sr){var e=sr,t=jn;if(jn=sr=null,Zt(e),t)for(e=0;e<t.length;e++)Zt(t[e])}}function Ae(e,t){return e(t)}function Ie(){}var gt=!1;function bt(e,t,o){if(gt)return e(t,o);gt=!0;try{return Ae(e,t,o)}finally{gt=!1,(sr!==null||jn!==null)&&(Ie(),xe())}}function jt(e,t){var o=e.stateNode;if(o===null)return null;var i=Di(o);if(i===null)return null;o=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(o&&typeof o!="function")throw Error(s(231,t,typeof o));return o}var Jt=!1;if(h)try{var st={};Object.defineProperty(st,"passive",{get:function(){Jt=!0}}),window.addEventListener("test",st,st),window.removeEventListener("test",st,st)}catch{Jt=!1}function kn(e,t,o,i,u,d,m,N,C){var I=Array.prototype.slice.call(arguments,3);try{t.apply(o,I)}catch(W){this.onError(W)}}var us=!1,pi=null,hi=!1,na=null,Kg={onError:function(e){us=!0,pi=e}};function qg(e,t,o,i,u,d,m,N,C){us=!1,pi=null,kn.apply(Kg,arguments)}function Gg(e,t,o,i,u,d,m,N,C){if(qg.apply(this,arguments),us){if(us){var I=pi;us=!1,pi=null}else throw Error(s(198));hi||(hi=!0,na=I)}}function Vr(e){var t=e,o=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(o=t.return),e=t.return;while(e)}return t.tag===3?o:null}function zc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Uc(e){if(Vr(e)!==e)throw Error(s(188))}function Yg(e){var t=e.alternate;if(!t){if(t=Vr(e),t===null)throw Error(s(188));return t!==e?null:e}for(var o=e,i=t;;){var u=o.return;if(u===null)break;var d=u.alternate;if(d===null){if(i=u.return,i!==null){o=i;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===o)return Uc(u),e;if(d===i)return Uc(u),t;d=d.sibling}throw Error(s(188))}if(o.return!==i.return)o=u,i=d;else{for(var m=!1,N=u.child;N;){if(N===o){m=!0,o=u,i=d;break}if(N===i){m=!0,i=u,o=d;break}N=N.sibling}if(!m){for(N=d.child;N;){if(N===o){m=!0,o=d,i=u;break}if(N===i){m=!0,i=d,o=u;break}N=N.sibling}if(!m)throw Error(s(189))}}if(o.alternate!==i)throw Error(s(190))}if(o.tag!==3)throw Error(s(188));return o.stateNode.current===o?e:t}function $c(e){return e=Yg(e),e!==null?Bc(e):null}function Bc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bc(e);if(t!==null)return t;e=e.sibling}return null}var Vc=r.unstable_scheduleCallback,Hc=r.unstable_cancelCallback,Xg=r.unstable_shouldYield,Zg=r.unstable_requestPaint,Ye=r.unstable_now,Jg=r.unstable_getCurrentPriorityLevel,ra=r.unstable_ImmediatePriority,Wc=r.unstable_UserBlockingPriority,mi=r.unstable_NormalPriority,e0=r.unstable_LowPriority,Qc=r.unstable_IdlePriority,gi=null,Nn=null;function t0(e){if(Nn&&typeof Nn.onCommitFiberRoot=="function")try{Nn.onCommitFiberRoot(gi,e,void 0,(e.current.flags&128)===128)}catch{}}var en=Math.clz32?Math.clz32:o0,n0=Math.log,r0=Math.LN2;function o0(e){return e>>>=0,e===0?32:31-(n0(e)/r0|0)|0}var vi=64,yi=4194304;function cs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function xi(e,t){var o=e.pendingLanes;if(o===0)return 0;var i=0,u=e.suspendedLanes,d=e.pingedLanes,m=o&268435455;if(m!==0){var N=m&~u;N!==0?i=cs(N):(d&=m,d!==0&&(i=cs(d)))}else m=o&~u,m!==0?i=cs(m):d!==0&&(i=cs(d));if(i===0)return 0;if(t!==0&&t!==i&&(t&u)===0&&(u=i&-i,d=t&-t,u>=d||u===16&&(d&4194240)!==0))return t;if((i&4)!==0&&(i|=o&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=i;0<t;)o=31-en(t),u=1<<o,i|=e[o],t&=~u;return i}function s0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function i0(e,t){for(var o=e.suspendedLanes,i=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes;0<d;){var m=31-en(d),N=1<<m,C=u[m];C===-1?((N&o)===0||(N&i)!==0)&&(u[m]=s0(N,t)):C<=t&&(e.expiredLanes|=N),d&=~N}}function oa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Kc(){var e=vi;return vi<<=1,(vi&4194240)===0&&(vi=64),e}function sa(e){for(var t=[],o=0;31>o;o++)t.push(e);return t}function ds(e,t,o){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-en(t),e[t]=o}function l0(e,t){var o=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var i=e.eventTimes;for(e=e.expirationTimes;0<o;){var u=31-en(o),d=1<<u;t[u]=0,i[u]=-1,e[u]=-1,o&=~d}}function ia(e,t){var o=e.entangledLanes|=t;for(e=e.entanglements;o;){var i=31-en(o),u=1<<i;u&t|e[i]&t&&(e[i]|=t),o&=~u}}var Me=0;function qc(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Gc,la,Yc,Xc,Zc,aa=!1,wi=[],ir=null,lr=null,ar=null,fs=new Map,ps=new Map,ur=[],a0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Jc(e,t){switch(e){case"focusin":case"focusout":ir=null;break;case"dragenter":case"dragleave":lr=null;break;case"mouseover":case"mouseout":ar=null;break;case"pointerover":case"pointerout":fs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ps.delete(t.pointerId)}}function hs(e,t,o,i,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:o,eventSystemFlags:i,nativeEvent:d,targetContainers:[u]},t!==null&&(t=Ps(t),t!==null&&la(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function u0(e,t,o,i,u){switch(t){case"focusin":return ir=hs(ir,e,t,o,i,u),!0;case"dragenter":return lr=hs(lr,e,t,o,i,u),!0;case"mouseover":return ar=hs(ar,e,t,o,i,u),!0;case"pointerover":var d=u.pointerId;return fs.set(d,hs(fs.get(d)||null,e,t,o,i,u)),!0;case"gotpointercapture":return d=u.pointerId,ps.set(d,hs(ps.get(d)||null,e,t,o,i,u)),!0}return!1}function ed(e){var t=Hr(e.target);if(t!==null){var o=Vr(t);if(o!==null){if(t=o.tag,t===13){if(t=zc(o),t!==null){e.blockedOn=t,Zc(e.priority,function(){Yc(o)});return}}else if(t===3&&o.stateNode.current.memoizedState.isDehydrated){e.blockedOn=o.tag===3?o.stateNode.containerInfo:null;return}}}e.blockedOn=null}function bi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var o=ca(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(o===null){o=e.nativeEvent;var i=new o.constructor(o.type,o);as=i,o.target.dispatchEvent(i),as=null}else return t=Ps(o),t!==null&&la(t),e.blockedOn=o,!1;t.shift()}return!0}function td(e,t,o){bi(e)&&o.delete(t)}function c0(){aa=!1,ir!==null&&bi(ir)&&(ir=null),lr!==null&&bi(lr)&&(lr=null),ar!==null&&bi(ar)&&(ar=null),fs.forEach(td),ps.forEach(td)}function ms(e,t){e.blockedOn===t&&(e.blockedOn=null,aa||(aa=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,c0)))}function gs(e){function t(u){return ms(u,e)}if(0<wi.length){ms(wi[0],e);for(var o=1;o<wi.length;o++){var i=wi[o];i.blockedOn===e&&(i.blockedOn=null)}}for(ir!==null&&ms(ir,e),lr!==null&&ms(lr,e),ar!==null&&ms(ar,e),fs.forEach(t),ps.forEach(t),o=0;o<ur.length;o++)i=ur[o],i.blockedOn===e&&(i.blockedOn=null);for(;0<ur.length&&(o=ur[0],o.blockedOn===null);)ed(o),o.blockedOn===null&&ur.shift()}var yo=z.ReactCurrentBatchConfig,ji=!0;function d0(e,t,o,i){var u=Me,d=yo.transition;yo.transition=null;try{Me=1,ua(e,t,o,i)}finally{Me=u,yo.transition=d}}function f0(e,t,o,i){var u=Me,d=yo.transition;yo.transition=null;try{Me=4,ua(e,t,o,i)}finally{Me=u,yo.transition=d}}function ua(e,t,o,i){if(ji){var u=ca(e,t,o,i);if(u===null)Ea(e,t,i,ki,o),Jc(e,i);else if(u0(u,e,t,o,i))i.stopPropagation();else if(Jc(e,i),t&4&&-1<a0.indexOf(e)){for(;u!==null;){var d=Ps(u);if(d!==null&&Gc(d),d=ca(e,t,o,i),d===null&&Ea(e,t,i,ki,o),d===u)break;u=d}u!==null&&i.stopPropagation()}else Ea(e,t,i,null,o)}}var ki=null;function ca(e,t,o,i){if(ki=null,e=go(i),e=Hr(e),e!==null)if(t=Vr(e),t===null)e=null;else if(o=t.tag,o===13){if(e=zc(t),e!==null)return e;e=null}else if(o===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ki=e,null}function nd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Jg()){case ra:return 1;case Wc:return 4;case mi:case e0:return 16;case Qc:return 536870912;default:return 16}default:return 16}}var cr=null,da=null,Ni=null;function rd(){if(Ni)return Ni;var e,t=da,o=t.length,i,u="value"in cr?cr.value:cr.textContent,d=u.length;for(e=0;e<o&&t[e]===u[e];e++);var m=o-e;for(i=1;i<=m&&t[o-i]===u[d-i];i++);return Ni=u.slice(e,1<i?1-i:void 0)}function Si(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ci(){return!0}function od(){return!1}function Ot(e){function t(o,i,u,d,m){this._reactName=o,this._targetInst=u,this.type=i,this.nativeEvent=d,this.target=m,this.currentTarget=null;for(var N in e)e.hasOwnProperty(N)&&(o=e[N],this[N]=o?o(d):d[N]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Ci:od,this.isPropagationStopped=od,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var o=this.nativeEvent;o&&(o.preventDefault?o.preventDefault():typeof o.returnValue!="unknown"&&(o.returnValue=!1),this.isDefaultPrevented=Ci)},stopPropagation:function(){var o=this.nativeEvent;o&&(o.stopPropagation?o.stopPropagation():typeof o.cancelBubble!="unknown"&&(o.cancelBubble=!0),this.isPropagationStopped=Ci)},persist:function(){},isPersistent:Ci}),t}var xo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fa=Ot(xo),vs=K({},xo,{view:0,detail:0}),p0=Ot(vs),pa,ha,ys,Ei=K({},vs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ga,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ys&&(ys&&e.type==="mousemove"?(pa=e.screenX-ys.screenX,ha=e.screenY-ys.screenY):ha=pa=0,ys=e),pa)},movementY:function(e){return"movementY"in e?e.movementY:ha}}),sd=Ot(Ei),h0=K({},Ei,{dataTransfer:0}),m0=Ot(h0),g0=K({},vs,{relatedTarget:0}),ma=Ot(g0),v0=K({},xo,{animationName:0,elapsedTime:0,pseudoElement:0}),y0=Ot(v0),x0=K({},xo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),w0=Ot(x0),b0=K({},xo,{data:0}),id=Ot(b0),j0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},k0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},N0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function S0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=N0[e])?!!t[e]:!1}function ga(){return S0}var C0=K({},vs,{key:function(e){if(e.key){var t=j0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Si(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?k0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ga,charCode:function(e){return e.type==="keypress"?Si(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Si(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),E0=Ot(C0),P0=K({},Ei,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ld=Ot(P0),T0=K({},vs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ga}),R0=Ot(T0),_0=K({},xo,{propertyName:0,elapsedTime:0,pseudoElement:0}),A0=Ot(_0),O0=K({},Ei,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),M0=Ot(O0),D0=[9,13,27,32],va=h&&"CompositionEvent"in window,xs=null;h&&"documentMode"in document&&(xs=document.documentMode);var I0=h&&"TextEvent"in window&&!xs,ad=h&&(!va||xs&&8<xs&&11>=xs),ud=" ",cd=!1;function dd(e,t){switch(e){case"keyup":return D0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function fd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var wo=!1;function F0(e,t){switch(e){case"compositionend":return fd(t);case"keypress":return t.which!==32?null:(cd=!0,ud);case"textInput":return e=t.data,e===ud&&cd?null:e;default:return null}}function L0(e,t){if(wo)return e==="compositionend"||!va&&dd(e,t)?(e=rd(),Ni=da=cr=null,wo=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ad&&t.locale!=="ko"?null:t.data;default:return null}}var z0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function pd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!z0[e.type]:t==="textarea"}function hd(e,t,o,i){fi(i),t=Ai(t,"onChange"),0<t.length&&(o=new fa("onChange","change",null,o,i),e.push({event:o,listeners:t}))}var ws=null,bs=null;function U0(e){Ad(e,0)}function Pi(e){var t=So(e);if(Fn(t))return e}function $0(e,t){if(e==="change")return t}var md=!1;if(h){var ya;if(h){var xa="oninput"in document;if(!xa){var gd=document.createElement("div");gd.setAttribute("oninput","return;"),xa=typeof gd.oninput=="function"}ya=xa}else ya=!1;md=ya&&(!document.documentMode||9<document.documentMode)}function vd(){ws&&(ws.detachEvent("onpropertychange",yd),bs=ws=null)}function yd(e){if(e.propertyName==="value"&&Pi(bs)){var t=[];hd(t,bs,e,go(e)),bt(U0,t)}}function B0(e,t,o){e==="focusin"?(vd(),ws=t,bs=o,ws.attachEvent("onpropertychange",yd)):e==="focusout"&&vd()}function V0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Pi(bs)}function H0(e,t){if(e==="click")return Pi(t)}function W0(e,t){if(e==="input"||e==="change")return Pi(t)}function Q0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var tn=typeof Object.is=="function"?Object.is:Q0;function js(e,t){if(tn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;for(i=0;i<o.length;i++){var u=o[i];if(!g.call(t,u)||!tn(e[u],t[u]))return!1}return!0}function xd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wd(e,t){var o=xd(e);e=0;for(var i;o;){if(o.nodeType===3){if(i=e+o.textContent.length,e<=t&&i>=t)return{node:o,offset:t-e};e=i}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=xd(o)}}function bd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?bd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function jd(){for(var e=window,t=At();t instanceof e.HTMLIFrameElement;){try{var o=typeof t.contentWindow.location.href=="string"}catch{o=!1}if(o)e=t.contentWindow;else break;t=At(e.document)}return t}function wa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function K0(e){var t=jd(),o=e.focusedElem,i=e.selectionRange;if(t!==o&&o&&o.ownerDocument&&bd(o.ownerDocument.documentElement,o)){if(i!==null&&wa(o)){if(t=i.start,e=i.end,e===void 0&&(e=t),"selectionStart"in o)o.selectionStart=t,o.selectionEnd=Math.min(e,o.value.length);else if(e=(t=o.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var u=o.textContent.length,d=Math.min(i.start,u);i=i.end===void 0?d:Math.min(i.end,u),!e.extend&&d>i&&(u=i,i=d,d=u),u=wd(o,d);var m=wd(o,i);u&&m&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==m.node||e.focusOffset!==m.offset)&&(t=t.createRange(),t.setStart(u.node,u.offset),e.removeAllRanges(),d>i?(e.addRange(t),e.extend(m.node,m.offset)):(t.setEnd(m.node,m.offset),e.addRange(t)))}}for(t=[],e=o;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<t.length;o++)e=t[o],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var q0=h&&"documentMode"in document&&11>=document.documentMode,bo=null,ba=null,ks=null,ja=!1;function kd(e,t,o){var i=o.window===o?o.document:o.nodeType===9?o:o.ownerDocument;ja||bo==null||bo!==At(i)||(i=bo,"selectionStart"in i&&wa(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),ks&&js(ks,i)||(ks=i,i=Ai(ba,"onSelect"),0<i.length&&(t=new fa("onSelect","select",null,t,o),e.push({event:t,listeners:i}),t.target=bo)))}function Ti(e,t){var o={};return o[e.toLowerCase()]=t.toLowerCase(),o["Webkit"+e]="webkit"+t,o["Moz"+e]="moz"+t,o}var jo={animationend:Ti("Animation","AnimationEnd"),animationiteration:Ti("Animation","AnimationIteration"),animationstart:Ti("Animation","AnimationStart"),transitionend:Ti("Transition","TransitionEnd")},ka={},Nd={};h&&(Nd=document.createElement("div").style,"AnimationEvent"in window||(delete jo.animationend.animation,delete jo.animationiteration.animation,delete jo.animationstart.animation),"TransitionEvent"in window||delete jo.transitionend.transition);function Ri(e){if(ka[e])return ka[e];if(!jo[e])return e;var t=jo[e],o;for(o in t)if(t.hasOwnProperty(o)&&o in Nd)return ka[e]=t[o];return e}var Sd=Ri("animationend"),Cd=Ri("animationiteration"),Ed=Ri("animationstart"),Pd=Ri("transitionend"),Td=new Map,Rd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function dr(e,t){Td.set(e,t),f(t,[e])}for(var Na=0;Na<Rd.length;Na++){var Sa=Rd[Na],G0=Sa.toLowerCase(),Y0=Sa[0].toUpperCase()+Sa.slice(1);dr(G0,"on"+Y0)}dr(Sd,"onAnimationEnd"),dr(Cd,"onAnimationIteration"),dr(Ed,"onAnimationStart"),dr("dblclick","onDoubleClick"),dr("focusin","onFocus"),dr("focusout","onBlur"),dr(Pd,"onTransitionEnd"),p("onMouseEnter",["mouseout","mouseover"]),p("onMouseLeave",["mouseout","mouseover"]),p("onPointerEnter",["pointerout","pointerover"]),p("onPointerLeave",["pointerout","pointerover"]),f("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),f("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),f("onBeforeInput",["compositionend","keypress","textInput","paste"]),f("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ns="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),X0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ns));function _d(e,t,o){var i=e.type||"unknown-event";e.currentTarget=o,Gg(i,t,void 0,e),e.currentTarget=null}function Ad(e,t){t=(t&4)!==0;for(var o=0;o<e.length;o++){var i=e[o],u=i.event;i=i.listeners;e:{var d=void 0;if(t)for(var m=i.length-1;0<=m;m--){var N=i[m],C=N.instance,I=N.currentTarget;if(N=N.listener,C!==d&&u.isPropagationStopped())break e;_d(u,N,I),d=C}else for(m=0;m<i.length;m++){if(N=i[m],C=N.instance,I=N.currentTarget,N=N.listener,C!==d&&u.isPropagationStopped())break e;_d(u,N,I),d=C}}}if(hi)throw e=na,hi=!1,na=null,e}function Le(e,t){var o=t[Oa];o===void 0&&(o=t[Oa]=new Set);var i=e+"__bubble";o.has(i)||(Od(t,e,2,!1),o.add(i))}function Ca(e,t,o){var i=0;t&&(i|=4),Od(o,e,i,t)}var _i="_reactListening"+Math.random().toString(36).slice(2);function Ss(e){if(!e[_i]){e[_i]=!0,l.forEach(function(o){o!=="selectionchange"&&(X0.has(o)||Ca(o,!1,e),Ca(o,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[_i]||(t[_i]=!0,Ca("selectionchange",!1,t))}}function Od(e,t,o,i){switch(nd(t)){case 1:var u=d0;break;case 4:u=f0;break;default:u=ua}o=u.bind(null,t,o,e),u=void 0,!Jt||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),i?u!==void 0?e.addEventListener(t,o,{capture:!0,passive:u}):e.addEventListener(t,o,!0):u!==void 0?e.addEventListener(t,o,{passive:u}):e.addEventListener(t,o,!1)}function Ea(e,t,o,i,u){var d=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var m=i.tag;if(m===3||m===4){var N=i.stateNode.containerInfo;if(N===u||N.nodeType===8&&N.parentNode===u)break;if(m===4)for(m=i.return;m!==null;){var C=m.tag;if((C===3||C===4)&&(C=m.stateNode.containerInfo,C===u||C.nodeType===8&&C.parentNode===u))return;m=m.return}for(;N!==null;){if(m=Hr(N),m===null)return;if(C=m.tag,C===5||C===6){i=d=m;continue e}N=N.parentNode}}i=i.return}bt(function(){var I=d,W=go(o),Q=[];e:{var H=Td.get(e);if(H!==void 0){var ne=fa,ie=e;switch(e){case"keypress":if(Si(o)===0)break e;case"keydown":case"keyup":ne=E0;break;case"focusin":ie="focus",ne=ma;break;case"focusout":ie="blur",ne=ma;break;case"beforeblur":case"afterblur":ne=ma;break;case"click":if(o.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ne=sd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ne=m0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ne=R0;break;case Sd:case Cd:case Ed:ne=y0;break;case Pd:ne=A0;break;case"scroll":ne=p0;break;case"wheel":ne=M0;break;case"copy":case"cut":case"paste":ne=w0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ne=ld}var le=(t&4)!==0,Xe=!le&&e==="scroll",M=le?H!==null?H+"Capture":null:H;le=[];for(var T=I,D;T!==null;){D=T;var q=D.stateNode;if(D.tag===5&&q!==null&&(D=q,M!==null&&(q=jt(T,M),q!=null&&le.push(Cs(T,q,D)))),Xe)break;T=T.return}0<le.length&&(H=new ne(H,ie,null,o,W),Q.push({event:H,listeners:le}))}}if((t&7)===0){e:{if(H=e==="mouseover"||e==="pointerover",ne=e==="mouseout"||e==="pointerout",H&&o!==as&&(ie=o.relatedTarget||o.fromElement)&&(Hr(ie)||ie[zn]))break e;if((ne||H)&&(H=W.window===W?W:(H=W.ownerDocument)?H.defaultView||H.parentWindow:window,ne?(ie=o.relatedTarget||o.toElement,ne=I,ie=ie?Hr(ie):null,ie!==null&&(Xe=Vr(ie),ie!==Xe||ie.tag!==5&&ie.tag!==6)&&(ie=null)):(ne=null,ie=I),ne!==ie)){if(le=sd,q="onMouseLeave",M="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(le=ld,q="onPointerLeave",M="onPointerEnter",T="pointer"),Xe=ne==null?H:So(ne),D=ie==null?H:So(ie),H=new le(q,T+"leave",ne,o,W),H.target=Xe,H.relatedTarget=D,q=null,Hr(W)===I&&(le=new le(M,T+"enter",ie,o,W),le.target=D,le.relatedTarget=Xe,q=le),Xe=q,ne&&ie)t:{for(le=ne,M=ie,T=0,D=le;D;D=ko(D))T++;for(D=0,q=M;q;q=ko(q))D++;for(;0<T-D;)le=ko(le),T--;for(;0<D-T;)M=ko(M),D--;for(;T--;){if(le===M||M!==null&&le===M.alternate)break t;le=ko(le),M=ko(M)}le=null}else le=null;ne!==null&&Md(Q,H,ne,le,!1),ie!==null&&Xe!==null&&Md(Q,Xe,ie,le,!0)}}e:{if(H=I?So(I):window,ne=H.nodeName&&H.nodeName.toLowerCase(),ne==="select"||ne==="input"&&H.type==="file")var ue=$0;else if(pd(H))if(md)ue=W0;else{ue=V0;var de=B0}else(ne=H.nodeName)&&ne.toLowerCase()==="input"&&(H.type==="checkbox"||H.type==="radio")&&(ue=H0);if(ue&&(ue=ue(e,I))){hd(Q,ue,o,W);break e}de&&de(e,H,I),e==="focusout"&&(de=H._wrapperState)&&de.controlled&&H.type==="number"&&vn(H,"number",H.value)}switch(de=I?So(I):window,e){case"focusin":(pd(de)||de.contentEditable==="true")&&(bo=de,ba=I,ks=null);break;case"focusout":ks=ba=bo=null;break;case"mousedown":ja=!0;break;case"contextmenu":case"mouseup":case"dragend":ja=!1,kd(Q,o,W);break;case"selectionchange":if(q0)break;case"keydown":case"keyup":kd(Q,o,W)}var fe;if(va)e:{switch(e){case"compositionstart":var ye="onCompositionStart";break e;case"compositionend":ye="onCompositionEnd";break e;case"compositionupdate":ye="onCompositionUpdate";break e}ye=void 0}else wo?dd(e,o)&&(ye="onCompositionEnd"):e==="keydown"&&o.keyCode===229&&(ye="onCompositionStart");ye&&(ad&&o.locale!=="ko"&&(wo||ye!=="onCompositionStart"?ye==="onCompositionEnd"&&wo&&(fe=rd()):(cr=W,da="value"in cr?cr.value:cr.textContent,wo=!0)),de=Ai(I,ye),0<de.length&&(ye=new id(ye,e,null,o,W),Q.push({event:ye,listeners:de}),fe?ye.data=fe:(fe=fd(o),fe!==null&&(ye.data=fe)))),(fe=I0?F0(e,o):L0(e,o))&&(I=Ai(I,"onBeforeInput"),0<I.length&&(W=new id("onBeforeInput","beforeinput",null,o,W),Q.push({event:W,listeners:I}),W.data=fe))}Ad(Q,t)})}function Cs(e,t,o){return{instance:e,listener:t,currentTarget:o}}function Ai(e,t){for(var o=t+"Capture",i=[];e!==null;){var u=e,d=u.stateNode;u.tag===5&&d!==null&&(u=d,d=jt(e,o),d!=null&&i.unshift(Cs(e,d,u)),d=jt(e,t),d!=null&&i.push(Cs(e,d,u))),e=e.return}return i}function ko(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Md(e,t,o,i,u){for(var d=t._reactName,m=[];o!==null&&o!==i;){var N=o,C=N.alternate,I=N.stateNode;if(C!==null&&C===i)break;N.tag===5&&I!==null&&(N=I,u?(C=jt(o,d),C!=null&&m.unshift(Cs(o,C,N))):u||(C=jt(o,d),C!=null&&m.push(Cs(o,C,N)))),o=o.return}m.length!==0&&e.push({event:t,listeners:m})}var Z0=/\r\n?/g,J0=/\u0000|\uFFFD/g;function Dd(e){return(typeof e=="string"?e:""+e).replace(Z0,`
`).replace(J0,"")}function Oi(e,t,o){if(t=Dd(t),Dd(e)!==t&&o)throw Error(s(425))}function Mi(){}var Pa=null,Ta=null;function Ra(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var _a=typeof setTimeout=="function"?setTimeout:void 0,ev=typeof clearTimeout=="function"?clearTimeout:void 0,Id=typeof Promise=="function"?Promise:void 0,tv=typeof queueMicrotask=="function"?queueMicrotask:typeof Id<"u"?function(e){return Id.resolve(null).then(e).catch(nv)}:_a;function nv(e){setTimeout(function(){throw e})}function Aa(e,t){var o=t,i=0;do{var u=o.nextSibling;if(e.removeChild(o),u&&u.nodeType===8)if(o=u.data,o==="/$"){if(i===0){e.removeChild(u),gs(t);return}i--}else o!=="$"&&o!=="$?"&&o!=="$!"||i++;o=u}while(o);gs(t)}function fr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Fd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var o=e.data;if(o==="$"||o==="$!"||o==="$?"){if(t===0)return e;t--}else o==="/$"&&t++}e=e.previousSibling}return null}var No=Math.random().toString(36).slice(2),Sn="__reactFiber$"+No,Es="__reactProps$"+No,zn="__reactContainer$"+No,Oa="__reactEvents$"+No,rv="__reactListeners$"+No,ov="__reactHandles$"+No;function Hr(e){var t=e[Sn];if(t)return t;for(var o=e.parentNode;o;){if(t=o[zn]||o[Sn]){if(o=t.alternate,t.child!==null||o!==null&&o.child!==null)for(e=Fd(e);e!==null;){if(o=e[Sn])return o;e=Fd(e)}return t}e=o,o=e.parentNode}return null}function Ps(e){return e=e[Sn]||e[zn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function So(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function Di(e){return e[Es]||null}var Ma=[],Co=-1;function pr(e){return{current:e}}function ze(e){0>Co||(e.current=Ma[Co],Ma[Co]=null,Co--)}function Fe(e,t){Co++,Ma[Co]=e.current,e.current=t}var hr={},ut=pr(hr),kt=pr(!1),Wr=hr;function Eo(e,t){var o=e.type.contextTypes;if(!o)return hr;var i=e.stateNode;if(i&&i.__reactInternalMemoizedUnmaskedChildContext===t)return i.__reactInternalMemoizedMaskedChildContext;var u={},d;for(d in o)u[d]=t[d];return i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=u),u}function Nt(e){return e=e.childContextTypes,e!=null}function Ii(){ze(kt),ze(ut)}function Ld(e,t,o){if(ut.current!==hr)throw Error(s(168));Fe(ut,t),Fe(kt,o)}function zd(e,t,o){var i=e.stateNode;if(t=t.childContextTypes,typeof i.getChildContext!="function")return o;i=i.getChildContext();for(var u in i)if(!(u in t))throw Error(s(108,ce(e)||"Unknown",u));return K({},o,i)}function Fi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hr,Wr=ut.current,Fe(ut,e),Fe(kt,kt.current),!0}function Ud(e,t,o){var i=e.stateNode;if(!i)throw Error(s(169));o?(e=zd(e,t,Wr),i.__reactInternalMemoizedMergedChildContext=e,ze(kt),ze(ut),Fe(ut,e)):ze(kt),Fe(kt,o)}var Un=null,Li=!1,Da=!1;function $d(e){Un===null?Un=[e]:Un.push(e)}function sv(e){Li=!0,$d(e)}function mr(){if(!Da&&Un!==null){Da=!0;var e=0,t=Me;try{var o=Un;for(Me=1;e<o.length;e++){var i=o[e];do i=i(!0);while(i!==null)}Un=null,Li=!1}catch(u){throw Un!==null&&(Un=Un.slice(e+1)),Vc(ra,mr),u}finally{Me=t,Da=!1}}return null}var Po=[],To=0,zi=null,Ui=0,$t=[],Bt=0,Qr=null,$n=1,Bn="";function Kr(e,t){Po[To++]=Ui,Po[To++]=zi,zi=e,Ui=t}function Bd(e,t,o){$t[Bt++]=$n,$t[Bt++]=Bn,$t[Bt++]=Qr,Qr=e;var i=$n;e=Bn;var u=32-en(i)-1;i&=~(1<<u),o+=1;var d=32-en(t)+u;if(30<d){var m=u-u%5;d=(i&(1<<m)-1).toString(32),i>>=m,u-=m,$n=1<<32-en(t)+u|o<<u|i,Bn=d+e}else $n=1<<d|o<<u|i,Bn=e}function Ia(e){e.return!==null&&(Kr(e,1),Bd(e,1,0))}function Fa(e){for(;e===zi;)zi=Po[--To],Po[To]=null,Ui=Po[--To],Po[To]=null;for(;e===Qr;)Qr=$t[--Bt],$t[Bt]=null,Bn=$t[--Bt],$t[Bt]=null,$n=$t[--Bt],$t[Bt]=null}var Mt=null,Dt=null,Be=!1,nn=null;function Vd(e,t){var o=Qt(5,null,null,0);o.elementType="DELETED",o.stateNode=t,o.return=e,t=e.deletions,t===null?(e.deletions=[o],e.flags|=16):t.push(o)}function Hd(e,t){switch(e.tag){case 5:var o=e.type;return t=t.nodeType!==1||o.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Mt=e,Dt=fr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Mt=e,Dt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(o=Qr!==null?{id:$n,overflow:Bn}:null,e.memoizedState={dehydrated:t,treeContext:o,retryLane:1073741824},o=Qt(18,null,null,0),o.stateNode=t,o.return=e,e.child=o,Mt=e,Dt=null,!0):!1;default:return!1}}function La(e){return(e.mode&1)!==0&&(e.flags&128)===0}function za(e){if(Be){var t=Dt;if(t){var o=t;if(!Hd(e,t)){if(La(e))throw Error(s(418));t=fr(o.nextSibling);var i=Mt;t&&Hd(e,t)?Vd(i,o):(e.flags=e.flags&-4097|2,Be=!1,Mt=e)}}else{if(La(e))throw Error(s(418));e.flags=e.flags&-4097|2,Be=!1,Mt=e}}}function Wd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Mt=e}function $i(e){if(e!==Mt)return!1;if(!Be)return Wd(e),Be=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ra(e.type,e.memoizedProps)),t&&(t=Dt)){if(La(e))throw Qd(),Error(s(418));for(;t;)Vd(e,t),t=fr(t.nextSibling)}if(Wd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var o=e.data;if(o==="/$"){if(t===0){Dt=fr(e.nextSibling);break e}t--}else o!=="$"&&o!=="$!"&&o!=="$?"||t++}e=e.nextSibling}Dt=null}}else Dt=Mt?fr(e.stateNode.nextSibling):null;return!0}function Qd(){for(var e=Dt;e;)e=fr(e.nextSibling)}function Ro(){Dt=Mt=null,Be=!1}function Ua(e){nn===null?nn=[e]:nn.push(e)}var iv=z.ReactCurrentBatchConfig;function Ts(e,t,o){if(e=o.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(o._owner){if(o=o._owner,o){if(o.tag!==1)throw Error(s(309));var i=o.stateNode}if(!i)throw Error(s(147,e));var u=i,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(m){var N=u.refs;m===null?delete N[d]:N[d]=m},t._stringRef=d,t)}if(typeof e!="string")throw Error(s(284));if(!o._owner)throw Error(s(290,e))}return e}function Bi(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Kd(e){var t=e._init;return t(e._payload)}function qd(e){function t(M,T){if(e){var D=M.deletions;D===null?(M.deletions=[T],M.flags|=16):D.push(T)}}function o(M,T){if(!e)return null;for(;T!==null;)t(M,T),T=T.sibling;return null}function i(M,T){for(M=new Map;T!==null;)T.key!==null?M.set(T.key,T):M.set(T.index,T),T=T.sibling;return M}function u(M,T){return M=kr(M,T),M.index=0,M.sibling=null,M}function d(M,T,D){return M.index=D,e?(D=M.alternate,D!==null?(D=D.index,D<T?(M.flags|=2,T):D):(M.flags|=2,T)):(M.flags|=1048576,T)}function m(M){return e&&M.alternate===null&&(M.flags|=2),M}function N(M,T,D,q){return T===null||T.tag!==6?(T=_u(D,M.mode,q),T.return=M,T):(T=u(T,D),T.return=M,T)}function C(M,T,D,q){var ue=D.type;return ue===$?W(M,T,D.props.children,q,D.key):T!==null&&(T.elementType===ue||typeof ue=="object"&&ue!==null&&ue.$$typeof===se&&Kd(ue)===T.type)?(q=u(T,D.props),q.ref=Ts(M,T,D),q.return=M,q):(q=fl(D.type,D.key,D.props,null,M.mode,q),q.ref=Ts(M,T,D),q.return=M,q)}function I(M,T,D,q){return T===null||T.tag!==4||T.stateNode.containerInfo!==D.containerInfo||T.stateNode.implementation!==D.implementation?(T=Au(D,M.mode,q),T.return=M,T):(T=u(T,D.children||[]),T.return=M,T)}function W(M,T,D,q,ue){return T===null||T.tag!==7?(T=to(D,M.mode,q,ue),T.return=M,T):(T=u(T,D),T.return=M,T)}function Q(M,T,D){if(typeof T=="string"&&T!==""||typeof T=="number")return T=_u(""+T,M.mode,D),T.return=M,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case O:return D=fl(T.type,T.key,T.props,null,M.mode,D),D.ref=Ts(M,null,T),D.return=M,D;case F:return T=Au(T,M.mode,D),T.return=M,T;case se:var q=T._init;return Q(M,q(T._payload),D)}if(or(T)||V(T))return T=to(T,M.mode,D,null),T.return=M,T;Bi(M,T)}return null}function H(M,T,D,q){var ue=T!==null?T.key:null;if(typeof D=="string"&&D!==""||typeof D=="number")return ue!==null?null:N(M,T,""+D,q);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case O:return D.key===ue?C(M,T,D,q):null;case F:return D.key===ue?I(M,T,D,q):null;case se:return ue=D._init,H(M,T,ue(D._payload),q)}if(or(D)||V(D))return ue!==null?null:W(M,T,D,q,null);Bi(M,D)}return null}function ne(M,T,D,q,ue){if(typeof q=="string"&&q!==""||typeof q=="number")return M=M.get(D)||null,N(T,M,""+q,ue);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case O:return M=M.get(q.key===null?D:q.key)||null,C(T,M,q,ue);case F:return M=M.get(q.key===null?D:q.key)||null,I(T,M,q,ue);case se:var de=q._init;return ne(M,T,D,de(q._payload),ue)}if(or(q)||V(q))return M=M.get(D)||null,W(T,M,q,ue,null);Bi(T,q)}return null}function ie(M,T,D,q){for(var ue=null,de=null,fe=T,ye=T=0,ot=null;fe!==null&&ye<D.length;ye++){fe.index>ye?(ot=fe,fe=null):ot=fe.sibling;var Re=H(M,fe,D[ye],q);if(Re===null){fe===null&&(fe=ot);break}e&&fe&&Re.alternate===null&&t(M,fe),T=d(Re,T,ye),de===null?ue=Re:de.sibling=Re,de=Re,fe=ot}if(ye===D.length)return o(M,fe),Be&&Kr(M,ye),ue;if(fe===null){for(;ye<D.length;ye++)fe=Q(M,D[ye],q),fe!==null&&(T=d(fe,T,ye),de===null?ue=fe:de.sibling=fe,de=fe);return Be&&Kr(M,ye),ue}for(fe=i(M,fe);ye<D.length;ye++)ot=ne(fe,M,ye,D[ye],q),ot!==null&&(e&&ot.alternate!==null&&fe.delete(ot.key===null?ye:ot.key),T=d(ot,T,ye),de===null?ue=ot:de.sibling=ot,de=ot);return e&&fe.forEach(function(Nr){return t(M,Nr)}),Be&&Kr(M,ye),ue}function le(M,T,D,q){var ue=V(D);if(typeof ue!="function")throw Error(s(150));if(D=ue.call(D),D==null)throw Error(s(151));for(var de=ue=null,fe=T,ye=T=0,ot=null,Re=D.next();fe!==null&&!Re.done;ye++,Re=D.next()){fe.index>ye?(ot=fe,fe=null):ot=fe.sibling;var Nr=H(M,fe,Re.value,q);if(Nr===null){fe===null&&(fe=ot);break}e&&fe&&Nr.alternate===null&&t(M,fe),T=d(Nr,T,ye),de===null?ue=Nr:de.sibling=Nr,de=Nr,fe=ot}if(Re.done)return o(M,fe),Be&&Kr(M,ye),ue;if(fe===null){for(;!Re.done;ye++,Re=D.next())Re=Q(M,Re.value,q),Re!==null&&(T=d(Re,T,ye),de===null?ue=Re:de.sibling=Re,de=Re);return Be&&Kr(M,ye),ue}for(fe=i(M,fe);!Re.done;ye++,Re=D.next())Re=ne(fe,M,ye,Re.value,q),Re!==null&&(e&&Re.alternate!==null&&fe.delete(Re.key===null?ye:Re.key),T=d(Re,T,ye),de===null?ue=Re:de.sibling=Re,de=Re);return e&&fe.forEach(function(zv){return t(M,zv)}),Be&&Kr(M,ye),ue}function Xe(M,T,D,q){if(typeof D=="object"&&D!==null&&D.type===$&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case O:e:{for(var ue=D.key,de=T;de!==null;){if(de.key===ue){if(ue=D.type,ue===$){if(de.tag===7){o(M,de.sibling),T=u(de,D.props.children),T.return=M,M=T;break e}}else if(de.elementType===ue||typeof ue=="object"&&ue!==null&&ue.$$typeof===se&&Kd(ue)===de.type){o(M,de.sibling),T=u(de,D.props),T.ref=Ts(M,de,D),T.return=M,M=T;break e}o(M,de);break}else t(M,de);de=de.sibling}D.type===$?(T=to(D.props.children,M.mode,q,D.key),T.return=M,M=T):(q=fl(D.type,D.key,D.props,null,M.mode,q),q.ref=Ts(M,T,D),q.return=M,M=q)}return m(M);case F:e:{for(de=D.key;T!==null;){if(T.key===de)if(T.tag===4&&T.stateNode.containerInfo===D.containerInfo&&T.stateNode.implementation===D.implementation){o(M,T.sibling),T=u(T,D.children||[]),T.return=M,M=T;break e}else{o(M,T);break}else t(M,T);T=T.sibling}T=Au(D,M.mode,q),T.return=M,M=T}return m(M);case se:return de=D._init,Xe(M,T,de(D._payload),q)}if(or(D))return ie(M,T,D,q);if(V(D))return le(M,T,D,q);Bi(M,D)}return typeof D=="string"&&D!==""||typeof D=="number"?(D=""+D,T!==null&&T.tag===6?(o(M,T.sibling),T=u(T,D),T.return=M,M=T):(o(M,T),T=_u(D,M.mode,q),T.return=M,M=T),m(M)):o(M,T)}return Xe}var _o=qd(!0),Gd=qd(!1),Vi=pr(null),Hi=null,Ao=null,$a=null;function Ba(){$a=Ao=Hi=null}function Va(e){var t=Vi.current;ze(Vi),e._currentValue=t}function Ha(e,t,o){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===o)break;e=e.return}}function Oo(e,t){Hi=e,$a=Ao=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(St=!0),e.firstContext=null)}function Vt(e){var t=e._currentValue;if($a!==e)if(e={context:e,memoizedValue:t,next:null},Ao===null){if(Hi===null)throw Error(s(308));Ao=e,Hi.dependencies={lanes:0,firstContext:e}}else Ao=Ao.next=e;return t}var qr=null;function Wa(e){qr===null?qr=[e]:qr.push(e)}function Yd(e,t,o,i){var u=t.interleaved;return u===null?(o.next=o,Wa(t)):(o.next=u.next,u.next=o),t.interleaved=o,Vn(e,i)}function Vn(e,t){e.lanes|=t;var o=e.alternate;for(o!==null&&(o.lanes|=t),o=e,e=e.return;e!==null;)e.childLanes|=t,o=e.alternate,o!==null&&(o.childLanes|=t),o=e,e=e.return;return o.tag===3?o.stateNode:null}var gr=!1;function Qa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Xd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Hn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function vr(e,t,o){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Te&2)!==0){var u=i.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),i.pending=t,Vn(e,o)}return u=i.interleaved,u===null?(t.next=t,Wa(i)):(t.next=u.next,u.next=t),i.interleaved=t,Vn(e,o)}function Wi(e,t,o){if(t=t.updateQueue,t!==null&&(t=t.shared,(o&4194240)!==0)){var i=t.lanes;i&=e.pendingLanes,o|=i,t.lanes=o,ia(e,o)}}function Zd(e,t){var o=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,o===i)){var u=null,d=null;if(o=o.firstBaseUpdate,o!==null){do{var m={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};d===null?u=d=m:d=d.next=m,o=o.next}while(o!==null);d===null?u=d=t:d=d.next=t}else u=d=t;o={baseState:i.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:i.shared,effects:i.effects},e.updateQueue=o;return}e=o.lastBaseUpdate,e===null?o.firstBaseUpdate=t:e.next=t,o.lastBaseUpdate=t}function Qi(e,t,o,i){var u=e.updateQueue;gr=!1;var d=u.firstBaseUpdate,m=u.lastBaseUpdate,N=u.shared.pending;if(N!==null){u.shared.pending=null;var C=N,I=C.next;C.next=null,m===null?d=I:m.next=I,m=C;var W=e.alternate;W!==null&&(W=W.updateQueue,N=W.lastBaseUpdate,N!==m&&(N===null?W.firstBaseUpdate=I:N.next=I,W.lastBaseUpdate=C))}if(d!==null){var Q=u.baseState;m=0,W=I=C=null,N=d;do{var H=N.lane,ne=N.eventTime;if((i&H)===H){W!==null&&(W=W.next={eventTime:ne,lane:0,tag:N.tag,payload:N.payload,callback:N.callback,next:null});e:{var ie=e,le=N;switch(H=t,ne=o,le.tag){case 1:if(ie=le.payload,typeof ie=="function"){Q=ie.call(ne,Q,H);break e}Q=ie;break e;case 3:ie.flags=ie.flags&-65537|128;case 0:if(ie=le.payload,H=typeof ie=="function"?ie.call(ne,Q,H):ie,H==null)break e;Q=K({},Q,H);break e;case 2:gr=!0}}N.callback!==null&&N.lane!==0&&(e.flags|=64,H=u.effects,H===null?u.effects=[N]:H.push(N))}else ne={eventTime:ne,lane:H,tag:N.tag,payload:N.payload,callback:N.callback,next:null},W===null?(I=W=ne,C=Q):W=W.next=ne,m|=H;if(N=N.next,N===null){if(N=u.shared.pending,N===null)break;H=N,N=H.next,H.next=null,u.lastBaseUpdate=H,u.shared.pending=null}}while(!0);if(W===null&&(C=Q),u.baseState=C,u.firstBaseUpdate=I,u.lastBaseUpdate=W,t=u.shared.interleaved,t!==null){u=t;do m|=u.lane,u=u.next;while(u!==t)}else d===null&&(u.shared.lanes=0);Xr|=m,e.lanes=m,e.memoizedState=Q}}function Jd(e,t,o){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var i=e[t],u=i.callback;if(u!==null){if(i.callback=null,i=o,typeof u!="function")throw Error(s(191,u));u.call(i)}}}var Rs={},Cn=pr(Rs),_s=pr(Rs),As=pr(Rs);function Gr(e){if(e===Rs)throw Error(s(174));return e}function Ka(e,t){switch(Fe(As,t),Fe(_s,e),Fe(Cn,Rs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:xn(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=xn(t,e)}ze(Cn),Fe(Cn,t)}function Mo(){ze(Cn),ze(_s),ze(As)}function ef(e){Gr(As.current);var t=Gr(Cn.current),o=xn(t,e.type);t!==o&&(Fe(_s,e),Fe(Cn,o))}function qa(e){_s.current===e&&(ze(Cn),ze(_s))}var Ve=pr(0);function Ki(e){for(var t=e;t!==null;){if(t.tag===13){var o=t.memoizedState;if(o!==null&&(o=o.dehydrated,o===null||o.data==="$?"||o.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ga=[];function Ya(){for(var e=0;e<Ga.length;e++)Ga[e]._workInProgressVersionPrimary=null;Ga.length=0}var qi=z.ReactCurrentDispatcher,Xa=z.ReactCurrentBatchConfig,Yr=0,He=null,et=null,nt=null,Gi=!1,Os=!1,Ms=0,lv=0;function ct(){throw Error(s(321))}function Za(e,t){if(t===null)return!1;for(var o=0;o<t.length&&o<e.length;o++)if(!tn(e[o],t[o]))return!1;return!0}function Ja(e,t,o,i,u,d){if(Yr=d,He=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,qi.current=e===null||e.memoizedState===null?dv:fv,e=o(i,u),Os){d=0;do{if(Os=!1,Ms=0,25<=d)throw Error(s(301));d+=1,nt=et=null,t.updateQueue=null,qi.current=pv,e=o(i,u)}while(Os)}if(qi.current=Zi,t=et!==null&&et.next!==null,Yr=0,nt=et=He=null,Gi=!1,t)throw Error(s(300));return e}function eu(){var e=Ms!==0;return Ms=0,e}function En(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return nt===null?He.memoizedState=nt=e:nt=nt.next=e,nt}function Ht(){if(et===null){var e=He.alternate;e=e!==null?e.memoizedState:null}else e=et.next;var t=nt===null?He.memoizedState:nt.next;if(t!==null)nt=t,et=e;else{if(e===null)throw Error(s(310));et=e,e={memoizedState:et.memoizedState,baseState:et.baseState,baseQueue:et.baseQueue,queue:et.queue,next:null},nt===null?He.memoizedState=nt=e:nt=nt.next=e}return nt}function Ds(e,t){return typeof t=="function"?t(e):t}function tu(e){var t=Ht(),o=t.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=e;var i=et,u=i.baseQueue,d=o.pending;if(d!==null){if(u!==null){var m=u.next;u.next=d.next,d.next=m}i.baseQueue=u=d,o.pending=null}if(u!==null){d=u.next,i=i.baseState;var N=m=null,C=null,I=d;do{var W=I.lane;if((Yr&W)===W)C!==null&&(C=C.next={lane:0,action:I.action,hasEagerState:I.hasEagerState,eagerState:I.eagerState,next:null}),i=I.hasEagerState?I.eagerState:e(i,I.action);else{var Q={lane:W,action:I.action,hasEagerState:I.hasEagerState,eagerState:I.eagerState,next:null};C===null?(N=C=Q,m=i):C=C.next=Q,He.lanes|=W,Xr|=W}I=I.next}while(I!==null&&I!==d);C===null?m=i:C.next=N,tn(i,t.memoizedState)||(St=!0),t.memoizedState=i,t.baseState=m,t.baseQueue=C,o.lastRenderedState=i}if(e=o.interleaved,e!==null){u=e;do d=u.lane,He.lanes|=d,Xr|=d,u=u.next;while(u!==e)}else u===null&&(o.lanes=0);return[t.memoizedState,o.dispatch]}function nu(e){var t=Ht(),o=t.queue;if(o===null)throw Error(s(311));o.lastRenderedReducer=e;var i=o.dispatch,u=o.pending,d=t.memoizedState;if(u!==null){o.pending=null;var m=u=u.next;do d=e(d,m.action),m=m.next;while(m!==u);tn(d,t.memoizedState)||(St=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),o.lastRenderedState=d}return[d,i]}function tf(){}function nf(e,t){var o=He,i=Ht(),u=t(),d=!tn(i.memoizedState,u);if(d&&(i.memoizedState=u,St=!0),i=i.queue,ru(sf.bind(null,o,i,e),[e]),i.getSnapshot!==t||d||nt!==null&&nt.memoizedState.tag&1){if(o.flags|=2048,Is(9,of.bind(null,o,i,u,t),void 0,null),rt===null)throw Error(s(349));(Yr&30)!==0||rf(o,t,u)}return u}function rf(e,t,o){e.flags|=16384,e={getSnapshot:t,value:o},t=He.updateQueue,t===null?(t={lastEffect:null,stores:null},He.updateQueue=t,t.stores=[e]):(o=t.stores,o===null?t.stores=[e]:o.push(e))}function of(e,t,o,i){t.value=o,t.getSnapshot=i,lf(t)&&af(e)}function sf(e,t,o){return o(function(){lf(t)&&af(e)})}function lf(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!tn(e,o)}catch{return!0}}function af(e){var t=Vn(e,1);t!==null&&ln(t,e,1,-1)}function uf(e){var t=En();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ds,lastRenderedState:e},t.queue=e,e=e.dispatch=cv.bind(null,He,e),[t.memoizedState,e]}function Is(e,t,o,i){return e={tag:e,create:t,destroy:o,deps:i,next:null},t=He.updateQueue,t===null?(t={lastEffect:null,stores:null},He.updateQueue=t,t.lastEffect=e.next=e):(o=t.lastEffect,o===null?t.lastEffect=e.next=e:(i=o.next,o.next=e,e.next=i,t.lastEffect=e)),e}function cf(){return Ht().memoizedState}function Yi(e,t,o,i){var u=En();He.flags|=e,u.memoizedState=Is(1|t,o,void 0,i===void 0?null:i)}function Xi(e,t,o,i){var u=Ht();i=i===void 0?null:i;var d=void 0;if(et!==null){var m=et.memoizedState;if(d=m.destroy,i!==null&&Za(i,m.deps)){u.memoizedState=Is(t,o,d,i);return}}He.flags|=e,u.memoizedState=Is(1|t,o,d,i)}function df(e,t){return Yi(8390656,8,e,t)}function ru(e,t){return Xi(2048,8,e,t)}function ff(e,t){return Xi(4,2,e,t)}function pf(e,t){return Xi(4,4,e,t)}function hf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mf(e,t,o){return o=o!=null?o.concat([e]):null,Xi(4,4,hf.bind(null,t,e),o)}function ou(){}function gf(e,t){var o=Ht();t=t===void 0?null:t;var i=o.memoizedState;return i!==null&&t!==null&&Za(t,i[1])?i[0]:(o.memoizedState=[e,t],e)}function vf(e,t){var o=Ht();t=t===void 0?null:t;var i=o.memoizedState;return i!==null&&t!==null&&Za(t,i[1])?i[0]:(e=e(),o.memoizedState=[e,t],e)}function yf(e,t,o){return(Yr&21)===0?(e.baseState&&(e.baseState=!1,St=!0),e.memoizedState=o):(tn(o,t)||(o=Kc(),He.lanes|=o,Xr|=o,e.baseState=!0),t)}function av(e,t){var o=Me;Me=o!==0&&4>o?o:4,e(!0);var i=Xa.transition;Xa.transition={};try{e(!1),t()}finally{Me=o,Xa.transition=i}}function xf(){return Ht().memoizedState}function uv(e,t,o){var i=br(e);if(o={lane:i,action:o,hasEagerState:!1,eagerState:null,next:null},wf(e))bf(t,o);else if(o=Yd(e,t,o,i),o!==null){var u=yt();ln(o,e,i,u),jf(o,t,i)}}function cv(e,t,o){var i=br(e),u={lane:i,action:o,hasEagerState:!1,eagerState:null,next:null};if(wf(e))bf(t,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var m=t.lastRenderedState,N=d(m,o);if(u.hasEagerState=!0,u.eagerState=N,tn(N,m)){var C=t.interleaved;C===null?(u.next=u,Wa(t)):(u.next=C.next,C.next=u),t.interleaved=u;return}}catch{}finally{}o=Yd(e,t,u,i),o!==null&&(u=yt(),ln(o,e,i,u),jf(o,t,i))}}function wf(e){var t=e.alternate;return e===He||t!==null&&t===He}function bf(e,t){Os=Gi=!0;var o=e.pending;o===null?t.next=t:(t.next=o.next,o.next=t),e.pending=t}function jf(e,t,o){if((o&4194240)!==0){var i=t.lanes;i&=e.pendingLanes,o|=i,t.lanes=o,ia(e,o)}}var Zi={readContext:Vt,useCallback:ct,useContext:ct,useEffect:ct,useImperativeHandle:ct,useInsertionEffect:ct,useLayoutEffect:ct,useMemo:ct,useReducer:ct,useRef:ct,useState:ct,useDebugValue:ct,useDeferredValue:ct,useTransition:ct,useMutableSource:ct,useSyncExternalStore:ct,useId:ct,unstable_isNewReconciler:!1},dv={readContext:Vt,useCallback:function(e,t){return En().memoizedState=[e,t===void 0?null:t],e},useContext:Vt,useEffect:df,useImperativeHandle:function(e,t,o){return o=o!=null?o.concat([e]):null,Yi(4194308,4,hf.bind(null,t,e),o)},useLayoutEffect:function(e,t){return Yi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Yi(4,2,e,t)},useMemo:function(e,t){var o=En();return t=t===void 0?null:t,e=e(),o.memoizedState=[e,t],e},useReducer:function(e,t,o){var i=En();return t=o!==void 0?o(t):t,i.memoizedState=i.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},i.queue=e,e=e.dispatch=uv.bind(null,He,e),[i.memoizedState,e]},useRef:function(e){var t=En();return e={current:e},t.memoizedState=e},useState:uf,useDebugValue:ou,useDeferredValue:function(e){return En().memoizedState=e},useTransition:function(){var e=uf(!1),t=e[0];return e=av.bind(null,e[1]),En().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,o){var i=He,u=En();if(Be){if(o===void 0)throw Error(s(407));o=o()}else{if(o=t(),rt===null)throw Error(s(349));(Yr&30)!==0||rf(i,t,o)}u.memoizedState=o;var d={value:o,getSnapshot:t};return u.queue=d,df(sf.bind(null,i,d,e),[e]),i.flags|=2048,Is(9,of.bind(null,i,d,o,t),void 0,null),o},useId:function(){var e=En(),t=rt.identifierPrefix;if(Be){var o=Bn,i=$n;o=(i&~(1<<32-en(i)-1)).toString(32)+o,t=":"+t+"R"+o,o=Ms++,0<o&&(t+="H"+o.toString(32)),t+=":"}else o=lv++,t=":"+t+"r"+o.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},fv={readContext:Vt,useCallback:gf,useContext:Vt,useEffect:ru,useImperativeHandle:mf,useInsertionEffect:ff,useLayoutEffect:pf,useMemo:vf,useReducer:tu,useRef:cf,useState:function(){return tu(Ds)},useDebugValue:ou,useDeferredValue:function(e){var t=Ht();return yf(t,et.memoizedState,e)},useTransition:function(){var e=tu(Ds)[0],t=Ht().memoizedState;return[e,t]},useMutableSource:tf,useSyncExternalStore:nf,useId:xf,unstable_isNewReconciler:!1},pv={readContext:Vt,useCallback:gf,useContext:Vt,useEffect:ru,useImperativeHandle:mf,useInsertionEffect:ff,useLayoutEffect:pf,useMemo:vf,useReducer:nu,useRef:cf,useState:function(){return nu(Ds)},useDebugValue:ou,useDeferredValue:function(e){var t=Ht();return et===null?t.memoizedState=e:yf(t,et.memoizedState,e)},useTransition:function(){var e=nu(Ds)[0],t=Ht().memoizedState;return[e,t]},useMutableSource:tf,useSyncExternalStore:nf,useId:xf,unstable_isNewReconciler:!1};function rn(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var o in e)t[o]===void 0&&(t[o]=e[o]);return t}return t}function su(e,t,o,i){t=e.memoizedState,o=o(i,t),o=o==null?t:K({},t,o),e.memoizedState=o,e.lanes===0&&(e.updateQueue.baseState=o)}var Ji={isMounted:function(e){return(e=e._reactInternals)?Vr(e)===e:!1},enqueueSetState:function(e,t,o){e=e._reactInternals;var i=yt(),u=br(e),d=Hn(i,u);d.payload=t,o!=null&&(d.callback=o),t=vr(e,d,u),t!==null&&(ln(t,e,u,i),Wi(t,e,u))},enqueueReplaceState:function(e,t,o){e=e._reactInternals;var i=yt(),u=br(e),d=Hn(i,u);d.tag=1,d.payload=t,o!=null&&(d.callback=o),t=vr(e,d,u),t!==null&&(ln(t,e,u,i),Wi(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var o=yt(),i=br(e),u=Hn(o,i);u.tag=2,t!=null&&(u.callback=t),t=vr(e,u,i),t!==null&&(ln(t,e,i,o),Wi(t,e,i))}};function kf(e,t,o,i,u,d,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,d,m):t.prototype&&t.prototype.isPureReactComponent?!js(o,i)||!js(u,d):!0}function Nf(e,t,o){var i=!1,u=hr,d=t.contextType;return typeof d=="object"&&d!==null?d=Vt(d):(u=Nt(t)?Wr:ut.current,i=t.contextTypes,d=(i=i!=null)?Eo(e,u):hr),t=new t(o,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ji,e.stateNode=t,t._reactInternals=e,i&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=d),t}function Sf(e,t,o,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(o,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(o,i),t.state!==e&&Ji.enqueueReplaceState(t,t.state,null)}function iu(e,t,o,i){var u=e.stateNode;u.props=o,u.state=e.memoizedState,u.refs={},Qa(e);var d=t.contextType;typeof d=="object"&&d!==null?u.context=Vt(d):(d=Nt(t)?Wr:ut.current,u.context=Eo(e,d)),u.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(su(e,t,d,o),u.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(t=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),t!==u.state&&Ji.enqueueReplaceState(u,u.state,null),Qi(e,o,u,i),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function Do(e,t){try{var o="",i=t;do o+=he(i),i=i.return;while(i);var u=o}catch(d){u=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:u,digest:null}}function lu(e,t,o){return{value:e,source:null,stack:o??null,digest:t??null}}function au(e,t){try{console.error(t.value)}catch(o){setTimeout(function(){throw o})}}var hv=typeof WeakMap=="function"?WeakMap:Map;function Cf(e,t,o){o=Hn(-1,o),o.tag=3,o.payload={element:null};var i=t.value;return o.callback=function(){il||(il=!0,ku=i),au(e,t)},o}function Ef(e,t,o){o=Hn(-1,o),o.tag=3;var i=e.type.getDerivedStateFromError;if(typeof i=="function"){var u=t.value;o.payload=function(){return i(u)},o.callback=function(){au(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(o.callback=function(){au(e,t),typeof i!="function"&&(xr===null?xr=new Set([this]):xr.add(this));var m=t.stack;this.componentDidCatch(t.value,{componentStack:m!==null?m:""})}),o}function Pf(e,t,o){var i=e.pingCache;if(i===null){i=e.pingCache=new hv;var u=new Set;i.set(t,u)}else u=i.get(t),u===void 0&&(u=new Set,i.set(t,u));u.has(o)||(u.add(o),e=Pv.bind(null,e,t,o),t.then(e,e))}function Tf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Rf(e,t,o,i,u){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,o.flags|=131072,o.flags&=-52805,o.tag===1&&(o.alternate===null?o.tag=17:(t=Hn(-1,1),t.tag=2,vr(o,t,1))),o.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var mv=z.ReactCurrentOwner,St=!1;function vt(e,t,o,i){t.child=e===null?Gd(t,null,o,i):_o(t,e.child,o,i)}function _f(e,t,o,i,u){o=o.render;var d=t.ref;return Oo(t,u),i=Ja(e,t,o,i,d,u),o=eu(),e!==null&&!St?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,Wn(e,t,u)):(Be&&o&&Ia(t),t.flags|=1,vt(e,t,i,u),t.child)}function Af(e,t,o,i,u){if(e===null){var d=o.type;return typeof d=="function"&&!Ru(d)&&d.defaultProps===void 0&&o.compare===null&&o.defaultProps===void 0?(t.tag=15,t.type=d,Of(e,t,d,i,u)):(e=fl(o.type,null,i,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&u)===0){var m=d.memoizedProps;if(o=o.compare,o=o!==null?o:js,o(m,i)&&e.ref===t.ref)return Wn(e,t,u)}return t.flags|=1,e=kr(d,i),e.ref=t.ref,e.return=t,t.child=e}function Of(e,t,o,i,u){if(e!==null){var d=e.memoizedProps;if(js(d,i)&&e.ref===t.ref)if(St=!1,t.pendingProps=i=d,(e.lanes&u)!==0)(e.flags&131072)!==0&&(St=!0);else return t.lanes=e.lanes,Wn(e,t,u)}return uu(e,t,o,i,u)}function Mf(e,t,o){var i=t.pendingProps,u=i.children,d=e!==null?e.memoizedState:null;if(i.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Fe(Fo,It),It|=o;else{if((o&1073741824)===0)return e=d!==null?d.baseLanes|o:o,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Fe(Fo,It),It|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},i=d!==null?d.baseLanes:o,Fe(Fo,It),It|=i}else d!==null?(i=d.baseLanes|o,t.memoizedState=null):i=o,Fe(Fo,It),It|=i;return vt(e,t,u,o),t.child}function Df(e,t){var o=t.ref;(e===null&&o!==null||e!==null&&e.ref!==o)&&(t.flags|=512,t.flags|=2097152)}function uu(e,t,o,i,u){var d=Nt(o)?Wr:ut.current;return d=Eo(t,d),Oo(t,u),o=Ja(e,t,o,i,d,u),i=eu(),e!==null&&!St?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,Wn(e,t,u)):(Be&&i&&Ia(t),t.flags|=1,vt(e,t,o,u),t.child)}function If(e,t,o,i,u){if(Nt(o)){var d=!0;Fi(t)}else d=!1;if(Oo(t,u),t.stateNode===null)tl(e,t),Nf(t,o,i),iu(t,o,i,u),i=!0;else if(e===null){var m=t.stateNode,N=t.memoizedProps;m.props=N;var C=m.context,I=o.contextType;typeof I=="object"&&I!==null?I=Vt(I):(I=Nt(o)?Wr:ut.current,I=Eo(t,I));var W=o.getDerivedStateFromProps,Q=typeof W=="function"||typeof m.getSnapshotBeforeUpdate=="function";Q||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(N!==i||C!==I)&&Sf(t,m,i,I),gr=!1;var H=t.memoizedState;m.state=H,Qi(t,i,m,u),C=t.memoizedState,N!==i||H!==C||kt.current||gr?(typeof W=="function"&&(su(t,o,W,i),C=t.memoizedState),(N=gr||kf(t,o,N,i,H,C,I))?(Q||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(t.flags|=4194308)):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=C),m.props=i,m.state=C,m.context=I,i=N):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{m=t.stateNode,Xd(e,t),N=t.memoizedProps,I=t.type===t.elementType?N:rn(t.type,N),m.props=I,Q=t.pendingProps,H=m.context,C=o.contextType,typeof C=="object"&&C!==null?C=Vt(C):(C=Nt(o)?Wr:ut.current,C=Eo(t,C));var ne=o.getDerivedStateFromProps;(W=typeof ne=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(N!==Q||H!==C)&&Sf(t,m,i,C),gr=!1,H=t.memoizedState,m.state=H,Qi(t,i,m,u);var ie=t.memoizedState;N!==Q||H!==ie||kt.current||gr?(typeof ne=="function"&&(su(t,o,ne,i),ie=t.memoizedState),(I=gr||kf(t,o,I,i,H,ie,C)||!1)?(W||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(i,ie,C),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(i,ie,C)),typeof m.componentDidUpdate=="function"&&(t.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof m.componentDidUpdate!="function"||N===e.memoizedProps&&H===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||N===e.memoizedProps&&H===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=ie),m.props=i,m.state=ie,m.context=C,i=I):(typeof m.componentDidUpdate!="function"||N===e.memoizedProps&&H===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||N===e.memoizedProps&&H===e.memoizedState||(t.flags|=1024),i=!1)}return cu(e,t,o,i,d,u)}function cu(e,t,o,i,u,d){Df(e,t);var m=(t.flags&128)!==0;if(!i&&!m)return u&&Ud(t,o,!1),Wn(e,t,d);i=t.stateNode,mv.current=t;var N=m&&typeof o.getDerivedStateFromError!="function"?null:i.render();return t.flags|=1,e!==null&&m?(t.child=_o(t,e.child,null,d),t.child=_o(t,null,N,d)):vt(e,t,N,d),t.memoizedState=i.state,u&&Ud(t,o,!0),t.child}function Ff(e){var t=e.stateNode;t.pendingContext?Ld(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ld(e,t.context,!1),Ka(e,t.containerInfo)}function Lf(e,t,o,i,u){return Ro(),Ua(u),t.flags|=256,vt(e,t,o,i),t.child}var du={dehydrated:null,treeContext:null,retryLane:0};function fu(e){return{baseLanes:e,cachePool:null,transitions:null}}function zf(e,t,o){var i=t.pendingProps,u=Ve.current,d=!1,m=(t.flags&128)!==0,N;if((N=m)||(N=e!==null&&e.memoizedState===null?!1:(u&2)!==0),N?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),Fe(Ve,u&1),e===null)return za(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(m=i.children,e=i.fallback,d?(i=t.mode,d=t.child,m={mode:"hidden",children:m},(i&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=m):d=pl(m,i,0,null),e=to(e,i,o,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=fu(o),t.memoizedState=du,e):pu(t,m));if(u=e.memoizedState,u!==null&&(N=u.dehydrated,N!==null))return gv(e,t,m,i,N,u,o);if(d){d=i.fallback,m=t.mode,u=e.child,N=u.sibling;var C={mode:"hidden",children:i.children};return(m&1)===0&&t.child!==u?(i=t.child,i.childLanes=0,i.pendingProps=C,t.deletions=null):(i=kr(u,C),i.subtreeFlags=u.subtreeFlags&14680064),N!==null?d=kr(N,d):(d=to(d,m,o,null),d.flags|=2),d.return=t,i.return=t,i.sibling=d,t.child=i,i=d,d=t.child,m=e.child.memoizedState,m=m===null?fu(o):{baseLanes:m.baseLanes|o,cachePool:null,transitions:m.transitions},d.memoizedState=m,d.childLanes=e.childLanes&~o,t.memoizedState=du,i}return d=e.child,e=d.sibling,i=kr(d,{mode:"visible",children:i.children}),(t.mode&1)===0&&(i.lanes=o),i.return=t,i.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=i,t.memoizedState=null,i}function pu(e,t){return t=pl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function el(e,t,o,i){return i!==null&&Ua(i),_o(t,e.child,null,o),e=pu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function gv(e,t,o,i,u,d,m){if(o)return t.flags&256?(t.flags&=-257,i=lu(Error(s(422))),el(e,t,m,i)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=i.fallback,u=t.mode,i=pl({mode:"visible",children:i.children},u,0,null),d=to(d,u,m,null),d.flags|=2,i.return=t,d.return=t,i.sibling=d,t.child=i,(t.mode&1)!==0&&_o(t,e.child,null,m),t.child.memoizedState=fu(m),t.memoizedState=du,d);if((t.mode&1)===0)return el(e,t,m,null);if(u.data==="$!"){if(i=u.nextSibling&&u.nextSibling.dataset,i)var N=i.dgst;return i=N,d=Error(s(419)),i=lu(d,i,void 0),el(e,t,m,i)}if(N=(m&e.childLanes)!==0,St||N){if(i=rt,i!==null){switch(m&-m){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(i.suspendedLanes|m))!==0?0:u,u!==0&&u!==d.retryLane&&(d.retryLane=u,Vn(e,u),ln(i,e,u,-1))}return Tu(),i=lu(Error(s(421))),el(e,t,m,i)}return u.data==="$?"?(t.flags|=128,t.child=e.child,t=Tv.bind(null,e),u._reactRetry=t,null):(e=d.treeContext,Dt=fr(u.nextSibling),Mt=t,Be=!0,nn=null,e!==null&&($t[Bt++]=$n,$t[Bt++]=Bn,$t[Bt++]=Qr,$n=e.id,Bn=e.overflow,Qr=t),t=pu(t,i.children),t.flags|=4096,t)}function Uf(e,t,o){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Ha(e.return,t,o)}function hu(e,t,o,i,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:o,tailMode:u}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=i,d.tail=o,d.tailMode=u)}function $f(e,t,o){var i=t.pendingProps,u=i.revealOrder,d=i.tail;if(vt(e,t,i.children,o),i=Ve.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Uf(e,o,t);else if(e.tag===19)Uf(e,o,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}if(Fe(Ve,i),(t.mode&1)===0)t.memoizedState=null;else switch(u){case"forwards":for(o=t.child,u=null;o!==null;)e=o.alternate,e!==null&&Ki(e)===null&&(u=o),o=o.sibling;o=u,o===null?(u=t.child,t.child=null):(u=o.sibling,o.sibling=null),hu(t,!1,u,o,d);break;case"backwards":for(o=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Ki(e)===null){t.child=u;break}e=u.sibling,u.sibling=o,o=u,u=e}hu(t,!0,o,null,d);break;case"together":hu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function tl(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Wn(e,t,o){if(e!==null&&(t.dependencies=e.dependencies),Xr|=t.lanes,(o&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,o=kr(e,e.pendingProps),t.child=o,o.return=t;e.sibling!==null;)e=e.sibling,o=o.sibling=kr(e,e.pendingProps),o.return=t;o.sibling=null}return t.child}function vv(e,t,o){switch(t.tag){case 3:Ff(t),Ro();break;case 5:ef(t);break;case 1:Nt(t.type)&&Fi(t);break;case 4:Ka(t,t.stateNode.containerInfo);break;case 10:var i=t.type._context,u=t.memoizedProps.value;Fe(Vi,i._currentValue),i._currentValue=u;break;case 13:if(i=t.memoizedState,i!==null)return i.dehydrated!==null?(Fe(Ve,Ve.current&1),t.flags|=128,null):(o&t.child.childLanes)!==0?zf(e,t,o):(Fe(Ve,Ve.current&1),e=Wn(e,t,o),e!==null?e.sibling:null);Fe(Ve,Ve.current&1);break;case 19:if(i=(o&t.childLanes)!==0,(e.flags&128)!==0){if(i)return $f(e,t,o);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Fe(Ve,Ve.current),i)break;return null;case 22:case 23:return t.lanes=0,Mf(e,t,o)}return Wn(e,t,o)}var Bf,mu,Vf,Hf;Bf=function(e,t){for(var o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break;for(;o.sibling===null;){if(o.return===null||o.return===t)return;o=o.return}o.sibling.return=o.return,o=o.sibling}},mu=function(){},Vf=function(e,t,o,i){var u=e.memoizedProps;if(u!==i){e=t.stateNode,Gr(Cn.current);var d=null;switch(o){case"input":u=nr(e,u),i=nr(e,i),d=[];break;case"select":u=K({},u,{value:void 0}),i=K({},i,{value:void 0}),d=[];break;case"textarea":u=po(e,u),i=po(e,i),d=[];break;default:typeof u.onClick!="function"&&typeof i.onClick=="function"&&(e.onclick=Mi)}bn(o,i);var m;o=null;for(I in u)if(!i.hasOwnProperty(I)&&u.hasOwnProperty(I)&&u[I]!=null)if(I==="style"){var N=u[I];for(m in N)N.hasOwnProperty(m)&&(o||(o={}),o[m]="")}else I!=="dangerouslySetInnerHTML"&&I!=="children"&&I!=="suppressContentEditableWarning"&&I!=="suppressHydrationWarning"&&I!=="autoFocus"&&(c.hasOwnProperty(I)?d||(d=[]):(d=d||[]).push(I,null));for(I in i){var C=i[I];if(N=u!=null?u[I]:void 0,i.hasOwnProperty(I)&&C!==N&&(C!=null||N!=null))if(I==="style")if(N){for(m in N)!N.hasOwnProperty(m)||C&&C.hasOwnProperty(m)||(o||(o={}),o[m]="");for(m in C)C.hasOwnProperty(m)&&N[m]!==C[m]&&(o||(o={}),o[m]=C[m])}else o||(d||(d=[]),d.push(I,o)),o=C;else I==="dangerouslySetInnerHTML"?(C=C?C.__html:void 0,N=N?N.__html:void 0,C!=null&&N!==C&&(d=d||[]).push(I,C)):I==="children"?typeof C!="string"&&typeof C!="number"||(d=d||[]).push(I,""+C):I!=="suppressContentEditableWarning"&&I!=="suppressHydrationWarning"&&(c.hasOwnProperty(I)?(C!=null&&I==="onScroll"&&Le("scroll",e),d||N===C||(d=[])):(d=d||[]).push(I,C))}o&&(d=d||[]).push("style",o);var I=d;(t.updateQueue=I)&&(t.flags|=4)}},Hf=function(e,t,o,i){o!==i&&(t.flags|=4)};function Fs(e,t){if(!Be)switch(e.tailMode){case"hidden":t=e.tail;for(var o=null;t!==null;)t.alternate!==null&&(o=t),t=t.sibling;o===null?e.tail=null:o.sibling=null;break;case"collapsed":o=e.tail;for(var i=null;o!==null;)o.alternate!==null&&(i=o),o=o.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function dt(e){var t=e.alternate!==null&&e.alternate.child===e.child,o=0,i=0;if(t)for(var u=e.child;u!==null;)o|=u.lanes|u.childLanes,i|=u.subtreeFlags&14680064,i|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)o|=u.lanes|u.childLanes,i|=u.subtreeFlags,i|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=i,e.childLanes=o,t}function yv(e,t,o){var i=t.pendingProps;switch(Fa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return dt(t),null;case 1:return Nt(t.type)&&Ii(),dt(t),null;case 3:return i=t.stateNode,Mo(),ze(kt),ze(ut),Ya(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(e===null||e.child===null)&&($i(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,nn!==null&&(Cu(nn),nn=null))),mu(e,t),dt(t),null;case 5:qa(t);var u=Gr(As.current);if(o=t.type,e!==null&&t.stateNode!=null)Vf(e,t,o,i,u),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!i){if(t.stateNode===null)throw Error(s(166));return dt(t),null}if(e=Gr(Cn.current),$i(t)){i=t.stateNode,o=t.type;var d=t.memoizedProps;switch(i[Sn]=t,i[Es]=d,e=(t.mode&1)!==0,o){case"dialog":Le("cancel",i),Le("close",i);break;case"iframe":case"object":case"embed":Le("load",i);break;case"video":case"audio":for(u=0;u<Ns.length;u++)Le(Ns[u],i);break;case"source":Le("error",i);break;case"img":case"image":case"link":Le("error",i),Le("load",i);break;case"details":Le("toggle",i);break;case"input":fo(i,d),Le("invalid",i);break;case"select":i._wrapperState={wasMultiple:!!d.multiple},Le("invalid",i);break;case"textarea":yn(i,d),Le("invalid",i)}bn(o,d),u=null;for(var m in d)if(d.hasOwnProperty(m)){var N=d[m];m==="children"?typeof N=="string"?i.textContent!==N&&(d.suppressHydrationWarning!==!0&&Oi(i.textContent,N,e),u=["children",N]):typeof N=="number"&&i.textContent!==""+N&&(d.suppressHydrationWarning!==!0&&Oi(i.textContent,N,e),u=["children",""+N]):c.hasOwnProperty(m)&&N!=null&&m==="onScroll"&&Le("scroll",i)}switch(o){case"input":_t(i),rr(i,d,!0);break;case"textarea":_t(i),ai(i);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(i.onclick=Mi)}i=u,t.updateQueue=i,i!==null&&(t.flags|=4)}else{m=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=mt(o)),e==="http://www.w3.org/1999/xhtml"?o==="script"?(e=m.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof i.is=="string"?e=m.createElement(o,{is:i.is}):(e=m.createElement(o),o==="select"&&(m=e,i.multiple?m.multiple=!0:i.size&&(m.size=i.size))):e=m.createElementNS(e,o),e[Sn]=t,e[Es]=i,Bf(e,t,!1,!1),t.stateNode=e;e:{switch(m=ls(o,i),o){case"dialog":Le("cancel",e),Le("close",e),u=i;break;case"iframe":case"object":case"embed":Le("load",e),u=i;break;case"video":case"audio":for(u=0;u<Ns.length;u++)Le(Ns[u],e);u=i;break;case"source":Le("error",e),u=i;break;case"img":case"image":case"link":Le("error",e),Le("load",e),u=i;break;case"details":Le("toggle",e),u=i;break;case"input":fo(e,i),u=nr(e,i),Le("invalid",e);break;case"option":u=i;break;case"select":e._wrapperState={wasMultiple:!!i.multiple},u=K({},i,{value:void 0}),Le("invalid",e);break;case"textarea":yn(e,i),u=po(e,i),Le("invalid",e);break;default:u=i}bn(o,u),N=u;for(d in N)if(N.hasOwnProperty(d)){var C=N[d];d==="style"?Ln(e,C):d==="dangerouslySetInnerHTML"?(C=C?C.__html:void 0,C!=null&&ui(e,C)):d==="children"?typeof C=="string"?(o!=="textarea"||C!=="")&&wn(e,C):typeof C=="number"&&wn(e,""+C):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(c.hasOwnProperty(d)?C!=null&&d==="onScroll"&&Le("scroll",e):C!=null&&A(e,d,C,m))}switch(o){case"input":_t(e),rr(e,i,!1);break;case"textarea":_t(e),ai(e);break;case"option":i.value!=null&&e.setAttribute("value",""+Ce(i.value));break;case"select":e.multiple=!!i.multiple,d=i.value,d!=null?Ut(e,!!i.multiple,d,!1):i.defaultValue!=null&&Ut(e,!!i.multiple,i.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=Mi)}switch(o){case"button":case"input":case"select":case"textarea":i=!!i.autoFocus;break e;case"img":i=!0;break e;default:i=!1}}i&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return dt(t),null;case 6:if(e&&t.stateNode!=null)Hf(e,t,e.memoizedProps,i);else{if(typeof i!="string"&&t.stateNode===null)throw Error(s(166));if(o=Gr(As.current),Gr(Cn.current),$i(t)){if(i=t.stateNode,o=t.memoizedProps,i[Sn]=t,(d=i.nodeValue!==o)&&(e=Mt,e!==null))switch(e.tag){case 3:Oi(i.nodeValue,o,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Oi(i.nodeValue,o,(e.mode&1)!==0)}d&&(t.flags|=4)}else i=(o.nodeType===9?o:o.ownerDocument).createTextNode(i),i[Sn]=t,t.stateNode=i}return dt(t),null;case 13:if(ze(Ve),i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Be&&Dt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Qd(),Ro(),t.flags|=98560,d=!1;else if(d=$i(t),i!==null&&i.dehydrated!==null){if(e===null){if(!d)throw Error(s(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(s(317));d[Sn]=t}else Ro(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;dt(t),d=!1}else nn!==null&&(Cu(nn),nn=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=o,t):(i=i!==null,i!==(e!==null&&e.memoizedState!==null)&&i&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Ve.current&1)!==0?tt===0&&(tt=3):Tu())),t.updateQueue!==null&&(t.flags|=4),dt(t),null);case 4:return Mo(),mu(e,t),e===null&&Ss(t.stateNode.containerInfo),dt(t),null;case 10:return Va(t.type._context),dt(t),null;case 17:return Nt(t.type)&&Ii(),dt(t),null;case 19:if(ze(Ve),d=t.memoizedState,d===null)return dt(t),null;if(i=(t.flags&128)!==0,m=d.rendering,m===null)if(i)Fs(d,!1);else{if(tt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(m=Ki(e),m!==null){for(t.flags|=128,Fs(d,!1),i=m.updateQueue,i!==null&&(t.updateQueue=i,t.flags|=4),t.subtreeFlags=0,i=o,o=t.child;o!==null;)d=o,e=i,d.flags&=14680066,m=d.alternate,m===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=m.childLanes,d.lanes=m.lanes,d.child=m.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=m.memoizedProps,d.memoizedState=m.memoizedState,d.updateQueue=m.updateQueue,d.type=m.type,e=m.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),o=o.sibling;return Fe(Ve,Ve.current&1|2),t.child}e=e.sibling}d.tail!==null&&Ye()>Lo&&(t.flags|=128,i=!0,Fs(d,!1),t.lanes=4194304)}else{if(!i)if(e=Ki(m),e!==null){if(t.flags|=128,i=!0,o=e.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),Fs(d,!0),d.tail===null&&d.tailMode==="hidden"&&!m.alternate&&!Be)return dt(t),null}else 2*Ye()-d.renderingStartTime>Lo&&o!==1073741824&&(t.flags|=128,i=!0,Fs(d,!1),t.lanes=4194304);d.isBackwards?(m.sibling=t.child,t.child=m):(o=d.last,o!==null?o.sibling=m:t.child=m,d.last=m)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=Ye(),t.sibling=null,o=Ve.current,Fe(Ve,i?o&1|2:o&1),t):(dt(t),null);case 22:case 23:return Pu(),i=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==i&&(t.flags|=8192),i&&(t.mode&1)!==0?(It&1073741824)!==0&&(dt(t),t.subtreeFlags&6&&(t.flags|=8192)):dt(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function xv(e,t){switch(Fa(t),t.tag){case 1:return Nt(t.type)&&Ii(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Mo(),ze(kt),ze(ut),Ya(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return qa(t),null;case 13:if(ze(Ve),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Ro()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ze(Ve),null;case 4:return Mo(),null;case 10:return Va(t.type._context),null;case 22:case 23:return Pu(),null;case 24:return null;default:return null}}var nl=!1,ft=!1,wv=typeof WeakSet=="function"?WeakSet:Set,oe=null;function Io(e,t){var o=e.ref;if(o!==null)if(typeof o=="function")try{o(null)}catch(i){We(e,t,i)}else o.current=null}function gu(e,t,o){try{o()}catch(i){We(e,t,i)}}var Wf=!1;function bv(e,t){if(Pa=ji,e=jd(),wa(e)){if("selectionStart"in e)var o={start:e.selectionStart,end:e.selectionEnd};else e:{o=(o=e.ownerDocument)&&o.defaultView||window;var i=o.getSelection&&o.getSelection();if(i&&i.rangeCount!==0){o=i.anchorNode;var u=i.anchorOffset,d=i.focusNode;i=i.focusOffset;try{o.nodeType,d.nodeType}catch{o=null;break e}var m=0,N=-1,C=-1,I=0,W=0,Q=e,H=null;t:for(;;){for(var ne;Q!==o||u!==0&&Q.nodeType!==3||(N=m+u),Q!==d||i!==0&&Q.nodeType!==3||(C=m+i),Q.nodeType===3&&(m+=Q.nodeValue.length),(ne=Q.firstChild)!==null;)H=Q,Q=ne;for(;;){if(Q===e)break t;if(H===o&&++I===u&&(N=m),H===d&&++W===i&&(C=m),(ne=Q.nextSibling)!==null)break;Q=H,H=Q.parentNode}Q=ne}o=N===-1||C===-1?null:{start:N,end:C}}else o=null}o=o||{start:0,end:0}}else o=null;for(Ta={focusedElem:e,selectionRange:o},ji=!1,oe=t;oe!==null;)if(t=oe,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,oe=e;else for(;oe!==null;){t=oe;try{var ie=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ie!==null){var le=ie.memoizedProps,Xe=ie.memoizedState,M=t.stateNode,T=M.getSnapshotBeforeUpdate(t.elementType===t.type?le:rn(t.type,le),Xe);M.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var D=t.stateNode.containerInfo;D.nodeType===1?D.textContent="":D.nodeType===9&&D.documentElement&&D.removeChild(D.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(q){We(t,t.return,q)}if(e=t.sibling,e!==null){e.return=t.return,oe=e;break}oe=t.return}return ie=Wf,Wf=!1,ie}function Ls(e,t,o){var i=t.updateQueue;if(i=i!==null?i.lastEffect:null,i!==null){var u=i=i.next;do{if((u.tag&e)===e){var d=u.destroy;u.destroy=void 0,d!==void 0&&gu(t,o,d)}u=u.next}while(u!==i)}}function rl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var o=t=t.next;do{if((o.tag&e)===e){var i=o.create;o.destroy=i()}o=o.next}while(o!==t)}}function vu(e){var t=e.ref;if(t!==null){var o=e.stateNode;switch(e.tag){case 5:e=o;break;default:e=o}typeof t=="function"?t(e):t.current=e}}function Qf(e){var t=e.alternate;t!==null&&(e.alternate=null,Qf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Sn],delete t[Es],delete t[Oa],delete t[rv],delete t[ov])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Kf(e){return e.tag===5||e.tag===3||e.tag===4}function qf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function yu(e,t,o){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?o.nodeType===8?o.parentNode.insertBefore(e,t):o.insertBefore(e,t):(o.nodeType===8?(t=o.parentNode,t.insertBefore(e,o)):(t=o,t.appendChild(e)),o=o._reactRootContainer,o!=null||t.onclick!==null||(t.onclick=Mi));else if(i!==4&&(e=e.child,e!==null))for(yu(e,t,o),e=e.sibling;e!==null;)yu(e,t,o),e=e.sibling}function xu(e,t,o){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?o.insertBefore(e,t):o.appendChild(e);else if(i!==4&&(e=e.child,e!==null))for(xu(e,t,o),e=e.sibling;e!==null;)xu(e,t,o),e=e.sibling}var it=null,on=!1;function yr(e,t,o){for(o=o.child;o!==null;)Gf(e,t,o),o=o.sibling}function Gf(e,t,o){if(Nn&&typeof Nn.onCommitFiberUnmount=="function")try{Nn.onCommitFiberUnmount(gi,o)}catch{}switch(o.tag){case 5:ft||Io(o,t);case 6:var i=it,u=on;it=null,yr(e,t,o),it=i,on=u,it!==null&&(on?(e=it,o=o.stateNode,e.nodeType===8?e.parentNode.removeChild(o):e.removeChild(o)):it.removeChild(o.stateNode));break;case 18:it!==null&&(on?(e=it,o=o.stateNode,e.nodeType===8?Aa(e.parentNode,o):e.nodeType===1&&Aa(e,o),gs(e)):Aa(it,o.stateNode));break;case 4:i=it,u=on,it=o.stateNode.containerInfo,on=!0,yr(e,t,o),it=i,on=u;break;case 0:case 11:case 14:case 15:if(!ft&&(i=o.updateQueue,i!==null&&(i=i.lastEffect,i!==null))){u=i=i.next;do{var d=u,m=d.destroy;d=d.tag,m!==void 0&&((d&2)!==0||(d&4)!==0)&&gu(o,t,m),u=u.next}while(u!==i)}yr(e,t,o);break;case 1:if(!ft&&(Io(o,t),i=o.stateNode,typeof i.componentWillUnmount=="function"))try{i.props=o.memoizedProps,i.state=o.memoizedState,i.componentWillUnmount()}catch(N){We(o,t,N)}yr(e,t,o);break;case 21:yr(e,t,o);break;case 22:o.mode&1?(ft=(i=ft)||o.memoizedState!==null,yr(e,t,o),ft=i):yr(e,t,o);break;default:yr(e,t,o)}}function Yf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var o=e.stateNode;o===null&&(o=e.stateNode=new wv),t.forEach(function(i){var u=Rv.bind(null,e,i);o.has(i)||(o.add(i),i.then(u,u))})}}function sn(e,t){var o=t.deletions;if(o!==null)for(var i=0;i<o.length;i++){var u=o[i];try{var d=e,m=t,N=m;e:for(;N!==null;){switch(N.tag){case 5:it=N.stateNode,on=!1;break e;case 3:it=N.stateNode.containerInfo,on=!0;break e;case 4:it=N.stateNode.containerInfo,on=!0;break e}N=N.return}if(it===null)throw Error(s(160));Gf(d,m,u),it=null,on=!1;var C=u.alternate;C!==null&&(C.return=null),u.return=null}catch(I){We(u,t,I)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Xf(t,e),t=t.sibling}function Xf(e,t){var o=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(sn(t,e),Pn(e),i&4){try{Ls(3,e,e.return),rl(3,e)}catch(le){We(e,e.return,le)}try{Ls(5,e,e.return)}catch(le){We(e,e.return,le)}}break;case 1:sn(t,e),Pn(e),i&512&&o!==null&&Io(o,o.return);break;case 5:if(sn(t,e),Pn(e),i&512&&o!==null&&Io(o,o.return),e.flags&32){var u=e.stateNode;try{wn(u,"")}catch(le){We(e,e.return,le)}}if(i&4&&(u=e.stateNode,u!=null)){var d=e.memoizedProps,m=o!==null?o.memoizedProps:d,N=e.type,C=e.updateQueue;if(e.updateQueue=null,C!==null)try{N==="input"&&d.type==="radio"&&d.name!=null&&Ur(u,d),ls(N,m);var I=ls(N,d);for(m=0;m<C.length;m+=2){var W=C[m],Q=C[m+1];W==="style"?Ln(u,Q):W==="dangerouslySetInnerHTML"?ui(u,Q):W==="children"?wn(u,Q):A(u,W,Q,I)}switch(N){case"input":$r(u,d);break;case"textarea":li(u,d);break;case"select":var H=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!d.multiple;var ne=d.value;ne!=null?Ut(u,!!d.multiple,ne,!1):H!==!!d.multiple&&(d.defaultValue!=null?Ut(u,!!d.multiple,d.defaultValue,!0):Ut(u,!!d.multiple,d.multiple?[]:"",!1))}u[Es]=d}catch(le){We(e,e.return,le)}}break;case 6:if(sn(t,e),Pn(e),i&4){if(e.stateNode===null)throw Error(s(162));u=e.stateNode,d=e.memoizedProps;try{u.nodeValue=d}catch(le){We(e,e.return,le)}}break;case 3:if(sn(t,e),Pn(e),i&4&&o!==null&&o.memoizedState.isDehydrated)try{gs(t.containerInfo)}catch(le){We(e,e.return,le)}break;case 4:sn(t,e),Pn(e);break;case 13:sn(t,e),Pn(e),u=e.child,u.flags&8192&&(d=u.memoizedState!==null,u.stateNode.isHidden=d,!d||u.alternate!==null&&u.alternate.memoizedState!==null||(ju=Ye())),i&4&&Yf(e);break;case 22:if(W=o!==null&&o.memoizedState!==null,e.mode&1?(ft=(I=ft)||W,sn(t,e),ft=I):sn(t,e),Pn(e),i&8192){if(I=e.memoizedState!==null,(e.stateNode.isHidden=I)&&!W&&(e.mode&1)!==0)for(oe=e,W=e.child;W!==null;){for(Q=oe=W;oe!==null;){switch(H=oe,ne=H.child,H.tag){case 0:case 11:case 14:case 15:Ls(4,H,H.return);break;case 1:Io(H,H.return);var ie=H.stateNode;if(typeof ie.componentWillUnmount=="function"){i=H,o=H.return;try{t=i,ie.props=t.memoizedProps,ie.state=t.memoizedState,ie.componentWillUnmount()}catch(le){We(i,o,le)}}break;case 5:Io(H,H.return);break;case 22:if(H.memoizedState!==null){ep(Q);continue}}ne!==null?(ne.return=H,oe=ne):ep(Q)}W=W.sibling}e:for(W=null,Q=e;;){if(Q.tag===5){if(W===null){W=Q;try{u=Q.stateNode,I?(d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(N=Q.stateNode,C=Q.memoizedProps.style,m=C!=null&&C.hasOwnProperty("display")?C.display:null,N.style.display=mo("display",m))}catch(le){We(e,e.return,le)}}}else if(Q.tag===6){if(W===null)try{Q.stateNode.nodeValue=I?"":Q.memoizedProps}catch(le){We(e,e.return,le)}}else if((Q.tag!==22&&Q.tag!==23||Q.memoizedState===null||Q===e)&&Q.child!==null){Q.child.return=Q,Q=Q.child;continue}if(Q===e)break e;for(;Q.sibling===null;){if(Q.return===null||Q.return===e)break e;W===Q&&(W=null),Q=Q.return}W===Q&&(W=null),Q.sibling.return=Q.return,Q=Q.sibling}}break;case 19:sn(t,e),Pn(e),i&4&&Yf(e);break;case 21:break;default:sn(t,e),Pn(e)}}function Pn(e){var t=e.flags;if(t&2){try{e:{for(var o=e.return;o!==null;){if(Kf(o)){var i=o;break e}o=o.return}throw Error(s(160))}switch(i.tag){case 5:var u=i.stateNode;i.flags&32&&(wn(u,""),i.flags&=-33);var d=qf(e);xu(e,d,u);break;case 3:case 4:var m=i.stateNode.containerInfo,N=qf(e);yu(e,N,m);break;default:throw Error(s(161))}}catch(C){We(e,e.return,C)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function jv(e,t,o){oe=e,Zf(e)}function Zf(e,t,o){for(var i=(e.mode&1)!==0;oe!==null;){var u=oe,d=u.child;if(u.tag===22&&i){var m=u.memoizedState!==null||nl;if(!m){var N=u.alternate,C=N!==null&&N.memoizedState!==null||ft;N=nl;var I=ft;if(nl=m,(ft=C)&&!I)for(oe=u;oe!==null;)m=oe,C=m.child,m.tag===22&&m.memoizedState!==null?tp(u):C!==null?(C.return=m,oe=C):tp(u);for(;d!==null;)oe=d,Zf(d),d=d.sibling;oe=u,nl=N,ft=I}Jf(e)}else(u.subtreeFlags&8772)!==0&&d!==null?(d.return=u,oe=d):Jf(e)}}function Jf(e){for(;oe!==null;){var t=oe;if((t.flags&8772)!==0){var o=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:ft||rl(5,t);break;case 1:var i=t.stateNode;if(t.flags&4&&!ft)if(o===null)i.componentDidMount();else{var u=t.elementType===t.type?o.memoizedProps:rn(t.type,o.memoizedProps);i.componentDidUpdate(u,o.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&Jd(t,d,i);break;case 3:var m=t.updateQueue;if(m!==null){if(o=null,t.child!==null)switch(t.child.tag){case 5:o=t.child.stateNode;break;case 1:o=t.child.stateNode}Jd(t,m,o)}break;case 5:var N=t.stateNode;if(o===null&&t.flags&4){o=N;var C=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":C.autoFocus&&o.focus();break;case"img":C.src&&(o.src=C.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var I=t.alternate;if(I!==null){var W=I.memoizedState;if(W!==null){var Q=W.dehydrated;Q!==null&&gs(Q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}ft||t.flags&512&&vu(t)}catch(H){We(t,t.return,H)}}if(t===e){oe=null;break}if(o=t.sibling,o!==null){o.return=t.return,oe=o;break}oe=t.return}}function ep(e){for(;oe!==null;){var t=oe;if(t===e){oe=null;break}var o=t.sibling;if(o!==null){o.return=t.return,oe=o;break}oe=t.return}}function tp(e){for(;oe!==null;){var t=oe;try{switch(t.tag){case 0:case 11:case 15:var o=t.return;try{rl(4,t)}catch(C){We(t,o,C)}break;case 1:var i=t.stateNode;if(typeof i.componentDidMount=="function"){var u=t.return;try{i.componentDidMount()}catch(C){We(t,u,C)}}var d=t.return;try{vu(t)}catch(C){We(t,d,C)}break;case 5:var m=t.return;try{vu(t)}catch(C){We(t,m,C)}}}catch(C){We(t,t.return,C)}if(t===e){oe=null;break}var N=t.sibling;if(N!==null){N.return=t.return,oe=N;break}oe=t.return}}var kv=Math.ceil,ol=z.ReactCurrentDispatcher,wu=z.ReactCurrentOwner,Wt=z.ReactCurrentBatchConfig,Te=0,rt=null,Ze=null,lt=0,It=0,Fo=pr(0),tt=0,zs=null,Xr=0,sl=0,bu=0,Us=null,Ct=null,ju=0,Lo=1/0,Qn=null,il=!1,ku=null,xr=null,ll=!1,wr=null,al=0,$s=0,Nu=null,ul=-1,cl=0;function yt(){return(Te&6)!==0?Ye():ul!==-1?ul:ul=Ye()}function br(e){return(e.mode&1)===0?1:(Te&2)!==0&&lt!==0?lt&-lt:iv.transition!==null?(cl===0&&(cl=Kc()),cl):(e=Me,e!==0||(e=window.event,e=e===void 0?16:nd(e.type)),e)}function ln(e,t,o,i){if(50<$s)throw $s=0,Nu=null,Error(s(185));ds(e,o,i),((Te&2)===0||e!==rt)&&(e===rt&&((Te&2)===0&&(sl|=o),tt===4&&jr(e,lt)),Et(e,i),o===1&&Te===0&&(t.mode&1)===0&&(Lo=Ye()+500,Li&&mr()))}function Et(e,t){var o=e.callbackNode;i0(e,t);var i=xi(e,e===rt?lt:0);if(i===0)o!==null&&Hc(o),e.callbackNode=null,e.callbackPriority=0;else if(t=i&-i,e.callbackPriority!==t){if(o!=null&&Hc(o),t===1)e.tag===0?sv(rp.bind(null,e)):$d(rp.bind(null,e)),tv(function(){(Te&6)===0&&mr()}),o=null;else{switch(qc(i)){case 1:o=ra;break;case 4:o=Wc;break;case 16:o=mi;break;case 536870912:o=Qc;break;default:o=mi}o=dp(o,np.bind(null,e))}e.callbackPriority=t,e.callbackNode=o}}function np(e,t){if(ul=-1,cl=0,(Te&6)!==0)throw Error(s(327));var o=e.callbackNode;if(zo()&&e.callbackNode!==o)return null;var i=xi(e,e===rt?lt:0);if(i===0)return null;if((i&30)!==0||(i&e.expiredLanes)!==0||t)t=dl(e,i);else{t=i;var u=Te;Te|=2;var d=sp();(rt!==e||lt!==t)&&(Qn=null,Lo=Ye()+500,Jr(e,t));do try{Cv();break}catch(N){op(e,N)}while(!0);Ba(),ol.current=d,Te=u,Ze!==null?t=0:(rt=null,lt=0,t=tt)}if(t!==0){if(t===2&&(u=oa(e),u!==0&&(i=u,t=Su(e,u))),t===1)throw o=zs,Jr(e,0),jr(e,i),Et(e,Ye()),o;if(t===6)jr(e,i);else{if(u=e.current.alternate,(i&30)===0&&!Nv(u)&&(t=dl(e,i),t===2&&(d=oa(e),d!==0&&(i=d,t=Su(e,d))),t===1))throw o=zs,Jr(e,0),jr(e,i),Et(e,Ye()),o;switch(e.finishedWork=u,e.finishedLanes=i,t){case 0:case 1:throw Error(s(345));case 2:eo(e,Ct,Qn);break;case 3:if(jr(e,i),(i&130023424)===i&&(t=ju+500-Ye(),10<t)){if(xi(e,0)!==0)break;if(u=e.suspendedLanes,(u&i)!==i){yt(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=_a(eo.bind(null,e,Ct,Qn),t);break}eo(e,Ct,Qn);break;case 4:if(jr(e,i),(i&4194240)===i)break;for(t=e.eventTimes,u=-1;0<i;){var m=31-en(i);d=1<<m,m=t[m],m>u&&(u=m),i&=~d}if(i=u,i=Ye()-i,i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*kv(i/1960))-i,10<i){e.timeoutHandle=_a(eo.bind(null,e,Ct,Qn),i);break}eo(e,Ct,Qn);break;case 5:eo(e,Ct,Qn);break;default:throw Error(s(329))}}}return Et(e,Ye()),e.callbackNode===o?np.bind(null,e):null}function Su(e,t){var o=Us;return e.current.memoizedState.isDehydrated&&(Jr(e,t).flags|=256),e=dl(e,t),e!==2&&(t=Ct,Ct=o,t!==null&&Cu(t)),e}function Cu(e){Ct===null?Ct=e:Ct.push.apply(Ct,e)}function Nv(e){for(var t=e;;){if(t.flags&16384){var o=t.updateQueue;if(o!==null&&(o=o.stores,o!==null))for(var i=0;i<o.length;i++){var u=o[i],d=u.getSnapshot;u=u.value;try{if(!tn(d(),u))return!1}catch{return!1}}}if(o=t.child,t.subtreeFlags&16384&&o!==null)o.return=t,t=o;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function jr(e,t){for(t&=~bu,t&=~sl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var o=31-en(t),i=1<<o;e[o]=-1,t&=~i}}function rp(e){if((Te&6)!==0)throw Error(s(327));zo();var t=xi(e,0);if((t&1)===0)return Et(e,Ye()),null;var o=dl(e,t);if(e.tag!==0&&o===2){var i=oa(e);i!==0&&(t=i,o=Su(e,i))}if(o===1)throw o=zs,Jr(e,0),jr(e,t),Et(e,Ye()),o;if(o===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,eo(e,Ct,Qn),Et(e,Ye()),null}function Eu(e,t){var o=Te;Te|=1;try{return e(t)}finally{Te=o,Te===0&&(Lo=Ye()+500,Li&&mr())}}function Zr(e){wr!==null&&wr.tag===0&&(Te&6)===0&&zo();var t=Te;Te|=1;var o=Wt.transition,i=Me;try{if(Wt.transition=null,Me=1,e)return e()}finally{Me=i,Wt.transition=o,Te=t,(Te&6)===0&&mr()}}function Pu(){It=Fo.current,ze(Fo)}function Jr(e,t){e.finishedWork=null,e.finishedLanes=0;var o=e.timeoutHandle;if(o!==-1&&(e.timeoutHandle=-1,ev(o)),Ze!==null)for(o=Ze.return;o!==null;){var i=o;switch(Fa(i),i.tag){case 1:i=i.type.childContextTypes,i!=null&&Ii();break;case 3:Mo(),ze(kt),ze(ut),Ya();break;case 5:qa(i);break;case 4:Mo();break;case 13:ze(Ve);break;case 19:ze(Ve);break;case 10:Va(i.type._context);break;case 22:case 23:Pu()}o=o.return}if(rt=e,Ze=e=kr(e.current,null),lt=It=t,tt=0,zs=null,bu=sl=Xr=0,Ct=Us=null,qr!==null){for(t=0;t<qr.length;t++)if(o=qr[t],i=o.interleaved,i!==null){o.interleaved=null;var u=i.next,d=o.pending;if(d!==null){var m=d.next;d.next=u,i.next=m}o.pending=i}qr=null}return e}function op(e,t){do{var o=Ze;try{if(Ba(),qi.current=Zi,Gi){for(var i=He.memoizedState;i!==null;){var u=i.queue;u!==null&&(u.pending=null),i=i.next}Gi=!1}if(Yr=0,nt=et=He=null,Os=!1,Ms=0,wu.current=null,o===null||o.return===null){tt=1,zs=t,Ze=null;break}e:{var d=e,m=o.return,N=o,C=t;if(t=lt,N.flags|=32768,C!==null&&typeof C=="object"&&typeof C.then=="function"){var I=C,W=N,Q=W.tag;if((W.mode&1)===0&&(Q===0||Q===11||Q===15)){var H=W.alternate;H?(W.updateQueue=H.updateQueue,W.memoizedState=H.memoizedState,W.lanes=H.lanes):(W.updateQueue=null,W.memoizedState=null)}var ne=Tf(m);if(ne!==null){ne.flags&=-257,Rf(ne,m,N,d,t),ne.mode&1&&Pf(d,I,t),t=ne,C=I;var ie=t.updateQueue;if(ie===null){var le=new Set;le.add(C),t.updateQueue=le}else ie.add(C);break e}else{if((t&1)===0){Pf(d,I,t),Tu();break e}C=Error(s(426))}}else if(Be&&N.mode&1){var Xe=Tf(m);if(Xe!==null){(Xe.flags&65536)===0&&(Xe.flags|=256),Rf(Xe,m,N,d,t),Ua(Do(C,N));break e}}d=C=Do(C,N),tt!==4&&(tt=2),Us===null?Us=[d]:Us.push(d),d=m;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var M=Cf(d,C,t);Zd(d,M);break e;case 1:N=C;var T=d.type,D=d.stateNode;if((d.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||D!==null&&typeof D.componentDidCatch=="function"&&(xr===null||!xr.has(D)))){d.flags|=65536,t&=-t,d.lanes|=t;var q=Ef(d,N,t);Zd(d,q);break e}}d=d.return}while(d!==null)}lp(o)}catch(ue){t=ue,Ze===o&&o!==null&&(Ze=o=o.return);continue}break}while(!0)}function sp(){var e=ol.current;return ol.current=Zi,e===null?Zi:e}function Tu(){(tt===0||tt===3||tt===2)&&(tt=4),rt===null||(Xr&268435455)===0&&(sl&268435455)===0||jr(rt,lt)}function dl(e,t){var o=Te;Te|=2;var i=sp();(rt!==e||lt!==t)&&(Qn=null,Jr(e,t));do try{Sv();break}catch(u){op(e,u)}while(!0);if(Ba(),Te=o,ol.current=i,Ze!==null)throw Error(s(261));return rt=null,lt=0,tt}function Sv(){for(;Ze!==null;)ip(Ze)}function Cv(){for(;Ze!==null&&!Xg();)ip(Ze)}function ip(e){var t=cp(e.alternate,e,It);e.memoizedProps=e.pendingProps,t===null?lp(e):Ze=t,wu.current=null}function lp(e){var t=e;do{var o=t.alternate;if(e=t.return,(t.flags&32768)===0){if(o=yv(o,t,It),o!==null){Ze=o;return}}else{if(o=xv(o,t),o!==null){o.flags&=32767,Ze=o;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{tt=6,Ze=null;return}}if(t=t.sibling,t!==null){Ze=t;return}Ze=t=e}while(t!==null);tt===0&&(tt=5)}function eo(e,t,o){var i=Me,u=Wt.transition;try{Wt.transition=null,Me=1,Ev(e,t,o,i)}finally{Wt.transition=u,Me=i}return null}function Ev(e,t,o,i){do zo();while(wr!==null);if((Te&6)!==0)throw Error(s(327));o=e.finishedWork;var u=e.finishedLanes;if(o===null)return null;if(e.finishedWork=null,e.finishedLanes=0,o===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var d=o.lanes|o.childLanes;if(l0(e,d),e===rt&&(Ze=rt=null,lt=0),(o.subtreeFlags&2064)===0&&(o.flags&2064)===0||ll||(ll=!0,dp(mi,function(){return zo(),null})),d=(o.flags&15990)!==0,(o.subtreeFlags&15990)!==0||d){d=Wt.transition,Wt.transition=null;var m=Me;Me=1;var N=Te;Te|=4,wu.current=null,bv(e,o),Xf(o,e),K0(Ta),ji=!!Pa,Ta=Pa=null,e.current=o,jv(o),Zg(),Te=N,Me=m,Wt.transition=d}else e.current=o;if(ll&&(ll=!1,wr=e,al=u),d=e.pendingLanes,d===0&&(xr=null),t0(o.stateNode),Et(e,Ye()),t!==null)for(i=e.onRecoverableError,o=0;o<t.length;o++)u=t[o],i(u.value,{componentStack:u.stack,digest:u.digest});if(il)throw il=!1,e=ku,ku=null,e;return(al&1)!==0&&e.tag!==0&&zo(),d=e.pendingLanes,(d&1)!==0?e===Nu?$s++:($s=0,Nu=e):$s=0,mr(),null}function zo(){if(wr!==null){var e=qc(al),t=Wt.transition,o=Me;try{if(Wt.transition=null,Me=16>e?16:e,wr===null)var i=!1;else{if(e=wr,wr=null,al=0,(Te&6)!==0)throw Error(s(331));var u=Te;for(Te|=4,oe=e.current;oe!==null;){var d=oe,m=d.child;if((oe.flags&16)!==0){var N=d.deletions;if(N!==null){for(var C=0;C<N.length;C++){var I=N[C];for(oe=I;oe!==null;){var W=oe;switch(W.tag){case 0:case 11:case 15:Ls(8,W,d)}var Q=W.child;if(Q!==null)Q.return=W,oe=Q;else for(;oe!==null;){W=oe;var H=W.sibling,ne=W.return;if(Qf(W),W===I){oe=null;break}if(H!==null){H.return=ne,oe=H;break}oe=ne}}}var ie=d.alternate;if(ie!==null){var le=ie.child;if(le!==null){ie.child=null;do{var Xe=le.sibling;le.sibling=null,le=Xe}while(le!==null)}}oe=d}}if((d.subtreeFlags&2064)!==0&&m!==null)m.return=d,oe=m;else e:for(;oe!==null;){if(d=oe,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:Ls(9,d,d.return)}var M=d.sibling;if(M!==null){M.return=d.return,oe=M;break e}oe=d.return}}var T=e.current;for(oe=T;oe!==null;){m=oe;var D=m.child;if((m.subtreeFlags&2064)!==0&&D!==null)D.return=m,oe=D;else e:for(m=T;oe!==null;){if(N=oe,(N.flags&2048)!==0)try{switch(N.tag){case 0:case 11:case 15:rl(9,N)}}catch(ue){We(N,N.return,ue)}if(N===m){oe=null;break e}var q=N.sibling;if(q!==null){q.return=N.return,oe=q;break e}oe=N.return}}if(Te=u,mr(),Nn&&typeof Nn.onPostCommitFiberRoot=="function")try{Nn.onPostCommitFiberRoot(gi,e)}catch{}i=!0}return i}finally{Me=o,Wt.transition=t}}return!1}function ap(e,t,o){t=Do(o,t),t=Cf(e,t,1),e=vr(e,t,1),t=yt(),e!==null&&(ds(e,1,t),Et(e,t))}function We(e,t,o){if(e.tag===3)ap(e,e,o);else for(;t!==null;){if(t.tag===3){ap(t,e,o);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(xr===null||!xr.has(i))){e=Do(o,e),e=Ef(t,e,1),t=vr(t,e,1),e=yt(),t!==null&&(ds(t,1,e),Et(t,e));break}}t=t.return}}function Pv(e,t,o){var i=e.pingCache;i!==null&&i.delete(t),t=yt(),e.pingedLanes|=e.suspendedLanes&o,rt===e&&(lt&o)===o&&(tt===4||tt===3&&(lt&130023424)===lt&&500>Ye()-ju?Jr(e,0):bu|=o),Et(e,t)}function up(e,t){t===0&&((e.mode&1)===0?t=1:(t=yi,yi<<=1,(yi&130023424)===0&&(yi=4194304)));var o=yt();e=Vn(e,t),e!==null&&(ds(e,t,o),Et(e,o))}function Tv(e){var t=e.memoizedState,o=0;t!==null&&(o=t.retryLane),up(e,o)}function Rv(e,t){var o=0;switch(e.tag){case 13:var i=e.stateNode,u=e.memoizedState;u!==null&&(o=u.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(s(314))}i!==null&&i.delete(t),up(e,o)}var cp;cp=function(e,t,o){if(e!==null)if(e.memoizedProps!==t.pendingProps||kt.current)St=!0;else{if((e.lanes&o)===0&&(t.flags&128)===0)return St=!1,vv(e,t,o);St=(e.flags&131072)!==0}else St=!1,Be&&(t.flags&1048576)!==0&&Bd(t,Ui,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;tl(e,t),e=t.pendingProps;var u=Eo(t,ut.current);Oo(t,o),u=Ja(null,t,i,e,u,o);var d=eu();return t.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Nt(i)?(d=!0,Fi(t)):d=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,Qa(t),u.updater=Ji,t.stateNode=u,u._reactInternals=t,iu(t,i,e,o),t=cu(null,t,i,!0,d,o)):(t.tag=0,Be&&d&&Ia(t),vt(null,t,u,o),t=t.child),t;case 16:i=t.elementType;e:{switch(tl(e,t),e=t.pendingProps,u=i._init,i=u(i._payload),t.type=i,u=t.tag=Av(i),e=rn(i,e),u){case 0:t=uu(null,t,i,e,o);break e;case 1:t=If(null,t,i,e,o);break e;case 11:t=_f(null,t,i,e,o);break e;case 14:t=Af(null,t,i,rn(i.type,e),o);break e}throw Error(s(306,i,""))}return t;case 0:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),uu(e,t,i,u,o);case 1:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),If(e,t,i,u,o);case 3:e:{if(Ff(t),e===null)throw Error(s(387));i=t.pendingProps,d=t.memoizedState,u=d.element,Xd(e,t),Qi(t,i,null,o);var m=t.memoizedState;if(i=m.element,d.isDehydrated)if(d={element:i,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){u=Do(Error(s(423)),t),t=Lf(e,t,i,o,u);break e}else if(i!==u){u=Do(Error(s(424)),t),t=Lf(e,t,i,o,u);break e}else for(Dt=fr(t.stateNode.containerInfo.firstChild),Mt=t,Be=!0,nn=null,o=Gd(t,null,i,o),t.child=o;o;)o.flags=o.flags&-3|4096,o=o.sibling;else{if(Ro(),i===u){t=Wn(e,t,o);break e}vt(e,t,i,o)}t=t.child}return t;case 5:return ef(t),e===null&&za(t),i=t.type,u=t.pendingProps,d=e!==null?e.memoizedProps:null,m=u.children,Ra(i,u)?m=null:d!==null&&Ra(i,d)&&(t.flags|=32),Df(e,t),vt(e,t,m,o),t.child;case 6:return e===null&&za(t),null;case 13:return zf(e,t,o);case 4:return Ka(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=_o(t,null,i,o):vt(e,t,i,o),t.child;case 11:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),_f(e,t,i,u,o);case 7:return vt(e,t,t.pendingProps,o),t.child;case 8:return vt(e,t,t.pendingProps.children,o),t.child;case 12:return vt(e,t,t.pendingProps.children,o),t.child;case 10:e:{if(i=t.type._context,u=t.pendingProps,d=t.memoizedProps,m=u.value,Fe(Vi,i._currentValue),i._currentValue=m,d!==null)if(tn(d.value,m)){if(d.children===u.children&&!kt.current){t=Wn(e,t,o);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var N=d.dependencies;if(N!==null){m=d.child;for(var C=N.firstContext;C!==null;){if(C.context===i){if(d.tag===1){C=Hn(-1,o&-o),C.tag=2;var I=d.updateQueue;if(I!==null){I=I.shared;var W=I.pending;W===null?C.next=C:(C.next=W.next,W.next=C),I.pending=C}}d.lanes|=o,C=d.alternate,C!==null&&(C.lanes|=o),Ha(d.return,o,t),N.lanes|=o;break}C=C.next}}else if(d.tag===10)m=d.type===t.type?null:d.child;else if(d.tag===18){if(m=d.return,m===null)throw Error(s(341));m.lanes|=o,N=m.alternate,N!==null&&(N.lanes|=o),Ha(m,o,t),m=d.sibling}else m=d.child;if(m!==null)m.return=d;else for(m=d;m!==null;){if(m===t){m=null;break}if(d=m.sibling,d!==null){d.return=m.return,m=d;break}m=m.return}d=m}vt(e,t,u.children,o),t=t.child}return t;case 9:return u=t.type,i=t.pendingProps.children,Oo(t,o),u=Vt(u),i=i(u),t.flags|=1,vt(e,t,i,o),t.child;case 14:return i=t.type,u=rn(i,t.pendingProps),u=rn(i.type,u),Af(e,t,i,u,o);case 15:return Of(e,t,t.type,t.pendingProps,o);case 17:return i=t.type,u=t.pendingProps,u=t.elementType===i?u:rn(i,u),tl(e,t),t.tag=1,Nt(i)?(e=!0,Fi(t)):e=!1,Oo(t,o),Nf(t,i,u),iu(t,i,u,o),cu(null,t,i,!0,e,o);case 19:return $f(e,t,o);case 22:return Mf(e,t,o)}throw Error(s(156,t.tag))};function dp(e,t){return Vc(e,t)}function _v(e,t,o,i){this.tag=e,this.key=o,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qt(e,t,o,i){return new _v(e,t,o,i)}function Ru(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Av(e){if(typeof e=="function")return Ru(e)?1:0;if(e!=null){if(e=e.$$typeof,e===te)return 11;if(e===ve)return 14}return 2}function kr(e,t){var o=e.alternate;return o===null?(o=Qt(e.tag,t,e.key,e.mode),o.elementType=e.elementType,o.type=e.type,o.stateNode=e.stateNode,o.alternate=e,e.alternate=o):(o.pendingProps=t,o.type=e.type,o.flags=0,o.subtreeFlags=0,o.deletions=null),o.flags=e.flags&14680064,o.childLanes=e.childLanes,o.lanes=e.lanes,o.child=e.child,o.memoizedProps=e.memoizedProps,o.memoizedState=e.memoizedState,o.updateQueue=e.updateQueue,t=e.dependencies,o.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},o.sibling=e.sibling,o.index=e.index,o.ref=e.ref,o}function fl(e,t,o,i,u,d){var m=2;if(i=e,typeof e=="function")Ru(e)&&(m=1);else if(typeof e=="string")m=5;else e:switch(e){case $:return to(o.children,u,d,t);case G:m=8,u|=8;break;case Z:return e=Qt(12,o,t,u|2),e.elementType=Z,e.lanes=d,e;case ge:return e=Qt(13,o,t,u),e.elementType=ge,e.lanes=d,e;case X:return e=Qt(19,o,t,u),e.elementType=X,e.lanes=d,e;case ae:return pl(o,u,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case J:m=10;break e;case pe:m=9;break e;case te:m=11;break e;case ve:m=14;break e;case se:m=16,i=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=Qt(m,o,t,u),t.elementType=e,t.type=i,t.lanes=d,t}function to(e,t,o,i){return e=Qt(7,e,i,t),e.lanes=o,e}function pl(e,t,o,i){return e=Qt(22,e,i,t),e.elementType=ae,e.lanes=o,e.stateNode={isHidden:!1},e}function _u(e,t,o){return e=Qt(6,e,null,t),e.lanes=o,e}function Au(e,t,o){return t=Qt(4,e.children!==null?e.children:[],e.key,t),t.lanes=o,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ov(e,t,o,i,u){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=sa(0),this.expirationTimes=sa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=sa(0),this.identifierPrefix=i,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function Ou(e,t,o,i,u,d,m,N,C){return e=new Ov(e,t,o,N,C),t===1?(t=1,d===!0&&(t|=8)):t=0,d=Qt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:i,isDehydrated:o,cache:null,transitions:null,pendingSuspenseBoundaries:null},Qa(d),e}function Mv(e,t,o){var i=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:F,key:i==null?null:""+i,children:e,containerInfo:t,implementation:o}}function fp(e){if(!e)return hr;e=e._reactInternals;e:{if(Vr(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Nt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var o=e.type;if(Nt(o))return zd(e,o,t)}return t}function pp(e,t,o,i,u,d,m,N,C){return e=Ou(o,i,!0,e,u,d,m,N,C),e.context=fp(null),o=e.current,i=yt(),u=br(o),d=Hn(i,u),d.callback=t??null,vr(o,d,u),e.current.lanes=u,ds(e,u,i),Et(e,i),e}function hl(e,t,o,i){var u=t.current,d=yt(),m=br(u);return o=fp(o),t.context===null?t.context=o:t.pendingContext=o,t=Hn(d,m),t.payload={element:e},i=i===void 0?null:i,i!==null&&(t.callback=i),e=vr(u,t,m),e!==null&&(ln(e,u,m,d),Wi(e,u,m)),m}function ml(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function hp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var o=e.retryLane;e.retryLane=o!==0&&o<t?o:t}}function Mu(e,t){hp(e,t),(e=e.alternate)&&hp(e,t)}function Dv(){return null}var mp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Du(e){this._internalRoot=e}gl.prototype.render=Du.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));hl(e,t,null,null)},gl.prototype.unmount=Du.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Zr(function(){hl(null,e,null,null)}),t[zn]=null}};function gl(e){this._internalRoot=e}gl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xc();e={blockedOn:null,target:e,priority:t};for(var o=0;o<ur.length&&t!==0&&t<ur[o].priority;o++);ur.splice(o,0,e),o===0&&ed(e)}};function Iu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function vl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function gp(){}function Iv(e,t,o,i,u){if(u){if(typeof i=="function"){var d=i;i=function(){var I=ml(m);d.call(I)}}var m=pp(t,i,e,0,null,!1,!1,"",gp);return e._reactRootContainer=m,e[zn]=m.current,Ss(e.nodeType===8?e.parentNode:e),Zr(),m}for(;u=e.lastChild;)e.removeChild(u);if(typeof i=="function"){var N=i;i=function(){var I=ml(C);N.call(I)}}var C=Ou(e,0,!1,null,null,!1,!1,"",gp);return e._reactRootContainer=C,e[zn]=C.current,Ss(e.nodeType===8?e.parentNode:e),Zr(function(){hl(t,C,o,i)}),C}function yl(e,t,o,i,u){var d=o._reactRootContainer;if(d){var m=d;if(typeof u=="function"){var N=u;u=function(){var C=ml(m);N.call(C)}}hl(t,m,e,u)}else m=Iv(o,t,e,u,i);return ml(m)}Gc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var o=cs(t.pendingLanes);o!==0&&(ia(t,o|1),Et(t,Ye()),(Te&6)===0&&(Lo=Ye()+500,mr()))}break;case 13:Zr(function(){var i=Vn(e,1);if(i!==null){var u=yt();ln(i,e,1,u)}}),Mu(e,1)}},la=function(e){if(e.tag===13){var t=Vn(e,134217728);if(t!==null){var o=yt();ln(t,e,134217728,o)}Mu(e,134217728)}},Yc=function(e){if(e.tag===13){var t=br(e),o=Vn(e,t);if(o!==null){var i=yt();ln(o,e,t,i)}Mu(e,t)}},Xc=function(){return Me},Zc=function(e,t){var o=Me;try{return Me=e,t()}finally{Me=o}},vo=function(e,t,o){switch(t){case"input":if($r(e,o),t=o.name,o.type==="radio"&&t!=null){for(o=e;o.parentNode;)o=o.parentNode;for(o=o.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<o.length;t++){var i=o[t];if(i!==e&&i.form===e.form){var u=Di(i);if(!u)throw Error(s(90));Fn(i),$r(i,u)}}}break;case"textarea":li(e,o);break;case"select":t=o.value,t!=null&&Ut(e,!!o.multiple,t,!1)}},Ae=Eu,Ie=Zr;var Fv={usingClientEntryPoint:!1,Events:[Ps,So,Di,fi,xe,Eu]},Bs={findFiberByHostInstance:Hr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Lv={bundleType:Bs.bundleType,version:Bs.version,rendererPackageName:Bs.rendererPackageName,rendererConfig:Bs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:z.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=$c(e),e===null?null:e.stateNode},findFiberByHostInstance:Bs.findFiberByHostInstance||Dv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var xl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xl.isDisabled&&xl.supportsFiber)try{gi=xl.inject(Lv),Nn=xl}catch{}}return Pt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Fv,Pt.createPortal=function(e,t){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Iu(t))throw Error(s(200));return Mv(e,t,null,o)},Pt.createRoot=function(e,t){if(!Iu(e))throw Error(s(299));var o=!1,i="",u=mp;return t!=null&&(t.unstable_strictMode===!0&&(o=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onRecoverableError!==void 0&&(u=t.onRecoverableError)),t=Ou(e,1,!1,null,null,o,!1,i,u),e[zn]=t.current,Ss(e.nodeType===8?e.parentNode:e),new Du(t)},Pt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=$c(t),e=e===null?null:e.stateNode,e},Pt.flushSync=function(e){return Zr(e)},Pt.hydrate=function(e,t,o){if(!vl(t))throw Error(s(200));return yl(null,e,t,!0,o)},Pt.hydrateRoot=function(e,t,o){if(!Iu(e))throw Error(s(405));var i=o!=null&&o.hydratedSources||null,u=!1,d="",m=mp;if(o!=null&&(o.unstable_strictMode===!0&&(u=!0),o.identifierPrefix!==void 0&&(d=o.identifierPrefix),o.onRecoverableError!==void 0&&(m=o.onRecoverableError)),t=pp(t,null,e,1,o??null,u,!1,d,m),e[zn]=t.current,Ss(e),i)for(e=0;e<i.length;e++)o=i[e],u=o._getVersion,u=u(o._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[o,u]:t.mutableSourceEagerHydrationData.push(o,u);return new gl(t)},Pt.render=function(e,t,o){if(!vl(t))throw Error(s(200));return yl(null,e,t,!1,o)},Pt.unmountComponentAtNode=function(e){if(!vl(e))throw Error(s(40));return e._reactRootContainer?(Zr(function(){yl(null,null,e,!1,function(){e._reactRootContainer=null,e[zn]=null})}),!0):!1},Pt.unstable_batchedUpdates=Eu,Pt.unstable_renderSubtreeIntoContainer=function(e,t,o,i){if(!vl(o))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return yl(e,t,o,!1,i)},Pt.version="18.3.1-next-f1338f8080-20240426",Pt}var Sp;function Sh(){if(Sp)return Uu.exports;Sp=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Uu.exports=Qv(),Uu.exports}var Cp;function Kv(){if(Cp)return bl;Cp=1;var n=Sh();return bl.createRoot=n.createRoot,bl.hydrateRoot=n.hydrateRoot,bl}var qv=Kv(),w=hc();const Y=Nh(w),mc=Uv({__proto__:null,default:Y},[w]),Gv=1,Yv=1e6;let Vu=0;function Xv(){return Vu=(Vu+1)%Number.MAX_SAFE_INTEGER,Vu.toString()}const Hu=new Map,Ep=n=>{if(Hu.has(n))return;const r=setTimeout(()=>{Hu.delete(n),Gs({type:"REMOVE_TOAST",toastId:n})},Yv);Hu.set(n,r)},Zv=(n,r)=>{switch(r.type){case"ADD_TOAST":return{...n,toasts:[r.toast,...n.toasts].slice(0,Gv)};case"UPDATE_TOAST":return{...n,toasts:n.toasts.map(s=>s.id===r.toast.id?{...s,...r.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=r;return s?Ep(s):n.toasts.forEach(l=>{Ep(l.id)}),{...n,toasts:n.toasts.map(l=>l.id===s||s===void 0?{...l,open:!1}:l)}}case"REMOVE_TOAST":return r.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(s=>s.id!==r.toastId)}}},Pl=[];let Tl={toasts:[]};function Gs(n){Tl=Zv(Tl,n),Pl.forEach(r=>{r(Tl)})}function Je({...n}){const r=Xv(),s=c=>Gs({type:"UPDATE_TOAST",toast:{...c,id:r}}),l=()=>Gs({type:"DISMISS_TOAST",toastId:r});return Gs({type:"ADD_TOAST",toast:{...n,id:r,open:!0,onOpenChange:c=>{c||l()}}}),{id:r,dismiss:l,update:s}}function Jv(){const[n,r]=w.useState(Tl);return w.useEffect(()=>(Pl.push(r),()=>{const s=Pl.indexOf(r);s>-1&&Pl.splice(s,1)}),[n]),{...n,toast:Je,dismiss:s=>Gs({type:"DISMISS_TOAST",toastId:s})}}var zl=Sh();const Ch=Nh(zl);function Oe(n,r,{checkForDefaultPrevented:s=!0}={}){return function(c){if(n==null||n(c),s===!1||!c.defaultPrevented)return r==null?void 0:r(c)}}function Pp(n,r){if(typeof n=="function")return n(r);n!=null&&(n.current=r)}function Eh(...n){return r=>{let s=!1;const l=n.map(c=>{const f=Pp(c,r);return!s&&typeof f=="function"&&(s=!0),f});if(s)return()=>{for(let c=0;c<l.length;c++){const f=l[c];typeof f=="function"?f():Pp(n[c],null)}}}}function at(...n){return w.useCallback(Eh(...n),n)}function zr(n,r=[]){let s=[];function l(f,p){const h=w.createContext(p),g=s.length;s=[...s,p];const v=b=>{var _;const{scope:S,children:y,...E}=b,k=((_=S==null?void 0:S[n])==null?void 0:_[g])||h,j=w.useMemo(()=>E,Object.values(E));return a.jsx(k.Provider,{value:j,children:y})};v.displayName=f+"Provider";function x(b,S){var k;const y=((k=S==null?void 0:S[n])==null?void 0:k[g])||h,E=w.useContext(y);if(E)return E;if(p!==void 0)return p;throw new Error(`\`${b}\` must be used within \`${f}\``)}return[v,x]}const c=()=>{const f=s.map(p=>w.createContext(p));return function(h){const g=(h==null?void 0:h[n])||f;return w.useMemo(()=>({[`__scope${n}`]:{...h,[n]:g}}),[h,g])}};return c.scopeName=n,[l,ey(c,...r)]}function ey(...n){const r=n[0];if(n.length===1)return r;const s=()=>{const l=n.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(f){const p=l.reduce((h,{useScope:g,scopeName:v})=>{const b=g(f)[`__scope${v}`];return{...h,...b}},{});return w.useMemo(()=>({[`__scope${r.scopeName}`]:p}),[p])}};return s.scopeName=r.scopeName,s}function _l(n){const r=ny(n),s=w.forwardRef((l,c)=>{const{children:f,...p}=l,h=w.Children.toArray(f),g=h.find(oy);if(g){const v=g.props.children,x=h.map(b=>b===g?w.Children.count(v)>1?w.Children.only(null):w.isValidElement(v)?v.props.children:null:b);return a.jsx(r,{...p,ref:c,children:w.isValidElement(v)?w.cloneElement(v,void 0,x):null})}return a.jsx(r,{...p,ref:c,children:f})});return s.displayName=`${n}.Slot`,s}var ty=_l("Slot");function ny(n){const r=w.forwardRef((s,l)=>{const{children:c,...f}=s;if(w.isValidElement(c)){const p=iy(c),h=sy(f,c.props);return c.type!==w.Fragment&&(h.ref=l?Eh(l,p):p),w.cloneElement(c,h)}return w.Children.count(c)>1?w.Children.only(null):null});return r.displayName=`${n}.SlotClone`,r}var Ph=Symbol("radix.slottable");function ry(n){const r=({children:s})=>a.jsx(a.Fragment,{children:s});return r.displayName=`${n}.Slottable`,r.__radixId=Ph,r}function oy(n){return w.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===Ph}function sy(n,r){const s={...r};for(const l in r){const c=n[l],f=r[l];/^on[A-Z]/.test(l)?c&&f?s[l]=(...h)=>{const g=f(...h);return c(...h),g}:c&&(s[l]=c):l==="style"?s[l]={...c,...f}:l==="className"&&(s[l]=[c,f].filter(Boolean).join(" "))}return{...n,...s}}function iy(n){var l,c;let r=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?n.ref:(r=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}function Th(n){const r=n+"CollectionProvider",[s,l]=zr(r),[c,f]=s(r,{collectionRef:{current:null},itemMap:new Map}),p=k=>{const{scope:j,children:_}=k,P=Y.useRef(null),A=Y.useRef(new Map).current;return a.jsx(c,{scope:j,itemMap:A,collectionRef:P,children:_})};p.displayName=r;const h=n+"CollectionSlot",g=_l(h),v=Y.forwardRef((k,j)=>{const{scope:_,children:P}=k,A=f(h,_),z=at(j,A.collectionRef);return a.jsx(g,{ref:z,children:P})});v.displayName=h;const x=n+"CollectionItemSlot",b="data-radix-collection-item",S=_l(x),y=Y.forwardRef((k,j)=>{const{scope:_,children:P,...A}=k,z=Y.useRef(null),O=at(j,z),F=f(x,_);return Y.useEffect(()=>(F.itemMap.set(z,{ref:z,...A}),()=>void F.itemMap.delete(z))),a.jsx(S,{[b]:"",ref:O,children:P})});y.displayName=x;function E(k){const j=f(n+"CollectionConsumer",k);return Y.useCallback(()=>{const P=j.collectionRef.current;if(!P)return[];const A=Array.from(P.querySelectorAll(`[${b}]`));return Array.from(j.itemMap.values()).sort((F,$)=>A.indexOf(F.ref.current)-A.indexOf($.ref.current))},[j.collectionRef,j.itemMap])}return[{Provider:p,Slot:v,ItemSlot:y},E,l]}var ly=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],$e=ly.reduce((n,r)=>{const s=_l(`Primitive.${r}`),l=w.forwardRef((c,f)=>{const{asChild:p,...h}=c,g=p?s:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),a.jsx(g,{...h,ref:f})});return l.displayName=`Primitive.${r}`,{...n,[r]:l}},{});function Rh(n,r){n&&zl.flushSync(()=>n.dispatchEvent(r))}function Zn(n){const r=w.useRef(n);return w.useEffect(()=>{r.current=n}),w.useMemo(()=>(...s)=>{var l;return(l=r.current)==null?void 0:l.call(r,...s)},[])}function ay(n,r=globalThis==null?void 0:globalThis.document){const s=Zn(n);w.useEffect(()=>{const l=c=>{c.key==="Escape"&&s(c)};return r.addEventListener("keydown",l,{capture:!0}),()=>r.removeEventListener("keydown",l,{capture:!0})},[s,r])}var uy="DismissableLayer",Zu="dismissableLayer.update",cy="dismissableLayer.pointerDownOutside",dy="dismissableLayer.focusOutside",Tp,_h=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),gc=w.forwardRef((n,r)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:h,...g}=n,v=w.useContext(_h),[x,b]=w.useState(null),S=(x==null?void 0:x.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=w.useState({}),E=at(r,$=>b($)),k=Array.from(v.layers),[j]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),_=k.indexOf(j),P=x?k.indexOf(x):-1,A=v.layersWithOutsidePointerEventsDisabled.size>0,z=P>=_,O=py($=>{const G=$.target,Z=[...v.branches].some(J=>J.contains(G));!z||Z||(c==null||c($),p==null||p($),$.defaultPrevented||h==null||h())},S),F=hy($=>{const G=$.target;[...v.branches].some(J=>J.contains(G))||(f==null||f($),p==null||p($),$.defaultPrevented||h==null||h())},S);return ay($=>{P===v.layers.size-1&&(l==null||l($),!$.defaultPrevented&&h&&($.preventDefault(),h()))},S),w.useEffect(()=>{if(x)return s&&(v.layersWithOutsidePointerEventsDisabled.size===0&&(Tp=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(x)),v.layers.add(x),Rp(),()=>{s&&v.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=Tp)}},[x,S,s,v]),w.useEffect(()=>()=>{x&&(v.layers.delete(x),v.layersWithOutsidePointerEventsDisabled.delete(x),Rp())},[x,v]),w.useEffect(()=>{const $=()=>y({});return document.addEventListener(Zu,$),()=>document.removeEventListener(Zu,$)},[]),a.jsx($e.div,{...g,ref:E,style:{pointerEvents:A?z?"auto":"none":void 0,...n.style},onFocusCapture:Oe(n.onFocusCapture,F.onFocusCapture),onBlurCapture:Oe(n.onBlurCapture,F.onBlurCapture),onPointerDownCapture:Oe(n.onPointerDownCapture,O.onPointerDownCapture)})});gc.displayName=uy;var fy="DismissableLayerBranch",Ah=w.forwardRef((n,r)=>{const s=w.useContext(_h),l=w.useRef(null),c=at(r,l);return w.useEffect(()=>{const f=l.current;if(f)return s.branches.add(f),()=>{s.branches.delete(f)}},[s.branches]),a.jsx($e.div,{...n,ref:c})});Ah.displayName=fy;function py(n,r=globalThis==null?void 0:globalThis.document){const s=Zn(n),l=w.useRef(!1),c=w.useRef(()=>{});return w.useEffect(()=>{const f=h=>{if(h.target&&!l.current){let g=function(){Oh(cy,s,v,{discrete:!0})};const v={originalEvent:h};h.pointerType==="touch"?(r.removeEventListener("click",c.current),c.current=g,r.addEventListener("click",c.current,{once:!0})):g()}else r.removeEventListener("click",c.current);l.current=!1},p=window.setTimeout(()=>{r.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(p),r.removeEventListener("pointerdown",f),r.removeEventListener("click",c.current)}},[r,s]),{onPointerDownCapture:()=>l.current=!0}}function hy(n,r=globalThis==null?void 0:globalThis.document){const s=Zn(n),l=w.useRef(!1);return w.useEffect(()=>{const c=f=>{f.target&&!l.current&&Oh(dy,s,{originalEvent:f},{discrete:!1})};return r.addEventListener("focusin",c),()=>r.removeEventListener("focusin",c)},[r,s]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}function Rp(){const n=new CustomEvent(Zu);document.dispatchEvent(n)}function Oh(n,r,s,{discrete:l}){const c=s.originalEvent.target,f=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:s});r&&c.addEventListener(n,r,{once:!0}),l?Rh(c,f):c.dispatchEvent(f)}var my=gc,gy=Ah,Jn=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},vy="Portal",Mh=w.forwardRef((n,r)=>{var h;const{container:s,...l}=n,[c,f]=w.useState(!1);Jn(()=>f(!0),[]);const p=s||c&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return p?Ch.createPortal(a.jsx($e.div,{...l,ref:r}),p):null});Mh.displayName=vy;function yy(n,r){return w.useReducer((s,l)=>r[s][l]??s,n)}var ni=n=>{const{present:r,children:s}=n,l=xy(r),c=typeof s=="function"?s({present:l.isPresent}):w.Children.only(s),f=at(l.ref,wy(c));return typeof s=="function"||l.isPresent?w.cloneElement(c,{ref:f}):null};ni.displayName="Presence";function xy(n){const[r,s]=w.useState(),l=w.useRef(null),c=w.useRef(n),f=w.useRef("none"),p=n?"mounted":"unmounted",[h,g]=yy(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const v=jl(l.current);f.current=h==="mounted"?v:"none"},[h]),Jn(()=>{const v=l.current,x=c.current;if(x!==n){const S=f.current,y=jl(v);n?g("MOUNT"):y==="none"||(v==null?void 0:v.display)==="none"?g("UNMOUNT"):g(x&&S!==y?"ANIMATION_OUT":"UNMOUNT"),c.current=n}},[n,g]),Jn(()=>{if(r){let v;const x=r.ownerDocument.defaultView??window,b=y=>{const k=jl(l.current).includes(y.animationName);if(y.target===r&&k&&(g("ANIMATION_END"),!c.current)){const j=r.style.animationFillMode;r.style.animationFillMode="forwards",v=x.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=j)})}},S=y=>{y.target===r&&(f.current=jl(l.current))};return r.addEventListener("animationstart",S),r.addEventListener("animationcancel",b),r.addEventListener("animationend",b),()=>{x.clearTimeout(v),r.removeEventListener("animationstart",S),r.removeEventListener("animationcancel",b),r.removeEventListener("animationend",b)}}else g("ANIMATION_END")},[r,g]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(v=>{l.current=v?getComputedStyle(v):null,s(v)},[])}}function jl(n){return(n==null?void 0:n.animationName)||"none"}function wy(n){var l,c;let r=(l=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:l.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?n.ref:(r=(c=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?n.props.ref:n.props.ref||n.ref)}var by=mc[" useInsertionEffect ".trim().toString()]||Jn;function Ul({prop:n,defaultProp:r,onChange:s=()=>{},caller:l}){const[c,f,p]=jy({defaultProp:r,onChange:s}),h=n!==void 0,g=h?n:c;{const x=w.useRef(n!==void 0);w.useEffect(()=>{const b=x.current;b!==h&&console.warn(`${l} is changing from ${b?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),x.current=h},[h,l])}const v=w.useCallback(x=>{var b;if(h){const S=ky(x)?x(n):x;S!==n&&((b=p.current)==null||b.call(p,S))}else f(x)},[h,n,f,p]);return[g,v]}function jy({defaultProp:n,onChange:r}){const[s,l]=w.useState(n),c=w.useRef(s),f=w.useRef(r);return by(()=>{f.current=r},[r]),w.useEffect(()=>{var p;c.current!==s&&((p=f.current)==null||p.call(f,s),c.current=s)},[s,c]),[s,l,f]}function ky(n){return typeof n=="function"}var Ny=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Sy="VisuallyHidden",$l=w.forwardRef((n,r)=>a.jsx($e.span,{...n,ref:r,style:{...Ny,...n.style}}));$l.displayName=Sy;var Cy=$l,vc="ToastProvider",[yc,Ey,Py]=Th("Toast"),[Dh,L2]=zr("Toast",[Py]),[Ty,Bl]=Dh(vc),Ih=n=>{const{__scopeToast:r,label:s="Notification",duration:l=5e3,swipeDirection:c="right",swipeThreshold:f=50,children:p}=n,[h,g]=w.useState(null),[v,x]=w.useState(0),b=w.useRef(!1),S=w.useRef(!1);return s.trim()||console.error(`Invalid prop \`label\` supplied to \`${vc}\`. Expected non-empty \`string\`.`),a.jsx(yc.Provider,{scope:r,children:a.jsx(Ty,{scope:r,label:s,duration:l,swipeDirection:c,swipeThreshold:f,toastCount:v,viewport:h,onViewportChange:g,onToastAdd:w.useCallback(()=>x(y=>y+1),[]),onToastRemove:w.useCallback(()=>x(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:b,isClosePausedRef:S,children:p})})};Ih.displayName=vc;var Fh="ToastViewport",Ry=["F8"],Ju="toast.viewportPause",ec="toast.viewportResume",Lh=w.forwardRef((n,r)=>{const{__scopeToast:s,hotkey:l=Ry,label:c="Notifications ({hotkey})",...f}=n,p=Bl(Fh,s),h=Ey(s),g=w.useRef(null),v=w.useRef(null),x=w.useRef(null),b=w.useRef(null),S=at(r,b,p.onViewportChange),y=l.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=p.toastCount>0;w.useEffect(()=>{const j=_=>{var A;l.length!==0&&l.every(z=>_[z]||_.code===z)&&((A=b.current)==null||A.focus())};return document.addEventListener("keydown",j),()=>document.removeEventListener("keydown",j)},[l]),w.useEffect(()=>{const j=g.current,_=b.current;if(E&&j&&_){const P=()=>{if(!p.isClosePausedRef.current){const F=new CustomEvent(Ju);_.dispatchEvent(F),p.isClosePausedRef.current=!0}},A=()=>{if(p.isClosePausedRef.current){const F=new CustomEvent(ec);_.dispatchEvent(F),p.isClosePausedRef.current=!1}},z=F=>{!j.contains(F.relatedTarget)&&A()},O=()=>{j.contains(document.activeElement)||A()};return j.addEventListener("focusin",P),j.addEventListener("focusout",z),j.addEventListener("pointermove",P),j.addEventListener("pointerleave",O),window.addEventListener("blur",P),window.addEventListener("focus",A),()=>{j.removeEventListener("focusin",P),j.removeEventListener("focusout",z),j.removeEventListener("pointermove",P),j.removeEventListener("pointerleave",O),window.removeEventListener("blur",P),window.removeEventListener("focus",A)}}},[E,p.isClosePausedRef]);const k=w.useCallback(({tabbingDirection:j})=>{const P=h().map(A=>{const z=A.ref.current,O=[z,...Vy(z)];return j==="forwards"?O:O.reverse()});return(j==="forwards"?P.reverse():P).flat()},[h]);return w.useEffect(()=>{const j=b.current;if(j){const _=P=>{var O,F,$;const A=P.altKey||P.ctrlKey||P.metaKey;if(P.key==="Tab"&&!A){const G=document.activeElement,Z=P.shiftKey;if(P.target===j&&Z){(O=v.current)==null||O.focus();return}const te=k({tabbingDirection:Z?"backwards":"forwards"}),ge=te.findIndex(X=>X===G);Wu(te.slice(ge+1))?P.preventDefault():Z?(F=v.current)==null||F.focus():($=x.current)==null||$.focus()}};return j.addEventListener("keydown",_),()=>j.removeEventListener("keydown",_)}},[h,k]),a.jsxs(gy,{ref:g,role:"region","aria-label":c.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:E?void 0:"none"},children:[E&&a.jsx(tc,{ref:v,onFocusFromOutsideViewport:()=>{const j=k({tabbingDirection:"forwards"});Wu(j)}}),a.jsx(yc.Slot,{scope:s,children:a.jsx($e.ol,{tabIndex:-1,...f,ref:S})}),E&&a.jsx(tc,{ref:x,onFocusFromOutsideViewport:()=>{const j=k({tabbingDirection:"backwards"});Wu(j)}})]})});Lh.displayName=Fh;var zh="ToastFocusProxy",tc=w.forwardRef((n,r)=>{const{__scopeToast:s,onFocusFromOutsideViewport:l,...c}=n,f=Bl(zh,s);return a.jsx($l,{"aria-hidden":!0,tabIndex:0,...c,ref:r,style:{position:"fixed"},onFocus:p=>{var v;const h=p.relatedTarget;!((v=f.viewport)!=null&&v.contains(h))&&l()}})});tc.displayName=zh;var ri="Toast",_y="toast.swipeStart",Ay="toast.swipeMove",Oy="toast.swipeCancel",My="toast.swipeEnd",Uh=w.forwardRef((n,r)=>{const{forceMount:s,open:l,defaultOpen:c,onOpenChange:f,...p}=n,[h,g]=Ul({prop:l,defaultProp:c??!0,onChange:f,caller:ri});return a.jsx(ni,{present:s||h,children:a.jsx(Fy,{open:h,...p,ref:r,onClose:()=>g(!1),onPause:Zn(n.onPause),onResume:Zn(n.onResume),onSwipeStart:Oe(n.onSwipeStart,v=>{v.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Oe(n.onSwipeMove,v=>{const{x,y:b}=v.detail.delta;v.currentTarget.setAttribute("data-swipe","move"),v.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${x}px`),v.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${b}px`)}),onSwipeCancel:Oe(n.onSwipeCancel,v=>{v.currentTarget.setAttribute("data-swipe","cancel"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),v.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Oe(n.onSwipeEnd,v=>{const{x,y:b}=v.detail.delta;v.currentTarget.setAttribute("data-swipe","end"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),v.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${x}px`),v.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${b}px`),g(!1)})})})});Uh.displayName=ri;var[Dy,Iy]=Dh(ri,{onClose(){}}),Fy=w.forwardRef((n,r)=>{const{__scopeToast:s,type:l="foreground",duration:c,open:f,onClose:p,onEscapeKeyDown:h,onPause:g,onResume:v,onSwipeStart:x,onSwipeMove:b,onSwipeCancel:S,onSwipeEnd:y,...E}=n,k=Bl(ri,s),[j,_]=w.useState(null),P=at(r,X=>_(X)),A=w.useRef(null),z=w.useRef(null),O=c||k.duration,F=w.useRef(0),$=w.useRef(O),G=w.useRef(0),{onToastAdd:Z,onToastRemove:J}=k,pe=Zn(()=>{var ve;(j==null?void 0:j.contains(document.activeElement))&&((ve=k.viewport)==null||ve.focus()),p()}),te=w.useCallback(X=>{!X||X===1/0||(window.clearTimeout(G.current),F.current=new Date().getTime(),G.current=window.setTimeout(pe,X))},[pe]);w.useEffect(()=>{const X=k.viewport;if(X){const ve=()=>{te($.current),v==null||v()},se=()=>{const ae=new Date().getTime()-F.current;$.current=$.current-ae,window.clearTimeout(G.current),g==null||g()};return X.addEventListener(Ju,se),X.addEventListener(ec,ve),()=>{X.removeEventListener(Ju,se),X.removeEventListener(ec,ve)}}},[k.viewport,O,g,v,te]),w.useEffect(()=>{f&&!k.isClosePausedRef.current&&te(O)},[f,O,k.isClosePausedRef,te]),w.useEffect(()=>(Z(),()=>J()),[Z,J]);const ge=w.useMemo(()=>j?Kh(j):null,[j]);return k.viewport?a.jsxs(a.Fragment,{children:[ge&&a.jsx(Ly,{__scopeToast:s,role:"status","aria-live":l==="foreground"?"assertive":"polite","aria-atomic":!0,children:ge}),a.jsx(Dy,{scope:s,onClose:pe,children:zl.createPortal(a.jsx(yc.ItemSlot,{scope:s,children:a.jsx(my,{asChild:!0,onEscapeKeyDown:Oe(h,()=>{k.isFocusedToastEscapeKeyDownRef.current||pe(),k.isFocusedToastEscapeKeyDownRef.current=!1}),children:a.jsx($e.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":f?"open":"closed","data-swipe-direction":k.swipeDirection,...E,ref:P,style:{userSelect:"none",touchAction:"none",...n.style},onKeyDown:Oe(n.onKeyDown,X=>{X.key==="Escape"&&(h==null||h(X.nativeEvent),X.nativeEvent.defaultPrevented||(k.isFocusedToastEscapeKeyDownRef.current=!0,pe()))}),onPointerDown:Oe(n.onPointerDown,X=>{X.button===0&&(A.current={x:X.clientX,y:X.clientY})}),onPointerMove:Oe(n.onPointerMove,X=>{if(!A.current)return;const ve=X.clientX-A.current.x,se=X.clientY-A.current.y,ae=!!z.current,U=["left","right"].includes(k.swipeDirection),V=["left","up"].includes(k.swipeDirection)?Math.min:Math.max,K=U?V(0,ve):0,R=U?0:V(0,se),L=X.pointerType==="touch"?10:2,ee={x:K,y:R},re={originalEvent:X,delta:ee};ae?(z.current=ee,kl(Ay,b,re,{discrete:!1})):_p(ee,k.swipeDirection,L)?(z.current=ee,kl(_y,x,re,{discrete:!1}),X.target.setPointerCapture(X.pointerId)):(Math.abs(ve)>L||Math.abs(se)>L)&&(A.current=null)}),onPointerUp:Oe(n.onPointerUp,X=>{const ve=z.current,se=X.target;if(se.hasPointerCapture(X.pointerId)&&se.releasePointerCapture(X.pointerId),z.current=null,A.current=null,ve){const ae=X.currentTarget,U={originalEvent:X,delta:ve};_p(ve,k.swipeDirection,k.swipeThreshold)?kl(My,y,U,{discrete:!0}):kl(Oy,S,U,{discrete:!0}),ae.addEventListener("click",V=>V.preventDefault(),{once:!0})}})})})}),k.viewport)})]}):null}),Ly=n=>{const{__scopeToast:r,children:s,...l}=n,c=Bl(ri,r),[f,p]=w.useState(!1),[h,g]=w.useState(!1);return $y(()=>p(!0)),w.useEffect(()=>{const v=window.setTimeout(()=>g(!0),1e3);return()=>window.clearTimeout(v)},[]),h?null:a.jsx(Mh,{asChild:!0,children:a.jsx($l,{...l,children:f&&a.jsxs(a.Fragment,{children:[c.label," ",s]})})})},zy="ToastTitle",$h=w.forwardRef((n,r)=>{const{__scopeToast:s,...l}=n;return a.jsx($e.div,{...l,ref:r})});$h.displayName=zy;var Uy="ToastDescription",Bh=w.forwardRef((n,r)=>{const{__scopeToast:s,...l}=n;return a.jsx($e.div,{...l,ref:r})});Bh.displayName=Uy;var Vh="ToastAction",Hh=w.forwardRef((n,r)=>{const{altText:s,...l}=n;return s.trim()?a.jsx(Qh,{altText:s,asChild:!0,children:a.jsx(xc,{...l,ref:r})}):(console.error(`Invalid prop \`altText\` supplied to \`${Vh}\`. Expected non-empty \`string\`.`),null)});Hh.displayName=Vh;var Wh="ToastClose",xc=w.forwardRef((n,r)=>{const{__scopeToast:s,...l}=n,c=Iy(Wh,s);return a.jsx(Qh,{asChild:!0,children:a.jsx($e.button,{type:"button",...l,ref:r,onClick:Oe(n.onClick,c.onClose)})})});xc.displayName=Wh;var Qh=w.forwardRef((n,r)=>{const{__scopeToast:s,altText:l,...c}=n;return a.jsx($e.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":l||void 0,...c,ref:r})});function Kh(n){const r=[];return Array.from(n.childNodes).forEach(l=>{if(l.nodeType===l.TEXT_NODE&&l.textContent&&r.push(l.textContent),By(l)){const c=l.ariaHidden||l.hidden||l.style.display==="none",f=l.dataset.radixToastAnnounceExclude==="";if(!c)if(f){const p=l.dataset.radixToastAnnounceAlt;p&&r.push(p)}else r.push(...Kh(l))}}),r}function kl(n,r,s,{discrete:l}){const c=s.originalEvent.currentTarget,f=new CustomEvent(n,{bubbles:!0,cancelable:!0,detail:s});r&&c.addEventListener(n,r,{once:!0}),l?Rh(c,f):c.dispatchEvent(f)}var _p=(n,r,s=0)=>{const l=Math.abs(n.x),c=Math.abs(n.y),f=l>c;return r==="left"||r==="right"?f&&l>s:!f&&c>s};function $y(n=()=>{}){const r=Zn(n);Jn(()=>{let s=0,l=0;return s=window.requestAnimationFrame(()=>l=window.requestAnimationFrame(r)),()=>{window.cancelAnimationFrame(s),window.cancelAnimationFrame(l)}},[r])}function By(n){return n.nodeType===n.ELEMENT_NODE}function Vy(n){const r=[],s=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:l=>{const c=l.tagName==="INPUT"&&l.type==="hidden";return l.disabled||l.hidden||c?NodeFilter.FILTER_SKIP:l.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)r.push(s.currentNode);return r}function Wu(n){const r=document.activeElement;return n.some(s=>s===r?!0:(s.focus(),document.activeElement!==r))}var Hy=Ih,qh=Lh,Gh=Uh,Yh=$h,Xh=Bh,Zh=Hh,Jh=xc;function em(n){var r,s,l="";if(typeof n=="string"||typeof n=="number")l+=n;else if(typeof n=="object")if(Array.isArray(n)){var c=n.length;for(r=0;r<c;r++)n[r]&&(s=em(n[r]))&&(l&&(l+=" "),l+=s)}else for(s in n)n[s]&&(l&&(l+=" "),l+=s);return l}function tm(){for(var n,r,s=0,l="",c=arguments.length;s<c;s++)(n=arguments[s])&&(r=em(n))&&(l&&(l+=" "),l+=r);return l}const Ap=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,Op=tm,wc=(n,r)=>s=>{var l;if((r==null?void 0:r.variants)==null)return Op(n,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:c,defaultVariants:f}=r,p=Object.keys(c).map(v=>{const x=s==null?void 0:s[v],b=f==null?void 0:f[v];if(x===null)return null;const S=Ap(x)||Ap(b);return c[v][S]}),h=s&&Object.entries(s).reduce((v,x)=>{let[b,S]=x;return S===void 0||(v[b]=S),v},{}),g=r==null||(l=r.compoundVariants)===null||l===void 0?void 0:l.reduce((v,x)=>{let{class:b,className:S,...y}=x;return Object.entries(y).every(E=>{let[k,j]=E;return Array.isArray(j)?j.includes({...f,...h}[k]):{...f,...h}[k]===j})?[...v,b,S]:v},[]);return Op(n,p,g,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wy=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nm=(...n)=>n.filter((r,s,l)=>!!r&&r.trim()!==""&&l.indexOf(r)===s).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Qy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ky=w.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:f,iconNode:p,...h},g)=>w.createElement("svg",{ref:g,...Qy,width:r,height:r,stroke:n,strokeWidth:l?Number(s)*24/Number(r):s,className:nm("lucide",c),...h},[...p.map(([v,x])=>w.createElement(v,x)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=(n,r)=>{const s=w.forwardRef(({className:l,...c},f)=>w.createElement(Ky,{ref:f,iconNode:r,className:nm(`lucide-${Wy(n)}`,l),...c}));return s.displayName=`${n}`,s};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=De("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gy=De("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=De("Box",[["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xy=De("Calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=De("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=De("ChartColumnIncreasing",[["path",{d:"M13 17V9",key:"1fwyjl"}],["path",{d:"M18 17V5",key:"sfb6ij"}],["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=De("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=De("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rm=De("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bc=De("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nx=De("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rx=De("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ox=De("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sx=De("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const om=De("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sm=De("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=De("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lx=De("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ax=De("Sheet",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["line",{x1:"3",x2:"21",y1:"9",y2:"9",key:"1vqk6q"}],["line",{x1:"3",x2:"21",y1:"15",y2:"15",key:"o2sbyz"}],["line",{x1:"9",x2:"9",y1:"9",y2:"21",key:"1ib60c"}],["line",{x1:"15",x2:"15",y1:"9",y2:"21",key:"1n26ft"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ux=De("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cx=De("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dx=De("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=De("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const px=De("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=De("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const im=De("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=De("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=De("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=De("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),jc="-",yx=n=>{const r=wx(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:l}=n;return{getClassGroupId:p=>{const h=p.split(jc);return h[0]===""&&h.length!==1&&h.shift(),lm(h,r)||xx(p)},getConflictingClassGroupIds:(p,h)=>{const g=s[p]||[];return h&&l[p]?[...g,...l[p]]:g}}},lm=(n,r)=>{var p;if(n.length===0)return r.classGroupId;const s=n[0],l=r.nextPart.get(s),c=l?lm(n.slice(1),l):void 0;if(c)return c;if(r.validators.length===0)return;const f=n.join(jc);return(p=r.validators.find(({validator:h})=>h(f)))==null?void 0:p.classGroupId},Mp=/^\[(.+)\]$/,xx=n=>{if(Mp.test(n)){const r=Mp.exec(n)[1],s=r==null?void 0:r.substring(0,r.indexOf(":"));if(s)return"arbitrary.."+s}},wx=n=>{const{theme:r,prefix:s}=n,l={nextPart:new Map,validators:[]};return jx(Object.entries(n.classGroups),s).forEach(([f,p])=>{nc(p,l,f,r)}),l},nc=(n,r,s,l)=>{n.forEach(c=>{if(typeof c=="string"){const f=c===""?r:Dp(r,c);f.classGroupId=s;return}if(typeof c=="function"){if(bx(c)){nc(c(l),r,s,l);return}r.validators.push({validator:c,classGroupId:s});return}Object.entries(c).forEach(([f,p])=>{nc(p,Dp(r,f),s,l)})})},Dp=(n,r)=>{let s=n;return r.split(jc).forEach(l=>{s.nextPart.has(l)||s.nextPart.set(l,{nextPart:new Map,validators:[]}),s=s.nextPart.get(l)}),s},bx=n=>n.isThemeGetter,jx=(n,r)=>r?n.map(([s,l])=>{const c=l.map(f=>typeof f=="string"?r+f:typeof f=="object"?Object.fromEntries(Object.entries(f).map(([p,h])=>[r+p,h])):f);return[s,c]}):n,kx=n=>{if(n<1)return{get:()=>{},set:()=>{}};let r=0,s=new Map,l=new Map;const c=(f,p)=>{s.set(f,p),r++,r>n&&(r=0,l=s,s=new Map)};return{get(f){let p=s.get(f);if(p!==void 0)return p;if((p=l.get(f))!==void 0)return c(f,p),p},set(f,p){s.has(f)?s.set(f,p):c(f,p)}}},am="!",Nx=n=>{const{separator:r,experimentalParseClassName:s}=n,l=r.length===1,c=r[0],f=r.length,p=h=>{const g=[];let v=0,x=0,b;for(let j=0;j<h.length;j++){let _=h[j];if(v===0){if(_===c&&(l||h.slice(j,j+f)===r)){g.push(h.slice(x,j)),x=j+f;continue}if(_==="/"){b=j;continue}}_==="["?v++:_==="]"&&v--}const S=g.length===0?h:h.substring(x),y=S.startsWith(am),E=y?S.substring(1):S,k=b&&b>x?b-x:void 0;return{modifiers:g,hasImportantModifier:y,baseClassName:E,maybePostfixModifierPosition:k}};return s?h=>s({className:h,parseClassName:p}):p},Sx=n=>{if(n.length<=1)return n;const r=[];let s=[];return n.forEach(l=>{l[0]==="["?(r.push(...s.sort(),l),s=[]):s.push(l)}),r.push(...s.sort()),r},Cx=n=>({cache:kx(n.cacheSize),parseClassName:Nx(n),...yx(n)}),Ex=/\s+/,Px=(n,r)=>{const{parseClassName:s,getClassGroupId:l,getConflictingClassGroupIds:c}=r,f=[],p=n.trim().split(Ex);let h="";for(let g=p.length-1;g>=0;g-=1){const v=p[g],{modifiers:x,hasImportantModifier:b,baseClassName:S,maybePostfixModifierPosition:y}=s(v);let E=!!y,k=l(E?S.substring(0,y):S);if(!k){if(!E){h=v+(h.length>0?" "+h:h);continue}if(k=l(S),!k){h=v+(h.length>0?" "+h:h);continue}E=!1}const j=Sx(x).join(":"),_=b?j+am:j,P=_+k;if(f.includes(P))continue;f.push(P);const A=c(k,E);for(let z=0;z<A.length;++z){const O=A[z];f.push(_+O)}h=v+(h.length>0?" "+h:h)}return h};function Tx(){let n=0,r,s,l="";for(;n<arguments.length;)(r=arguments[n++])&&(s=um(r))&&(l&&(l+=" "),l+=s);return l}const um=n=>{if(typeof n=="string")return n;let r,s="";for(let l=0;l<n.length;l++)n[l]&&(r=um(n[l]))&&(s&&(s+=" "),s+=r);return s};function Rx(n,...r){let s,l,c,f=p;function p(g){const v=r.reduce((x,b)=>b(x),n());return s=Cx(v),l=s.cache.get,c=s.cache.set,f=h,h(g)}function h(g){const v=l(g);if(v)return v;const x=Px(g,s);return c(g,x),x}return function(){return f(Tx.apply(null,arguments))}}const Ue=n=>{const r=s=>s[n]||[];return r.isThemeGetter=!0,r},cm=/^\[(?:([a-z-]+):)?(.+)\]$/i,_x=/^\d+\/\d+$/,Ax=new Set(["px","full","screen"]),Ox=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Mx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Dx=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ix=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Fx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Kn=n=>$o(n)||Ax.has(n)||_x.test(n),Sr=n=>os(n,"length",Wx),$o=n=>!!n&&!Number.isNaN(Number(n)),Qu=n=>os(n,"number",$o),Hs=n=>!!n&&Number.isInteger(Number(n)),Lx=n=>n.endsWith("%")&&$o(n.slice(0,-1)),je=n=>cm.test(n),Cr=n=>Ox.test(n),zx=new Set(["length","size","percentage"]),Ux=n=>os(n,zx,dm),$x=n=>os(n,"position",dm),Bx=new Set(["image","url"]),Vx=n=>os(n,Bx,Kx),Hx=n=>os(n,"",Qx),Ws=()=>!0,os=(n,r,s)=>{const l=cm.exec(n);return l?l[1]?typeof r=="string"?l[1]===r:r.has(l[1]):s(l[2]):!1},Wx=n=>Mx.test(n)&&!Dx.test(n),dm=()=>!1,Qx=n=>Ix.test(n),Kx=n=>Fx.test(n),qx=()=>{const n=Ue("colors"),r=Ue("spacing"),s=Ue("blur"),l=Ue("brightness"),c=Ue("borderColor"),f=Ue("borderRadius"),p=Ue("borderSpacing"),h=Ue("borderWidth"),g=Ue("contrast"),v=Ue("grayscale"),x=Ue("hueRotate"),b=Ue("invert"),S=Ue("gap"),y=Ue("gradientColorStops"),E=Ue("gradientColorStopPositions"),k=Ue("inset"),j=Ue("margin"),_=Ue("opacity"),P=Ue("padding"),A=Ue("saturate"),z=Ue("scale"),O=Ue("sepia"),F=Ue("skew"),$=Ue("space"),G=Ue("translate"),Z=()=>["auto","contain","none"],J=()=>["auto","hidden","clip","visible","scroll"],pe=()=>["auto",je,r],te=()=>[je,r],ge=()=>["",Kn,Sr],X=()=>["auto",$o,je],ve=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],se=()=>["solid","dashed","dotted","double","none"],ae=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],U=()=>["start","end","center","between","around","evenly","stretch"],V=()=>["","0",je],K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>[$o,je];return{cacheSize:500,separator:":",theme:{colors:[Ws],spacing:[Kn,Sr],blur:["none","",Cr,je],brightness:R(),borderColor:[n],borderRadius:["none","","full",Cr,je],borderSpacing:te(),borderWidth:ge(),contrast:R(),grayscale:V(),hueRotate:R(),invert:V(),gap:te(),gradientColorStops:[n],gradientColorStopPositions:[Lx,Sr],inset:pe(),margin:pe(),opacity:R(),padding:te(),saturate:R(),scale:R(),sepia:V(),skew:R(),space:te(),translate:te()},classGroups:{aspect:[{aspect:["auto","square","video",je]}],container:["container"],columns:[{columns:[Cr]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ve(),je]}],overflow:[{overflow:J()}],"overflow-x":[{"overflow-x":J()}],"overflow-y":[{"overflow-y":J()}],overscroll:[{overscroll:Z()}],"overscroll-x":[{"overscroll-x":Z()}],"overscroll-y":[{"overscroll-y":Z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[k]}],"inset-x":[{"inset-x":[k]}],"inset-y":[{"inset-y":[k]}],start:[{start:[k]}],end:[{end:[k]}],top:[{top:[k]}],right:[{right:[k]}],bottom:[{bottom:[k]}],left:[{left:[k]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Hs,je]}],basis:[{basis:pe()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",je]}],grow:[{grow:V()}],shrink:[{shrink:V()}],order:[{order:["first","last","none",Hs,je]}],"grid-cols":[{"grid-cols":[Ws]}],"col-start-end":[{col:["auto",{span:["full",Hs,je]},je]}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":[Ws]}],"row-start-end":[{row:["auto",{span:[Hs,je]},je]}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",je]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",je]}],gap:[{gap:[S]}],"gap-x":[{"gap-x":[S]}],"gap-y":[{"gap-y":[S]}],"justify-content":[{justify:["normal",...U()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...U(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...U(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[P]}],px:[{px:[P]}],py:[{py:[P]}],ps:[{ps:[P]}],pe:[{pe:[P]}],pt:[{pt:[P]}],pr:[{pr:[P]}],pb:[{pb:[P]}],pl:[{pl:[P]}],m:[{m:[j]}],mx:[{mx:[j]}],my:[{my:[j]}],ms:[{ms:[j]}],me:[{me:[j]}],mt:[{mt:[j]}],mr:[{mr:[j]}],mb:[{mb:[j]}],ml:[{ml:[j]}],"space-x":[{"space-x":[$]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[$]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",je,r]}],"min-w":[{"min-w":[je,r,"min","max","fit"]}],"max-w":[{"max-w":[je,r,"none","full","min","max","fit","prose",{screen:[Cr]},Cr]}],h:[{h:[je,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[je,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[je,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[je,r,"auto","min","max","fit"]}],"font-size":[{text:["base",Cr,Sr]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Qu]}],"font-family":[{font:[Ws]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",je]}],"line-clamp":[{"line-clamp":["none",$o,Qu]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Kn,je]}],"list-image":[{"list-image":["none",je]}],"list-style-type":[{list:["none","disc","decimal",je]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[n]}],"placeholder-opacity":[{"placeholder-opacity":[_]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[n]}],"text-opacity":[{"text-opacity":[_]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...se(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Kn,Sr]}],"underline-offset":[{"underline-offset":["auto",Kn,je]}],"text-decoration-color":[{decoration:[n]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:te()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",je]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",je]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[_]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ve(),$x]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ux]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Vx]}],"bg-color":[{bg:[n]}],"gradient-from-pos":[{from:[E]}],"gradient-via-pos":[{via:[E]}],"gradient-to-pos":[{to:[E]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[f]}],"rounded-s":[{"rounded-s":[f]}],"rounded-e":[{"rounded-e":[f]}],"rounded-t":[{"rounded-t":[f]}],"rounded-r":[{"rounded-r":[f]}],"rounded-b":[{"rounded-b":[f]}],"rounded-l":[{"rounded-l":[f]}],"rounded-ss":[{"rounded-ss":[f]}],"rounded-se":[{"rounded-se":[f]}],"rounded-ee":[{"rounded-ee":[f]}],"rounded-es":[{"rounded-es":[f]}],"rounded-tl":[{"rounded-tl":[f]}],"rounded-tr":[{"rounded-tr":[f]}],"rounded-br":[{"rounded-br":[f]}],"rounded-bl":[{"rounded-bl":[f]}],"border-w":[{border:[h]}],"border-w-x":[{"border-x":[h]}],"border-w-y":[{"border-y":[h]}],"border-w-s":[{"border-s":[h]}],"border-w-e":[{"border-e":[h]}],"border-w-t":[{"border-t":[h]}],"border-w-r":[{"border-r":[h]}],"border-w-b":[{"border-b":[h]}],"border-w-l":[{"border-l":[h]}],"border-opacity":[{"border-opacity":[_]}],"border-style":[{border:[...se(),"hidden"]}],"divide-x":[{"divide-x":[h]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[h]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[_]}],"divide-style":[{divide:se()}],"border-color":[{border:[c]}],"border-color-x":[{"border-x":[c]}],"border-color-y":[{"border-y":[c]}],"border-color-s":[{"border-s":[c]}],"border-color-e":[{"border-e":[c]}],"border-color-t":[{"border-t":[c]}],"border-color-r":[{"border-r":[c]}],"border-color-b":[{"border-b":[c]}],"border-color-l":[{"border-l":[c]}],"divide-color":[{divide:[c]}],"outline-style":[{outline:["",...se()]}],"outline-offset":[{"outline-offset":[Kn,je]}],"outline-w":[{outline:[Kn,Sr]}],"outline-color":[{outline:[n]}],"ring-w":[{ring:ge()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[n]}],"ring-opacity":[{"ring-opacity":[_]}],"ring-offset-w":[{"ring-offset":[Kn,Sr]}],"ring-offset-color":[{"ring-offset":[n]}],shadow:[{shadow:["","inner","none",Cr,Hx]}],"shadow-color":[{shadow:[Ws]}],opacity:[{opacity:[_]}],"mix-blend":[{"mix-blend":[...ae(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ae()}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[l]}],contrast:[{contrast:[g]}],"drop-shadow":[{"drop-shadow":["","none",Cr,je]}],grayscale:[{grayscale:[v]}],"hue-rotate":[{"hue-rotate":[x]}],invert:[{invert:[b]}],saturate:[{saturate:[A]}],sepia:[{sepia:[O]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[l]}],"backdrop-contrast":[{"backdrop-contrast":[g]}],"backdrop-grayscale":[{"backdrop-grayscale":[v]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x]}],"backdrop-invert":[{"backdrop-invert":[b]}],"backdrop-opacity":[{"backdrop-opacity":[_]}],"backdrop-saturate":[{"backdrop-saturate":[A]}],"backdrop-sepia":[{"backdrop-sepia":[O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[p]}],"border-spacing-x":[{"border-spacing-x":[p]}],"border-spacing-y":[{"border-spacing-y":[p]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",je]}],duration:[{duration:R()}],ease:[{ease:["linear","in","out","in-out",je]}],delay:[{delay:R()}],animate:[{animate:["none","spin","ping","pulse","bounce",je]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[z]}],"scale-x":[{"scale-x":[z]}],"scale-y":[{"scale-y":[z]}],rotate:[{rotate:[Hs,je]}],"translate-x":[{"translate-x":[G]}],"translate-y":[{"translate-y":[G]}],"skew-x":[{"skew-x":[F]}],"skew-y":[{"skew-y":[F]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",je]}],accent:[{accent:["auto",n]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",je]}],"caret-color":[{caret:[n]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":te()}],"scroll-mx":[{"scroll-mx":te()}],"scroll-my":[{"scroll-my":te()}],"scroll-ms":[{"scroll-ms":te()}],"scroll-me":[{"scroll-me":te()}],"scroll-mt":[{"scroll-mt":te()}],"scroll-mr":[{"scroll-mr":te()}],"scroll-mb":[{"scroll-mb":te()}],"scroll-ml":[{"scroll-ml":te()}],"scroll-p":[{"scroll-p":te()}],"scroll-px":[{"scroll-px":te()}],"scroll-py":[{"scroll-py":te()}],"scroll-ps":[{"scroll-ps":te()}],"scroll-pe":[{"scroll-pe":te()}],"scroll-pt":[{"scroll-pt":te()}],"scroll-pr":[{"scroll-pr":te()}],"scroll-pb":[{"scroll-pb":te()}],"scroll-pl":[{"scroll-pl":te()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",je]}],fill:[{fill:[n,"none"]}],"stroke-w":[{stroke:[Kn,Sr,Qu]}],stroke:[{stroke:[n,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Gx=Rx(qx);function Se(...n){return Gx(tm(n))}const Yx=Hy,fm=w.forwardRef(({className:n,...r},s)=>a.jsx(qh,{ref:s,className:Se("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n),...r}));fm.displayName=qh.displayName;const Xx=wc("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),pm=w.forwardRef(({className:n,variant:r,...s},l)=>a.jsx(Gh,{ref:l,className:Se(Xx({variant:r}),n),...s}));pm.displayName=Gh.displayName;const Zx=w.forwardRef(({className:n,...r},s)=>a.jsx(Zh,{ref:s,className:Se("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),...r}));Zx.displayName=Zh.displayName;const hm=w.forwardRef(({className:n,...r},s)=>a.jsx(Jh,{ref:s,className:Se("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...r,children:a.jsx(vx,{className:"h-4 w-4"})}));hm.displayName=Jh.displayName;const mm=w.forwardRef(({className:n,...r},s)=>a.jsx(Yh,{ref:s,className:Se("text-sm font-semibold",n),...r}));mm.displayName=Yh.displayName;const gm=w.forwardRef(({className:n,...r},s)=>a.jsx(Xh,{ref:s,className:Se("text-sm opacity-90",n),...r}));gm.displayName=Xh.displayName;function Jx(){const{toasts:n}=Jv();return a.jsxs(Yx,{children:[n.map(function({id:r,title:s,description:l,action:c,...f}){return a.jsxs(pm,{...f,children:[a.jsxs("div",{className:"grid gap-1",children:[s&&a.jsx(mm,{children:s}),l&&a.jsx(gm,{children:l})]}),c,a.jsx(hm,{})]},r)}),a.jsx(fm,{})]})}var Ip=["light","dark"],ew="(prefers-color-scheme: dark)",tw=w.createContext(void 0),nw={setTheme:n=>{},themes:[]},rw=()=>{var n;return(n=w.useContext(tw))!=null?n:nw};w.memo(({forcedTheme:n,storageKey:r,attribute:s,enableSystem:l,enableColorScheme:c,defaultTheme:f,value:p,attrs:h,nonce:g})=>{let v=f==="system",x=s==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${h.map(E=>`'${E}'`).join(",")})`};`:`var d=document.documentElement,n='${s}',s='setAttribute';`,b=c?Ip.includes(f)&&f?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${f}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",S=(E,k=!1,j=!0)=>{let _=p?p[E]:E,P=k?E+"|| ''":`'${_}'`,A="";return c&&j&&!k&&Ip.includes(E)&&(A+=`d.style.colorScheme = '${E}';`),s==="class"?k||_?A+=`c.add(${P})`:A+="null":_&&(A+=`d[s](n,${P})`),A},y=n?`!function(){${x}${S(n)}}()`:l?`!function(){try{${x}var e=localStorage.getItem('${r}');if('system'===e||(!e&&${v})){var t='${ew}',m=window.matchMedia(t);if(m.media!==t||m.matches){${S("dark")}}else{${S("light")}}}else if(e){${p?`var x=${JSON.stringify(p)};`:""}${S(p?"x[e]":"e",!0)}}${v?"":"else{"+S(f,!1,!1)+"}"}${b}}catch(e){}}()`:`!function(){try{${x}var e=localStorage.getItem('${r}');if(e){${p?`var x=${JSON.stringify(p)};`:""}${S(p?"x[e]":"e",!0)}}else{${S(f,!1,!1)};}${b}}catch(t){}}();`;return w.createElement("script",{nonce:g,dangerouslySetInnerHTML:{__html:y}})});var ow=n=>{switch(n){case"success":return lw;case"info":return uw;case"warning":return aw;case"error":return cw;default:return null}},sw=Array(12).fill(0),iw=({visible:n,className:r})=>Y.createElement("div",{className:["sonner-loading-wrapper",r].filter(Boolean).join(" "),"data-visible":n},Y.createElement("div",{className:"sonner-spinner"},sw.map((s,l)=>Y.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${l}`})))),lw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),aw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),uw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),cw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},Y.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),dw=Y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},Y.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),Y.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),fw=()=>{let[n,r]=Y.useState(document.hidden);return Y.useEffect(()=>{let s=()=>{r(document.hidden)};return document.addEventListener("visibilitychange",s),()=>window.removeEventListener("visibilitychange",s)},[]),n},rc=1,pw=class{constructor(){this.subscribe=n=>(this.subscribers.push(n),()=>{let r=this.subscribers.indexOf(n);this.subscribers.splice(r,1)}),this.publish=n=>{this.subscribers.forEach(r=>r(n))},this.addToast=n=>{this.publish(n),this.toasts=[...this.toasts,n]},this.create=n=>{var r;let{message:s,...l}=n,c=typeof(n==null?void 0:n.id)=="number"||((r=n.id)==null?void 0:r.length)>0?n.id:rc++,f=this.toasts.find(h=>h.id===c),p=n.dismissible===void 0?!0:n.dismissible;return this.dismissedToasts.has(c)&&this.dismissedToasts.delete(c),f?this.toasts=this.toasts.map(h=>h.id===c?(this.publish({...h,...n,id:c,title:s}),{...h,...n,id:c,dismissible:p,title:s}):h):this.addToast({title:s,...l,dismissible:p,id:c}),c},this.dismiss=n=>(this.dismissedToasts.add(n),n||this.toasts.forEach(r=>{this.subscribers.forEach(s=>s({id:r.id,dismiss:!0}))}),this.subscribers.forEach(r=>r({id:n,dismiss:!0})),n),this.message=(n,r)=>this.create({...r,message:n}),this.error=(n,r)=>this.create({...r,message:n,type:"error"}),this.success=(n,r)=>this.create({...r,type:"success",message:n}),this.info=(n,r)=>this.create({...r,type:"info",message:n}),this.warning=(n,r)=>this.create({...r,type:"warning",message:n}),this.loading=(n,r)=>this.create({...r,type:"loading",message:n}),this.promise=(n,r)=>{if(!r)return;let s;r.loading!==void 0&&(s=this.create({...r,promise:n,type:"loading",message:r.loading,description:typeof r.description!="function"?r.description:void 0}));let l=n instanceof Promise?n:n(),c=s!==void 0,f,p=l.then(async g=>{if(f=["resolve",g],Y.isValidElement(g))c=!1,this.create({id:s,type:"default",message:g});else if(mw(g)&&!g.ok){c=!1;let v=typeof r.error=="function"?await r.error(`HTTP error! status: ${g.status}`):r.error,x=typeof r.description=="function"?await r.description(`HTTP error! status: ${g.status}`):r.description;this.create({id:s,type:"error",message:v,description:x})}else if(r.success!==void 0){c=!1;let v=typeof r.success=="function"?await r.success(g):r.success,x=typeof r.description=="function"?await r.description(g):r.description;this.create({id:s,type:"success",message:v,description:x})}}).catch(async g=>{if(f=["reject",g],r.error!==void 0){c=!1;let v=typeof r.error=="function"?await r.error(g):r.error,x=typeof r.description=="function"?await r.description(g):r.description;this.create({id:s,type:"error",message:v,description:x})}}).finally(()=>{var g;c&&(this.dismiss(s),s=void 0),(g=r.finally)==null||g.call(r)}),h=()=>new Promise((g,v)=>p.then(()=>f[0]==="reject"?v(f[1]):g(f[1])).catch(v));return typeof s!="string"&&typeof s!="number"?{unwrap:h}:Object.assign(s,{unwrap:h})},this.custom=(n,r)=>{let s=(r==null?void 0:r.id)||rc++;return this.create({jsx:n(s),id:s,...r}),s},this.getActiveToasts=()=>this.toasts.filter(n=>!this.dismissedToasts.has(n.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Tt=new pw,hw=(n,r)=>{let s=(r==null?void 0:r.id)||rc++;return Tt.addToast({title:n,...r,id:s}),s},mw=n=>n&&typeof n=="object"&&"ok"in n&&typeof n.ok=="boolean"&&"status"in n&&typeof n.status=="number",gw=hw,vw=()=>Tt.toasts,yw=()=>Tt.getActiveToasts();Object.assign(gw,{success:Tt.success,info:Tt.info,warning:Tt.warning,error:Tt.error,custom:Tt.custom,message:Tt.message,promise:Tt.promise,dismiss:Tt.dismiss,loading:Tt.loading},{getHistory:vw,getToasts:yw});function xw(n,{insertAt:r}={}){if(typeof document>"u")return;let s=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.type="text/css",r==="top"&&s.firstChild?s.insertBefore(l,s.firstChild):s.appendChild(l),l.styleSheet?l.styleSheet.cssText=n:l.appendChild(document.createTextNode(n))}xw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Nl(n){return n.label!==void 0}var ww=3,bw="32px",jw="16px",Fp=4e3,kw=356,Nw=14,Sw=20,Cw=200;function an(...n){return n.filter(Boolean).join(" ")}function Ew(n){let[r,s]=n.split("-"),l=[];return r&&l.push(r),s&&l.push(s),l}var Pw=n=>{var r,s,l,c,f,p,h,g,v,x,b;let{invert:S,toast:y,unstyled:E,interacting:k,setHeights:j,visibleToasts:_,heights:P,index:A,toasts:z,expanded:O,removeToast:F,defaultRichColors:$,closeButton:G,style:Z,cancelButtonStyle:J,actionButtonStyle:pe,className:te="",descriptionClassName:ge="",duration:X,position:ve,gap:se,loadingIcon:ae,expandByDefault:U,classNames:V,icons:K,closeButtonAriaLabel:R="Close toast",pauseWhenPageIsHidden:L}=n,[ee,re]=Y.useState(null),[he,be]=Y.useState(null),[ce,Ce]=Y.useState(!1),[Pe,Ge]=Y.useState(!1),[_t,Fn]=Y.useState(!1),[At,nr]=Y.useState(!1),[fo,Ur]=Y.useState(!1),[$r,rr]=Y.useState(0),[vn,or]=Y.useState(0),Ut=Y.useRef(y.duration||X||Fp),po=Y.useRef(null),yn=Y.useRef(null),li=A===0,ai=A+1<=_,mt=y.type,xn=y.dismissible!==!1,ho=y.className||"",ui=y.descriptionClassName||"",wn=Y.useMemo(()=>P.findIndex(xe=>xe.toastId===y.id)||0,[P,y.id]),Br=Y.useMemo(()=>{var xe;return(xe=y.closeButton)!=null?xe:G},[y.closeButton,G]),ci=Y.useMemo(()=>y.duration||X||Fp,[y.duration,X]),mo=Y.useRef(0),Ln=Y.useRef(0),di=Y.useRef(0),bn=Y.useRef(null),[ls,as]=ve.split("-"),go=Y.useMemo(()=>P.reduce((xe,Ae,Ie)=>Ie>=wn?xe:xe+Ae.height,0),[P,wn]),vo=fw(),sr=y.invert||S,jn=mt==="loading";Ln.current=Y.useMemo(()=>wn*se+go,[wn,go]),Y.useEffect(()=>{Ut.current=ci},[ci]),Y.useEffect(()=>{Ce(!0)},[]),Y.useEffect(()=>{let xe=yn.current;if(xe){let Ae=xe.getBoundingClientRect().height;return or(Ae),j(Ie=>[{toastId:y.id,height:Ae,position:y.position},...Ie]),()=>j(Ie=>Ie.filter(gt=>gt.toastId!==y.id))}},[j,y.id]),Y.useLayoutEffect(()=>{if(!ce)return;let xe=yn.current,Ae=xe.style.height;xe.style.height="auto";let Ie=xe.getBoundingClientRect().height;xe.style.height=Ae,or(Ie),j(gt=>gt.find(bt=>bt.toastId===y.id)?gt.map(bt=>bt.toastId===y.id?{...bt,height:Ie}:bt):[{toastId:y.id,height:Ie,position:y.position},...gt])},[ce,y.title,y.description,j,y.id]);let Zt=Y.useCallback(()=>{Ge(!0),rr(Ln.current),j(xe=>xe.filter(Ae=>Ae.toastId!==y.id)),setTimeout(()=>{F(y)},Cw)},[y,F,j,Ln]);Y.useEffect(()=>{if(y.promise&&mt==="loading"||y.duration===1/0||y.type==="loading")return;let xe;return O||k||L&&vo?(()=>{if(di.current<mo.current){let Ae=new Date().getTime()-mo.current;Ut.current=Ut.current-Ae}di.current=new Date().getTime()})():Ut.current!==1/0&&(mo.current=new Date().getTime(),xe=setTimeout(()=>{var Ae;(Ae=y.onAutoClose)==null||Ae.call(y,y),Zt()},Ut.current)),()=>clearTimeout(xe)},[O,k,y,mt,L,vo,Zt]),Y.useEffect(()=>{y.delete&&Zt()},[Zt,y.delete]);function fi(){var xe,Ae,Ie;return K!=null&&K.loading?Y.createElement("div",{className:an(V==null?void 0:V.loader,(xe=y==null?void 0:y.classNames)==null?void 0:xe.loader,"sonner-loader"),"data-visible":mt==="loading"},K.loading):ae?Y.createElement("div",{className:an(V==null?void 0:V.loader,(Ae=y==null?void 0:y.classNames)==null?void 0:Ae.loader,"sonner-loader"),"data-visible":mt==="loading"},ae):Y.createElement(iw,{className:an(V==null?void 0:V.loader,(Ie=y==null?void 0:y.classNames)==null?void 0:Ie.loader),visible:mt==="loading"})}return Y.createElement("li",{tabIndex:0,ref:yn,className:an(te,ho,V==null?void 0:V.toast,(r=y==null?void 0:y.classNames)==null?void 0:r.toast,V==null?void 0:V.default,V==null?void 0:V[mt],(s=y==null?void 0:y.classNames)==null?void 0:s[mt]),"data-sonner-toast":"","data-rich-colors":(l=y.richColors)!=null?l:$,"data-styled":!(y.jsx||y.unstyled||E),"data-mounted":ce,"data-promise":!!y.promise,"data-swiped":fo,"data-removed":Pe,"data-visible":ai,"data-y-position":ls,"data-x-position":as,"data-index":A,"data-front":li,"data-swiping":_t,"data-dismissible":xn,"data-type":mt,"data-invert":sr,"data-swipe-out":At,"data-swipe-direction":he,"data-expanded":!!(O||U&&ce),style:{"--index":A,"--toasts-before":A,"--z-index":z.length-A,"--offset":`${Pe?$r:Ln.current}px`,"--initial-height":U?"auto":`${vn}px`,...Z,...y.style},onDragEnd:()=>{Fn(!1),re(null),bn.current=null},onPointerDown:xe=>{jn||!xn||(po.current=new Date,rr(Ln.current),xe.target.setPointerCapture(xe.pointerId),xe.target.tagName!=="BUTTON"&&(Fn(!0),bn.current={x:xe.clientX,y:xe.clientY}))},onPointerUp:()=>{var xe,Ae,Ie,gt;if(At||!xn)return;bn.current=null;let bt=Number(((xe=yn.current)==null?void 0:xe.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),jt=Number(((Ae=yn.current)==null?void 0:Ae.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Jt=new Date().getTime()-((Ie=po.current)==null?void 0:Ie.getTime()),st=ee==="x"?bt:jt,kn=Math.abs(st)/Jt;if(Math.abs(st)>=Sw||kn>.11){rr(Ln.current),(gt=y.onDismiss)==null||gt.call(y,y),be(ee==="x"?bt>0?"right":"left":jt>0?"down":"up"),Zt(),nr(!0),Ur(!1);return}Fn(!1),re(null)},onPointerMove:xe=>{var Ae,Ie,gt,bt;if(!bn.current||!xn||((Ae=window.getSelection())==null?void 0:Ae.toString().length)>0)return;let jt=xe.clientY-bn.current.y,Jt=xe.clientX-bn.current.x,st=(Ie=n.swipeDirections)!=null?Ie:Ew(ve);!ee&&(Math.abs(Jt)>1||Math.abs(jt)>1)&&re(Math.abs(Jt)>Math.abs(jt)?"x":"y");let kn={x:0,y:0};ee==="y"?(st.includes("top")||st.includes("bottom"))&&(st.includes("top")&&jt<0||st.includes("bottom")&&jt>0)&&(kn.y=jt):ee==="x"&&(st.includes("left")||st.includes("right"))&&(st.includes("left")&&Jt<0||st.includes("right")&&Jt>0)&&(kn.x=Jt),(Math.abs(kn.x)>0||Math.abs(kn.y)>0)&&Ur(!0),(gt=yn.current)==null||gt.style.setProperty("--swipe-amount-x",`${kn.x}px`),(bt=yn.current)==null||bt.style.setProperty("--swipe-amount-y",`${kn.y}px`)}},Br&&!y.jsx?Y.createElement("button",{"aria-label":R,"data-disabled":jn,"data-close-button":!0,onClick:jn||!xn?()=>{}:()=>{var xe;Zt(),(xe=y.onDismiss)==null||xe.call(y,y)},className:an(V==null?void 0:V.closeButton,(c=y==null?void 0:y.classNames)==null?void 0:c.closeButton)},(f=K==null?void 0:K.close)!=null?f:dw):null,y.jsx||w.isValidElement(y.title)?y.jsx?y.jsx:typeof y.title=="function"?y.title():y.title:Y.createElement(Y.Fragment,null,mt||y.icon||y.promise?Y.createElement("div",{"data-icon":"",className:an(V==null?void 0:V.icon,(p=y==null?void 0:y.classNames)==null?void 0:p.icon)},y.promise||y.type==="loading"&&!y.icon?y.icon||fi():null,y.type!=="loading"?y.icon||(K==null?void 0:K[mt])||ow(mt):null):null,Y.createElement("div",{"data-content":"",className:an(V==null?void 0:V.content,(h=y==null?void 0:y.classNames)==null?void 0:h.content)},Y.createElement("div",{"data-title":"",className:an(V==null?void 0:V.title,(g=y==null?void 0:y.classNames)==null?void 0:g.title)},typeof y.title=="function"?y.title():y.title),y.description?Y.createElement("div",{"data-description":"",className:an(ge,ui,V==null?void 0:V.description,(v=y==null?void 0:y.classNames)==null?void 0:v.description)},typeof y.description=="function"?y.description():y.description):null),w.isValidElement(y.cancel)?y.cancel:y.cancel&&Nl(y.cancel)?Y.createElement("button",{"data-button":!0,"data-cancel":!0,style:y.cancelButtonStyle||J,onClick:xe=>{var Ae,Ie;Nl(y.cancel)&&xn&&((Ie=(Ae=y.cancel).onClick)==null||Ie.call(Ae,xe),Zt())},className:an(V==null?void 0:V.cancelButton,(x=y==null?void 0:y.classNames)==null?void 0:x.cancelButton)},y.cancel.label):null,w.isValidElement(y.action)?y.action:y.action&&Nl(y.action)?Y.createElement("button",{"data-button":!0,"data-action":!0,style:y.actionButtonStyle||pe,onClick:xe=>{var Ae,Ie;Nl(y.action)&&((Ie=(Ae=y.action).onClick)==null||Ie.call(Ae,xe),!xe.defaultPrevented&&Zt())},className:an(V==null?void 0:V.actionButton,(b=y==null?void 0:y.classNames)==null?void 0:b.actionButton)},y.action.label):null))};function Lp(){if(typeof window>"u"||typeof document>"u")return"ltr";let n=document.documentElement.getAttribute("dir");return n==="auto"||!n?window.getComputedStyle(document.documentElement).direction:n}function Tw(n,r){let s={};return[n,r].forEach((l,c)=>{let f=c===1,p=f?"--mobile-offset":"--offset",h=f?jw:bw;function g(v){["top","right","bottom","left"].forEach(x=>{s[`${p}-${x}`]=typeof v=="number"?`${v}px`:v})}typeof l=="number"||typeof l=="string"?g(l):typeof l=="object"?["top","right","bottom","left"].forEach(v=>{l[v]===void 0?s[`${p}-${v}`]=h:s[`${p}-${v}`]=typeof l[v]=="number"?`${l[v]}px`:l[v]}):g(h)}),s}var Rw=w.forwardRef(function(n,r){let{invert:s,position:l="bottom-right",hotkey:c=["altKey","KeyT"],expand:f,closeButton:p,className:h,offset:g,mobileOffset:v,theme:x="light",richColors:b,duration:S,style:y,visibleToasts:E=ww,toastOptions:k,dir:j=Lp(),gap:_=Nw,loadingIcon:P,icons:A,containerAriaLabel:z="Notifications",pauseWhenPageIsHidden:O}=n,[F,$]=Y.useState([]),G=Y.useMemo(()=>Array.from(new Set([l].concat(F.filter(L=>L.position).map(L=>L.position)))),[F,l]),[Z,J]=Y.useState([]),[pe,te]=Y.useState(!1),[ge,X]=Y.useState(!1),[ve,se]=Y.useState(x!=="system"?x:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),ae=Y.useRef(null),U=c.join("+").replace(/Key/g,"").replace(/Digit/g,""),V=Y.useRef(null),K=Y.useRef(!1),R=Y.useCallback(L=>{$(ee=>{var re;return(re=ee.find(he=>he.id===L.id))!=null&&re.delete||Tt.dismiss(L.id),ee.filter(({id:he})=>he!==L.id)})},[]);return Y.useEffect(()=>Tt.subscribe(L=>{if(L.dismiss){$(ee=>ee.map(re=>re.id===L.id?{...re,delete:!0}:re));return}setTimeout(()=>{Ch.flushSync(()=>{$(ee=>{let re=ee.findIndex(he=>he.id===L.id);return re!==-1?[...ee.slice(0,re),{...ee[re],...L},...ee.slice(re+1)]:[L,...ee]})})})}),[]),Y.useEffect(()=>{if(x!=="system"){se(x);return}if(x==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?se("dark"):se("light")),typeof window>"u")return;let L=window.matchMedia("(prefers-color-scheme: dark)");try{L.addEventListener("change",({matches:ee})=>{se(ee?"dark":"light")})}catch{L.addListener(({matches:re})=>{try{se(re?"dark":"light")}catch(he){console.error(he)}})}},[x]),Y.useEffect(()=>{F.length<=1&&te(!1)},[F]),Y.useEffect(()=>{let L=ee=>{var re,he;c.every(be=>ee[be]||ee.code===be)&&(te(!0),(re=ae.current)==null||re.focus()),ee.code==="Escape"&&(document.activeElement===ae.current||(he=ae.current)!=null&&he.contains(document.activeElement))&&te(!1)};return document.addEventListener("keydown",L),()=>document.removeEventListener("keydown",L)},[c]),Y.useEffect(()=>{if(ae.current)return()=>{V.current&&(V.current.focus({preventScroll:!0}),V.current=null,K.current=!1)}},[ae.current]),Y.createElement("section",{ref:r,"aria-label":`${z} ${U}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},G.map((L,ee)=>{var re;let[he,be]=L.split("-");return F.length?Y.createElement("ol",{key:L,dir:j==="auto"?Lp():j,tabIndex:-1,ref:ae,className:h,"data-sonner-toaster":!0,"data-theme":ve,"data-y-position":he,"data-lifted":pe&&F.length>1&&!f,"data-x-position":be,style:{"--front-toast-height":`${((re=Z[0])==null?void 0:re.height)||0}px`,"--width":`${kw}px`,"--gap":`${_}px`,...y,...Tw(g,v)},onBlur:ce=>{K.current&&!ce.currentTarget.contains(ce.relatedTarget)&&(K.current=!1,V.current&&(V.current.focus({preventScroll:!0}),V.current=null))},onFocus:ce=>{ce.target instanceof HTMLElement&&ce.target.dataset.dismissible==="false"||K.current||(K.current=!0,V.current=ce.relatedTarget)},onMouseEnter:()=>te(!0),onMouseMove:()=>te(!0),onMouseLeave:()=>{ge||te(!1)},onDragEnd:()=>te(!1),onPointerDown:ce=>{ce.target instanceof HTMLElement&&ce.target.dataset.dismissible==="false"||X(!0)},onPointerUp:()=>X(!1)},F.filter(ce=>!ce.position&&ee===0||ce.position===L).map((ce,Ce)=>{var Pe,Ge;return Y.createElement(Pw,{key:ce.id,icons:A,index:Ce,toast:ce,defaultRichColors:b,duration:(Pe=k==null?void 0:k.duration)!=null?Pe:S,className:k==null?void 0:k.className,descriptionClassName:k==null?void 0:k.descriptionClassName,invert:s,visibleToasts:E,closeButton:(Ge=k==null?void 0:k.closeButton)!=null?Ge:p,interacting:ge,position:L,style:k==null?void 0:k.style,unstyled:k==null?void 0:k.unstyled,classNames:k==null?void 0:k.classNames,cancelButtonStyle:k==null?void 0:k.cancelButtonStyle,actionButtonStyle:k==null?void 0:k.actionButtonStyle,removeToast:R,toasts:F.filter(_t=>_t.position==ce.position),heights:Z.filter(_t=>_t.position==ce.position),setHeights:J,expandByDefault:f,gap:_,loadingIcon:P,expanded:pe,pauseWhenPageIsHidden:O,swipeDirections:n.swipeDirections})})):null}))});const _w=({...n})=>{const{theme:r="system"}=rw();return a.jsx(Rw,{theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...n})};var Aw=mc[" useId ".trim().toString()]||(()=>{}),Ow=0;function Mw(n){const[r,s]=w.useState(Aw());return Jn(()=>{s(l=>l??String(Ow++))},[n]),r?`radix-${r}`:""}const Dw=["top","right","bottom","left"],Fr=Math.min,Lt=Math.max,Al=Math.round,Sl=Math.floor,Mn=n=>({x:n,y:n}),Iw={left:"right",right:"left",bottom:"top",top:"bottom"},Fw={start:"end",end:"start"};function oc(n,r,s){return Lt(n,Fr(r,s))}function er(n,r){return typeof n=="function"?n(r):n}function tr(n){return n.split("-")[0]}function ss(n){return n.split("-")[1]}function kc(n){return n==="x"?"y":"x"}function Nc(n){return n==="y"?"height":"width"}const Lw=new Set(["top","bottom"]);function On(n){return Lw.has(tr(n))?"y":"x"}function Sc(n){return kc(On(n))}function zw(n,r,s){s===void 0&&(s=!1);const l=ss(n),c=Sc(n),f=Nc(c);let p=c==="x"?l===(s?"end":"start")?"right":"left":l==="start"?"bottom":"top";return r.reference[f]>r.floating[f]&&(p=Ol(p)),[p,Ol(p)]}function Uw(n){const r=Ol(n);return[sc(n),r,sc(r)]}function sc(n){return n.replace(/start|end/g,r=>Fw[r])}const zp=["left","right"],Up=["right","left"],$w=["top","bottom"],Bw=["bottom","top"];function Vw(n,r,s){switch(n){case"top":case"bottom":return s?r?Up:zp:r?zp:Up;case"left":case"right":return r?$w:Bw;default:return[]}}function Hw(n,r,s,l){const c=ss(n);let f=Vw(tr(n),s==="start",l);return c&&(f=f.map(p=>p+"-"+c),r&&(f=f.concat(f.map(sc)))),f}function Ol(n){return n.replace(/left|right|bottom|top/g,r=>Iw[r])}function Ww(n){return{top:0,right:0,bottom:0,left:0,...n}}function vm(n){return typeof n!="number"?Ww(n):{top:n,right:n,bottom:n,left:n}}function Ml(n){const{x:r,y:s,width:l,height:c}=n;return{width:l,height:c,top:s,left:r,right:r+l,bottom:s+c,x:r,y:s}}function $p(n,r,s){let{reference:l,floating:c}=n;const f=On(r),p=Sc(r),h=Nc(p),g=tr(r),v=f==="y",x=l.x+l.width/2-c.width/2,b=l.y+l.height/2-c.height/2,S=l[h]/2-c[h]/2;let y;switch(g){case"top":y={x,y:l.y-c.height};break;case"bottom":y={x,y:l.y+l.height};break;case"right":y={x:l.x+l.width,y:b};break;case"left":y={x:l.x-c.width,y:b};break;default:y={x:l.x,y:l.y}}switch(ss(r)){case"start":y[p]-=S*(s&&v?-1:1);break;case"end":y[p]+=S*(s&&v?-1:1);break}return y}const Qw=async(n,r,s)=>{const{placement:l="bottom",strategy:c="absolute",middleware:f=[],platform:p}=s,h=f.filter(Boolean),g=await(p.isRTL==null?void 0:p.isRTL(r));let v=await p.getElementRects({reference:n,floating:r,strategy:c}),{x,y:b}=$p(v,l,g),S=l,y={},E=0;for(let k=0;k<h.length;k++){const{name:j,fn:_}=h[k],{x:P,y:A,data:z,reset:O}=await _({x,y:b,initialPlacement:l,placement:S,strategy:c,middlewareData:y,rects:v,platform:p,elements:{reference:n,floating:r}});x=P??x,b=A??b,y={...y,[j]:{...y[j],...z}},O&&E<=50&&(E++,typeof O=="object"&&(O.placement&&(S=O.placement),O.rects&&(v=O.rects===!0?await p.getElementRects({reference:n,floating:r,strategy:c}):O.rects),{x,y:b}=$p(v,S,g)),k=-1)}return{x,y:b,placement:S,strategy:c,middlewareData:y}};async function Ys(n,r){var s;r===void 0&&(r={});const{x:l,y:c,platform:f,rects:p,elements:h,strategy:g}=n,{boundary:v="clippingAncestors",rootBoundary:x="viewport",elementContext:b="floating",altBoundary:S=!1,padding:y=0}=er(r,n),E=vm(y),j=h[S?b==="floating"?"reference":"floating":b],_=Ml(await f.getClippingRect({element:(s=await(f.isElement==null?void 0:f.isElement(j)))==null||s?j:j.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:v,rootBoundary:x,strategy:g})),P=b==="floating"?{x:l,y:c,width:p.floating.width,height:p.floating.height}:p.reference,A=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),z=await(f.isElement==null?void 0:f.isElement(A))?await(f.getScale==null?void 0:f.getScale(A))||{x:1,y:1}:{x:1,y:1},O=Ml(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:P,offsetParent:A,strategy:g}):P);return{top:(_.top-O.top+E.top)/z.y,bottom:(O.bottom-_.bottom+E.bottom)/z.y,left:(_.left-O.left+E.left)/z.x,right:(O.right-_.right+E.right)/z.x}}const Kw=n=>({name:"arrow",options:n,async fn(r){const{x:s,y:l,placement:c,rects:f,platform:p,elements:h,middlewareData:g}=r,{element:v,padding:x=0}=er(n,r)||{};if(v==null)return{};const b=vm(x),S={x:s,y:l},y=Sc(c),E=Nc(y),k=await p.getDimensions(v),j=y==="y",_=j?"top":"left",P=j?"bottom":"right",A=j?"clientHeight":"clientWidth",z=f.reference[E]+f.reference[y]-S[y]-f.floating[E],O=S[y]-f.reference[y],F=await(p.getOffsetParent==null?void 0:p.getOffsetParent(v));let $=F?F[A]:0;(!$||!await(p.isElement==null?void 0:p.isElement(F)))&&($=h.floating[A]||f.floating[E]);const G=z/2-O/2,Z=$/2-k[E]/2-1,J=Fr(b[_],Z),pe=Fr(b[P],Z),te=J,ge=$-k[E]-pe,X=$/2-k[E]/2+G,ve=oc(te,X,ge),se=!g.arrow&&ss(c)!=null&&X!==ve&&f.reference[E]/2-(X<te?J:pe)-k[E]/2<0,ae=se?X<te?X-te:X-ge:0;return{[y]:S[y]+ae,data:{[y]:ve,centerOffset:X-ve-ae,...se&&{alignmentOffset:ae}},reset:se}}}),qw=function(n){return n===void 0&&(n={}),{name:"flip",options:n,async fn(r){var s,l;const{placement:c,middlewareData:f,rects:p,initialPlacement:h,platform:g,elements:v}=r,{mainAxis:x=!0,crossAxis:b=!0,fallbackPlacements:S,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:k=!0,...j}=er(n,r);if((s=f.arrow)!=null&&s.alignmentOffset)return{};const _=tr(c),P=On(h),A=tr(h)===h,z=await(g.isRTL==null?void 0:g.isRTL(v.floating)),O=S||(A||!k?[Ol(h)]:Uw(h)),F=E!=="none";!S&&F&&O.push(...Hw(h,k,E,z));const $=[h,...O],G=await Ys(r,j),Z=[];let J=((l=f.flip)==null?void 0:l.overflows)||[];if(x&&Z.push(G[_]),b){const X=zw(c,p,z);Z.push(G[X[0]],G[X[1]])}if(J=[...J,{placement:c,overflows:Z}],!Z.every(X=>X<=0)){var pe,te;const X=(((pe=f.flip)==null?void 0:pe.index)||0)+1,ve=$[X];if(ve&&(!(b==="alignment"?P!==On(ve):!1)||J.every(U=>U.overflows[0]>0&&On(U.placement)===P)))return{data:{index:X,overflows:J},reset:{placement:ve}};let se=(te=J.filter(ae=>ae.overflows[0]<=0).sort((ae,U)=>ae.overflows[1]-U.overflows[1])[0])==null?void 0:te.placement;if(!se)switch(y){case"bestFit":{var ge;const ae=(ge=J.filter(U=>{if(F){const V=On(U.placement);return V===P||V==="y"}return!0}).map(U=>[U.placement,U.overflows.filter(V=>V>0).reduce((V,K)=>V+K,0)]).sort((U,V)=>U[1]-V[1])[0])==null?void 0:ge[0];ae&&(se=ae);break}case"initialPlacement":se=h;break}if(c!==se)return{reset:{placement:se}}}return{}}}};function Bp(n,r){return{top:n.top-r.height,right:n.right-r.width,bottom:n.bottom-r.height,left:n.left-r.width}}function Vp(n){return Dw.some(r=>n[r]>=0)}const Gw=function(n){return n===void 0&&(n={}),{name:"hide",options:n,async fn(r){const{rects:s}=r,{strategy:l="referenceHidden",...c}=er(n,r);switch(l){case"referenceHidden":{const f=await Ys(r,{...c,elementContext:"reference"}),p=Bp(f,s.reference);return{data:{referenceHiddenOffsets:p,referenceHidden:Vp(p)}}}case"escaped":{const f=await Ys(r,{...c,altBoundary:!0}),p=Bp(f,s.floating);return{data:{escapedOffsets:p,escaped:Vp(p)}}}default:return{}}}}},ym=new Set(["left","top"]);async function Yw(n,r){const{placement:s,platform:l,elements:c}=n,f=await(l.isRTL==null?void 0:l.isRTL(c.floating)),p=tr(s),h=ss(s),g=On(s)==="y",v=ym.has(p)?-1:1,x=f&&g?-1:1,b=er(r,n);let{mainAxis:S,crossAxis:y,alignmentAxis:E}=typeof b=="number"?{mainAxis:b,crossAxis:0,alignmentAxis:null}:{mainAxis:b.mainAxis||0,crossAxis:b.crossAxis||0,alignmentAxis:b.alignmentAxis};return h&&typeof E=="number"&&(y=h==="end"?E*-1:E),g?{x:y*x,y:S*v}:{x:S*v,y:y*x}}const Xw=function(n){return n===void 0&&(n=0),{name:"offset",options:n,async fn(r){var s,l;const{x:c,y:f,placement:p,middlewareData:h}=r,g=await Yw(r,n);return p===((s=h.offset)==null?void 0:s.placement)&&(l=h.arrow)!=null&&l.alignmentOffset?{}:{x:c+g.x,y:f+g.y,data:{...g,placement:p}}}}},Zw=function(n){return n===void 0&&(n={}),{name:"shift",options:n,async fn(r){const{x:s,y:l,placement:c}=r,{mainAxis:f=!0,crossAxis:p=!1,limiter:h={fn:j=>{let{x:_,y:P}=j;return{x:_,y:P}}},...g}=er(n,r),v={x:s,y:l},x=await Ys(r,g),b=On(tr(c)),S=kc(b);let y=v[S],E=v[b];if(f){const j=S==="y"?"top":"left",_=S==="y"?"bottom":"right",P=y+x[j],A=y-x[_];y=oc(P,y,A)}if(p){const j=b==="y"?"top":"left",_=b==="y"?"bottom":"right",P=E+x[j],A=E-x[_];E=oc(P,E,A)}const k=h.fn({...r,[S]:y,[b]:E});return{...k,data:{x:k.x-s,y:k.y-l,enabled:{[S]:f,[b]:p}}}}}},Jw=function(n){return n===void 0&&(n={}),{options:n,fn(r){const{x:s,y:l,placement:c,rects:f,middlewareData:p}=r,{offset:h=0,mainAxis:g=!0,crossAxis:v=!0}=er(n,r),x={x:s,y:l},b=On(c),S=kc(b);let y=x[S],E=x[b];const k=er(h,r),j=typeof k=="number"?{mainAxis:k,crossAxis:0}:{mainAxis:0,crossAxis:0,...k};if(g){const A=S==="y"?"height":"width",z=f.reference[S]-f.floating[A]+j.mainAxis,O=f.reference[S]+f.reference[A]-j.mainAxis;y<z?y=z:y>O&&(y=O)}if(v){var _,P;const A=S==="y"?"width":"height",z=ym.has(tr(c)),O=f.reference[b]-f.floating[A]+(z&&((_=p.offset)==null?void 0:_[b])||0)+(z?0:j.crossAxis),F=f.reference[b]+f.reference[A]+(z?0:((P=p.offset)==null?void 0:P[b])||0)-(z?j.crossAxis:0);E<O?E=O:E>F&&(E=F)}return{[S]:y,[b]:E}}}},e1=function(n){return n===void 0&&(n={}),{name:"size",options:n,async fn(r){var s,l;const{placement:c,rects:f,platform:p,elements:h}=r,{apply:g=()=>{},...v}=er(n,r),x=await Ys(r,v),b=tr(c),S=ss(c),y=On(c)==="y",{width:E,height:k}=f.floating;let j,_;b==="top"||b==="bottom"?(j=b,_=S===(await(p.isRTL==null?void 0:p.isRTL(h.floating))?"start":"end")?"left":"right"):(_=b,j=S==="end"?"top":"bottom");const P=k-x.top-x.bottom,A=E-x.left-x.right,z=Fr(k-x[j],P),O=Fr(E-x[_],A),F=!r.middlewareData.shift;let $=z,G=O;if((s=r.middlewareData.shift)!=null&&s.enabled.x&&(G=A),(l=r.middlewareData.shift)!=null&&l.enabled.y&&($=P),F&&!S){const J=Lt(x.left,0),pe=Lt(x.right,0),te=Lt(x.top,0),ge=Lt(x.bottom,0);y?G=E-2*(J!==0||pe!==0?J+pe:Lt(x.left,x.right)):$=k-2*(te!==0||ge!==0?te+ge:Lt(x.top,x.bottom))}await g({...r,availableWidth:G,availableHeight:$});const Z=await p.getDimensions(h.floating);return E!==Z.width||k!==Z.height?{reset:{rects:!0}}:{}}}};function Vl(){return typeof window<"u"}function is(n){return xm(n)?(n.nodeName||"").toLowerCase():"#document"}function zt(n){var r;return(n==null||(r=n.ownerDocument)==null?void 0:r.defaultView)||window}function In(n){var r;return(r=(xm(n)?n.ownerDocument:n.document)||window.document)==null?void 0:r.documentElement}function xm(n){return Vl()?n instanceof Node||n instanceof zt(n).Node:!1}function mn(n){return Vl()?n instanceof Element||n instanceof zt(n).Element:!1}function Dn(n){return Vl()?n instanceof HTMLElement||n instanceof zt(n).HTMLElement:!1}function Hp(n){return!Vl()||typeof ShadowRoot>"u"?!1:n instanceof ShadowRoot||n instanceof zt(n).ShadowRoot}const t1=new Set(["inline","contents"]);function oi(n){const{overflow:r,overflowX:s,overflowY:l,display:c}=gn(n);return/auto|scroll|overlay|hidden|clip/.test(r+l+s)&&!t1.has(c)}const n1=new Set(["table","td","th"]);function r1(n){return n1.has(is(n))}const o1=[":popover-open",":modal"];function Hl(n){return o1.some(r=>{try{return n.matches(r)}catch{return!1}})}const s1=["transform","translate","scale","rotate","perspective"],i1=["transform","translate","scale","rotate","perspective","filter"],l1=["paint","layout","strict","content"];function Cc(n){const r=Ec(),s=mn(n)?gn(n):n;return s1.some(l=>s[l]?s[l]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!r&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!r&&(s.filter?s.filter!=="none":!1)||i1.some(l=>(s.willChange||"").includes(l))||l1.some(l=>(s.contain||"").includes(l))}function a1(n){let r=Lr(n);for(;Dn(r)&&!Zo(r);){if(Cc(r))return r;if(Hl(r))return null;r=Lr(r)}return null}function Ec(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const u1=new Set(["html","body","#document"]);function Zo(n){return u1.has(is(n))}function gn(n){return zt(n).getComputedStyle(n)}function Wl(n){return mn(n)?{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}:{scrollLeft:n.scrollX,scrollTop:n.scrollY}}function Lr(n){if(is(n)==="html")return n;const r=n.assignedSlot||n.parentNode||Hp(n)&&n.host||In(n);return Hp(r)?r.host:r}function wm(n){const r=Lr(n);return Zo(r)?n.ownerDocument?n.ownerDocument.body:n.body:Dn(r)&&oi(r)?r:wm(r)}function Xs(n,r,s){var l;r===void 0&&(r=[]),s===void 0&&(s=!0);const c=wm(n),f=c===((l=n.ownerDocument)==null?void 0:l.body),p=zt(c);if(f){const h=ic(p);return r.concat(p,p.visualViewport||[],oi(c)?c:[],h&&s?Xs(h):[])}return r.concat(c,Xs(c,[],s))}function ic(n){return n.parent&&Object.getPrototypeOf(n.parent)?n.frameElement:null}function bm(n){const r=gn(n);let s=parseFloat(r.width)||0,l=parseFloat(r.height)||0;const c=Dn(n),f=c?n.offsetWidth:s,p=c?n.offsetHeight:l,h=Al(s)!==f||Al(l)!==p;return h&&(s=f,l=p),{width:s,height:l,$:h}}function Pc(n){return mn(n)?n:n.contextElement}function Bo(n){const r=Pc(n);if(!Dn(r))return Mn(1);const s=r.getBoundingClientRect(),{width:l,height:c,$:f}=bm(r);let p=(f?Al(s.width):s.width)/l,h=(f?Al(s.height):s.height)/c;return(!p||!Number.isFinite(p))&&(p=1),(!h||!Number.isFinite(h))&&(h=1),{x:p,y:h}}const c1=Mn(0);function jm(n){const r=zt(n);return!Ec()||!r.visualViewport?c1:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function d1(n,r,s){return r===void 0&&(r=!1),!s||r&&s!==zt(n)?!1:r}function uo(n,r,s,l){r===void 0&&(r=!1),s===void 0&&(s=!1);const c=n.getBoundingClientRect(),f=Pc(n);let p=Mn(1);r&&(l?mn(l)&&(p=Bo(l)):p=Bo(n));const h=d1(f,s,l)?jm(f):Mn(0);let g=(c.left+h.x)/p.x,v=(c.top+h.y)/p.y,x=c.width/p.x,b=c.height/p.y;if(f){const S=zt(f),y=l&&mn(l)?zt(l):l;let E=S,k=ic(E);for(;k&&l&&y!==E;){const j=Bo(k),_=k.getBoundingClientRect(),P=gn(k),A=_.left+(k.clientLeft+parseFloat(P.paddingLeft))*j.x,z=_.top+(k.clientTop+parseFloat(P.paddingTop))*j.y;g*=j.x,v*=j.y,x*=j.x,b*=j.y,g+=A,v+=z,E=zt(k),k=ic(E)}}return Ml({width:x,height:b,x:g,y:v})}function Tc(n,r){const s=Wl(n).scrollLeft;return r?r.left+s:uo(In(n)).left+s}function km(n,r,s){s===void 0&&(s=!1);const l=n.getBoundingClientRect(),c=l.left+r.scrollLeft-(s?0:Tc(n,l)),f=l.top+r.scrollTop;return{x:c,y:f}}function f1(n){let{elements:r,rect:s,offsetParent:l,strategy:c}=n;const f=c==="fixed",p=In(l),h=r?Hl(r.floating):!1;if(l===p||h&&f)return s;let g={scrollLeft:0,scrollTop:0},v=Mn(1);const x=Mn(0),b=Dn(l);if((b||!b&&!f)&&((is(l)!=="body"||oi(p))&&(g=Wl(l)),Dn(l))){const y=uo(l);v=Bo(l),x.x=y.x+l.clientLeft,x.y=y.y+l.clientTop}const S=p&&!b&&!f?km(p,g,!0):Mn(0);return{width:s.width*v.x,height:s.height*v.y,x:s.x*v.x-g.scrollLeft*v.x+x.x+S.x,y:s.y*v.y-g.scrollTop*v.y+x.y+S.y}}function p1(n){return Array.from(n.getClientRects())}function h1(n){const r=In(n),s=Wl(n),l=n.ownerDocument.body,c=Lt(r.scrollWidth,r.clientWidth,l.scrollWidth,l.clientWidth),f=Lt(r.scrollHeight,r.clientHeight,l.scrollHeight,l.clientHeight);let p=-s.scrollLeft+Tc(n);const h=-s.scrollTop;return gn(l).direction==="rtl"&&(p+=Lt(r.clientWidth,l.clientWidth)-c),{width:c,height:f,x:p,y:h}}function m1(n,r){const s=zt(n),l=In(n),c=s.visualViewport;let f=l.clientWidth,p=l.clientHeight,h=0,g=0;if(c){f=c.width,p=c.height;const v=Ec();(!v||v&&r==="fixed")&&(h=c.offsetLeft,g=c.offsetTop)}return{width:f,height:p,x:h,y:g}}const g1=new Set(["absolute","fixed"]);function v1(n,r){const s=uo(n,!0,r==="fixed"),l=s.top+n.clientTop,c=s.left+n.clientLeft,f=Dn(n)?Bo(n):Mn(1),p=n.clientWidth*f.x,h=n.clientHeight*f.y,g=c*f.x,v=l*f.y;return{width:p,height:h,x:g,y:v}}function Wp(n,r,s){let l;if(r==="viewport")l=m1(n,s);else if(r==="document")l=h1(In(n));else if(mn(r))l=v1(r,s);else{const c=jm(n);l={x:r.x-c.x,y:r.y-c.y,width:r.width,height:r.height}}return Ml(l)}function Nm(n,r){const s=Lr(n);return s===r||!mn(s)||Zo(s)?!1:gn(s).position==="fixed"||Nm(s,r)}function y1(n,r){const s=r.get(n);if(s)return s;let l=Xs(n,[],!1).filter(h=>mn(h)&&is(h)!=="body"),c=null;const f=gn(n).position==="fixed";let p=f?Lr(n):n;for(;mn(p)&&!Zo(p);){const h=gn(p),g=Cc(p);!g&&h.position==="fixed"&&(c=null),(f?!g&&!c:!g&&h.position==="static"&&!!c&&g1.has(c.position)||oi(p)&&!g&&Nm(n,p))?l=l.filter(x=>x!==p):c=h,p=Lr(p)}return r.set(n,l),l}function x1(n){let{element:r,boundary:s,rootBoundary:l,strategy:c}=n;const p=[...s==="clippingAncestors"?Hl(r)?[]:y1(r,this._c):[].concat(s),l],h=p[0],g=p.reduce((v,x)=>{const b=Wp(r,x,c);return v.top=Lt(b.top,v.top),v.right=Fr(b.right,v.right),v.bottom=Fr(b.bottom,v.bottom),v.left=Lt(b.left,v.left),v},Wp(r,h,c));return{width:g.right-g.left,height:g.bottom-g.top,x:g.left,y:g.top}}function w1(n){const{width:r,height:s}=bm(n);return{width:r,height:s}}function b1(n,r,s){const l=Dn(r),c=In(r),f=s==="fixed",p=uo(n,!0,f,r);let h={scrollLeft:0,scrollTop:0};const g=Mn(0);function v(){g.x=Tc(c)}if(l||!l&&!f)if((is(r)!=="body"||oi(c))&&(h=Wl(r)),l){const y=uo(r,!0,f,r);g.x=y.x+r.clientLeft,g.y=y.y+r.clientTop}else c&&v();f&&!l&&c&&v();const x=c&&!l&&!f?km(c,h):Mn(0),b=p.left+h.scrollLeft-g.x-x.x,S=p.top+h.scrollTop-g.y-x.y;return{x:b,y:S,width:p.width,height:p.height}}function Ku(n){return gn(n).position==="static"}function Qp(n,r){if(!Dn(n)||gn(n).position==="fixed")return null;if(r)return r(n);let s=n.offsetParent;return In(n)===s&&(s=s.ownerDocument.body),s}function Sm(n,r){const s=zt(n);if(Hl(n))return s;if(!Dn(n)){let c=Lr(n);for(;c&&!Zo(c);){if(mn(c)&&!Ku(c))return c;c=Lr(c)}return s}let l=Qp(n,r);for(;l&&r1(l)&&Ku(l);)l=Qp(l,r);return l&&Zo(l)&&Ku(l)&&!Cc(l)?s:l||a1(n)||s}const j1=async function(n){const r=this.getOffsetParent||Sm,s=this.getDimensions,l=await s(n.floating);return{reference:b1(n.reference,await r(n.floating),n.strategy),floating:{x:0,y:0,width:l.width,height:l.height}}};function k1(n){return gn(n).direction==="rtl"}const N1={convertOffsetParentRelativeRectToViewportRelativeRect:f1,getDocumentElement:In,getClippingRect:x1,getOffsetParent:Sm,getElementRects:j1,getClientRects:p1,getDimensions:w1,getScale:Bo,isElement:mn,isRTL:k1};function Cm(n,r){return n.x===r.x&&n.y===r.y&&n.width===r.width&&n.height===r.height}function S1(n,r){let s=null,l;const c=In(n);function f(){var h;clearTimeout(l),(h=s)==null||h.disconnect(),s=null}function p(h,g){h===void 0&&(h=!1),g===void 0&&(g=1),f();const v=n.getBoundingClientRect(),{left:x,top:b,width:S,height:y}=v;if(h||r(),!S||!y)return;const E=Sl(b),k=Sl(c.clientWidth-(x+S)),j=Sl(c.clientHeight-(b+y)),_=Sl(x),A={rootMargin:-E+"px "+-k+"px "+-j+"px "+-_+"px",threshold:Lt(0,Fr(1,g))||1};let z=!0;function O(F){const $=F[0].intersectionRatio;if($!==g){if(!z)return p();$?p(!1,$):l=setTimeout(()=>{p(!1,1e-7)},1e3)}$===1&&!Cm(v,n.getBoundingClientRect())&&p(),z=!1}try{s=new IntersectionObserver(O,{...A,root:c.ownerDocument})}catch{s=new IntersectionObserver(O,A)}s.observe(n)}return p(!0),f}function C1(n,r,s,l){l===void 0&&(l={});const{ancestorScroll:c=!0,ancestorResize:f=!0,elementResize:p=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:g=!1}=l,v=Pc(n),x=c||f?[...v?Xs(v):[],...Xs(r)]:[];x.forEach(_=>{c&&_.addEventListener("scroll",s,{passive:!0}),f&&_.addEventListener("resize",s)});const b=v&&h?S1(v,s):null;let S=-1,y=null;p&&(y=new ResizeObserver(_=>{let[P]=_;P&&P.target===v&&y&&(y.unobserve(r),cancelAnimationFrame(S),S=requestAnimationFrame(()=>{var A;(A=y)==null||A.observe(r)})),s()}),v&&!g&&y.observe(v),y.observe(r));let E,k=g?uo(n):null;g&&j();function j(){const _=uo(n);k&&!Cm(k,_)&&s(),k=_,E=requestAnimationFrame(j)}return s(),()=>{var _;x.forEach(P=>{c&&P.removeEventListener("scroll",s),f&&P.removeEventListener("resize",s)}),b==null||b(),(_=y)==null||_.disconnect(),y=null,g&&cancelAnimationFrame(E)}}const E1=Xw,P1=Zw,T1=qw,R1=e1,_1=Gw,Kp=Kw,A1=Jw,O1=(n,r,s)=>{const l=new Map,c={platform:N1,...s},f={...c.platform,_c:l};return Qw(n,r,{...c,platform:f})};var M1=typeof document<"u",D1=function(){},Rl=M1?w.useLayoutEffect:D1;function Dl(n,r){if(n===r)return!0;if(typeof n!=typeof r)return!1;if(typeof n=="function"&&n.toString()===r.toString())return!0;let s,l,c;if(n&&r&&typeof n=="object"){if(Array.isArray(n)){if(s=n.length,s!==r.length)return!1;for(l=s;l--!==0;)if(!Dl(n[l],r[l]))return!1;return!0}if(c=Object.keys(n),s=c.length,s!==Object.keys(r).length)return!1;for(l=s;l--!==0;)if(!{}.hasOwnProperty.call(r,c[l]))return!1;for(l=s;l--!==0;){const f=c[l];if(!(f==="_owner"&&n.$$typeof)&&!Dl(n[f],r[f]))return!1}return!0}return n!==n&&r!==r}function Em(n){return typeof window>"u"?1:(n.ownerDocument.defaultView||window).devicePixelRatio||1}function qp(n,r){const s=Em(n);return Math.round(r*s)/s}function qu(n){const r=w.useRef(n);return Rl(()=>{r.current=n}),r}function I1(n){n===void 0&&(n={});const{placement:r="bottom",strategy:s="absolute",middleware:l=[],platform:c,elements:{reference:f,floating:p}={},transform:h=!0,whileElementsMounted:g,open:v}=n,[x,b]=w.useState({x:0,y:0,strategy:s,placement:r,middlewareData:{},isPositioned:!1}),[S,y]=w.useState(l);Dl(S,l)||y(l);const[E,k]=w.useState(null),[j,_]=w.useState(null),P=w.useCallback(U=>{U!==F.current&&(F.current=U,k(U))},[]),A=w.useCallback(U=>{U!==$.current&&($.current=U,_(U))},[]),z=f||E,O=p||j,F=w.useRef(null),$=w.useRef(null),G=w.useRef(x),Z=g!=null,J=qu(g),pe=qu(c),te=qu(v),ge=w.useCallback(()=>{if(!F.current||!$.current)return;const U={placement:r,strategy:s,middleware:S};pe.current&&(U.platform=pe.current),O1(F.current,$.current,U).then(V=>{const K={...V,isPositioned:te.current!==!1};X.current&&!Dl(G.current,K)&&(G.current=K,zl.flushSync(()=>{b(K)}))})},[S,r,s,pe,te]);Rl(()=>{v===!1&&G.current.isPositioned&&(G.current.isPositioned=!1,b(U=>({...U,isPositioned:!1})))},[v]);const X=w.useRef(!1);Rl(()=>(X.current=!0,()=>{X.current=!1}),[]),Rl(()=>{if(z&&(F.current=z),O&&($.current=O),z&&O){if(J.current)return J.current(z,O,ge);ge()}},[z,O,ge,J,Z]);const ve=w.useMemo(()=>({reference:F,floating:$,setReference:P,setFloating:A}),[P,A]),se=w.useMemo(()=>({reference:z,floating:O}),[z,O]),ae=w.useMemo(()=>{const U={position:s,left:0,top:0};if(!se.floating)return U;const V=qp(se.floating,x.x),K=qp(se.floating,x.y);return h?{...U,transform:"translate("+V+"px, "+K+"px)",...Em(se.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:V,top:K}},[s,h,se.floating,x.x,x.y]);return w.useMemo(()=>({...x,update:ge,refs:ve,elements:se,floatingStyles:ae}),[x,ge,ve,se,ae])}const F1=n=>{function r(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:n,fn(s){const{element:l,padding:c}=typeof n=="function"?n(s):n;return l&&r(l)?l.current!=null?Kp({element:l.current,padding:c}).fn(s):{}:l?Kp({element:l,padding:c}).fn(s):{}}}},L1=(n,r)=>({...E1(n),options:[n,r]}),z1=(n,r)=>({...P1(n),options:[n,r]}),U1=(n,r)=>({...A1(n),options:[n,r]}),$1=(n,r)=>({...T1(n),options:[n,r]}),B1=(n,r)=>({...R1(n),options:[n,r]}),V1=(n,r)=>({..._1(n),options:[n,r]}),H1=(n,r)=>({...F1(n),options:[n,r]});var W1="Arrow",Pm=w.forwardRef((n,r)=>{const{children:s,width:l=10,height:c=5,...f}=n;return a.jsx($e.svg,{...f,ref:r,width:l,height:c,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:n.asChild?s:a.jsx("polygon",{points:"0,0 30,0 15,10"})})});Pm.displayName=W1;var Q1=Pm;function Rc(n){const[r,s]=w.useState(void 0);return Jn(()=>{if(n){s({width:n.offsetWidth,height:n.offsetHeight});const l=new ResizeObserver(c=>{if(!Array.isArray(c)||!c.length)return;const f=c[0];let p,h;if("borderBoxSize"in f){const g=f.borderBoxSize,v=Array.isArray(g)?g[0]:g;p=v.inlineSize,h=v.blockSize}else p=n.offsetWidth,h=n.offsetHeight;s({width:p,height:h})});return l.observe(n,{box:"border-box"}),()=>l.unobserve(n)}else s(void 0)},[n]),r}var Tm="Popper",[Rm,_m]=zr(Tm),[z2,Am]=Rm(Tm),Om="PopperAnchor",Mm=w.forwardRef((n,r)=>{const{__scopePopper:s,virtualRef:l,...c}=n,f=Am(Om,s),p=w.useRef(null),h=at(r,p);return w.useEffect(()=>{f.onAnchorChange((l==null?void 0:l.current)||p.current)}),l?null:a.jsx($e.div,{...c,ref:h})});Mm.displayName=Om;var _c="PopperContent",[K1,q1]=Rm(_c),Dm=w.forwardRef((n,r)=>{var ce,Ce,Pe,Ge,_t,Fn;const{__scopePopper:s,side:l="bottom",sideOffset:c=0,align:f="center",alignOffset:p=0,arrowPadding:h=0,avoidCollisions:g=!0,collisionBoundary:v=[],collisionPadding:x=0,sticky:b="partial",hideWhenDetached:S=!1,updatePositionStrategy:y="optimized",onPlaced:E,...k}=n,j=Am(_c,s),[_,P]=w.useState(null),A=at(r,At=>P(At)),[z,O]=w.useState(null),F=Rc(z),$=(F==null?void 0:F.width)??0,G=(F==null?void 0:F.height)??0,Z=l+(f!=="center"?"-"+f:""),J=typeof x=="number"?x:{top:0,right:0,bottom:0,left:0,...x},pe=Array.isArray(v)?v:[v],te=pe.length>0,ge={padding:J,boundary:pe.filter(Y1),altBoundary:te},{refs:X,floatingStyles:ve,placement:se,isPositioned:ae,middlewareData:U}=I1({strategy:"fixed",placement:Z,whileElementsMounted:(...At)=>C1(...At,{animationFrame:y==="always"}),elements:{reference:j.anchor},middleware:[L1({mainAxis:c+G,alignmentAxis:p}),g&&z1({mainAxis:!0,crossAxis:!1,limiter:b==="partial"?U1():void 0,...ge}),g&&$1({...ge}),B1({...ge,apply:({elements:At,rects:nr,availableWidth:fo,availableHeight:Ur})=>{const{width:$r,height:rr}=nr.reference,vn=At.floating.style;vn.setProperty("--radix-popper-available-width",`${fo}px`),vn.setProperty("--radix-popper-available-height",`${Ur}px`),vn.setProperty("--radix-popper-anchor-width",`${$r}px`),vn.setProperty("--radix-popper-anchor-height",`${rr}px`)}}),z&&H1({element:z,padding:h}),X1({arrowWidth:$,arrowHeight:G}),S&&V1({strategy:"referenceHidden",...ge})]}),[V,K]=Lm(se),R=Zn(E);Jn(()=>{ae&&(R==null||R())},[ae,R]);const L=(ce=U.arrow)==null?void 0:ce.x,ee=(Ce=U.arrow)==null?void 0:Ce.y,re=((Pe=U.arrow)==null?void 0:Pe.centerOffset)!==0,[he,be]=w.useState();return Jn(()=>{_&&be(window.getComputedStyle(_).zIndex)},[_]),a.jsx("div",{ref:X.setFloating,"data-radix-popper-content-wrapper":"",style:{...ve,transform:ae?ve.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:he,"--radix-popper-transform-origin":[(Ge=U.transformOrigin)==null?void 0:Ge.x,(_t=U.transformOrigin)==null?void 0:_t.y].join(" "),...((Fn=U.hide)==null?void 0:Fn.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:a.jsx(K1,{scope:s,placedSide:V,onArrowChange:O,arrowX:L,arrowY:ee,shouldHideArrow:re,children:a.jsx($e.div,{"data-side":V,"data-align":K,...k,ref:A,style:{...k.style,animation:ae?void 0:"none"}})})})});Dm.displayName=_c;var Im="PopperArrow",G1={top:"bottom",right:"left",bottom:"top",left:"right"},Fm=w.forwardRef(function(r,s){const{__scopePopper:l,...c}=r,f=q1(Im,l),p=G1[f.placedSide];return a.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[p]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:a.jsx(Q1,{...c,ref:s,style:{...c.style,display:"block"}})})});Fm.displayName=Im;function Y1(n){return n!==null}var X1=n=>({name:"transformOrigin",options:n,fn(r){var j,_,P;const{placement:s,rects:l,middlewareData:c}=r,p=((j=c.arrow)==null?void 0:j.centerOffset)!==0,h=p?0:n.arrowWidth,g=p?0:n.arrowHeight,[v,x]=Lm(s),b={start:"0%",center:"50%",end:"100%"}[x],S=(((_=c.arrow)==null?void 0:_.x)??0)+h/2,y=(((P=c.arrow)==null?void 0:P.y)??0)+g/2;let E="",k="";return v==="bottom"?(E=p?b:`${S}px`,k=`${-g}px`):v==="top"?(E=p?b:`${S}px`,k=`${l.floating.height+g}px`):v==="right"?(E=`${-g}px`,k=p?b:`${y}px`):v==="left"&&(E=`${l.floating.width+g}px`,k=p?b:`${y}px`),{data:{x:E,y:k}}}});function Lm(n){const[r,s="center"]=n.split("-");return[r,s]}var Z1=Mm,J1=Dm,eb=Fm,[Ql,U2]=zr("Tooltip",[_m]),Ac=_m(),zm="TooltipProvider",tb=700,Gp="tooltip.open",[nb,Um]=Ql(zm),$m=n=>{const{__scopeTooltip:r,delayDuration:s=tb,skipDelayDuration:l=300,disableHoverableContent:c=!1,children:f}=n,p=w.useRef(!0),h=w.useRef(!1),g=w.useRef(0);return w.useEffect(()=>{const v=g.current;return()=>window.clearTimeout(v)},[]),a.jsx(nb,{scope:r,isOpenDelayedRef:p,delayDuration:s,onOpen:w.useCallback(()=>{window.clearTimeout(g.current),p.current=!1},[]),onClose:w.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>p.current=!0,l)},[l]),isPointerInTransitRef:h,onPointerInTransitChange:w.useCallback(v=>{h.current=v},[]),disableHoverableContent:c,children:f})};$m.displayName=zm;var Bm="Tooltip",[$2,Kl]=Ql(Bm),lc="TooltipTrigger",rb=w.forwardRef((n,r)=>{const{__scopeTooltip:s,...l}=n,c=Kl(lc,s),f=Um(lc,s),p=Ac(s),h=w.useRef(null),g=at(r,h,c.onTriggerChange),v=w.useRef(!1),x=w.useRef(!1),b=w.useCallback(()=>v.current=!1,[]);return w.useEffect(()=>()=>document.removeEventListener("pointerup",b),[b]),a.jsx(Z1,{asChild:!0,...p,children:a.jsx($e.button,{"aria-describedby":c.open?c.contentId:void 0,"data-state":c.stateAttribute,...l,ref:g,onPointerMove:Oe(n.onPointerMove,S=>{S.pointerType!=="touch"&&!x.current&&!f.isPointerInTransitRef.current&&(c.onTriggerEnter(),x.current=!0)}),onPointerLeave:Oe(n.onPointerLeave,()=>{c.onTriggerLeave(),x.current=!1}),onPointerDown:Oe(n.onPointerDown,()=>{c.open&&c.onClose(),v.current=!0,document.addEventListener("pointerup",b,{once:!0})}),onFocus:Oe(n.onFocus,()=>{v.current||c.onOpen()}),onBlur:Oe(n.onBlur,c.onClose),onClick:Oe(n.onClick,c.onClose)})})});rb.displayName=lc;var ob="TooltipPortal",[B2,sb]=Ql(ob,{forceMount:void 0}),Jo="TooltipContent",Vm=w.forwardRef((n,r)=>{const s=sb(Jo,n.__scopeTooltip),{forceMount:l=s.forceMount,side:c="top",...f}=n,p=Kl(Jo,n.__scopeTooltip);return a.jsx(ni,{present:l||p.open,children:p.disableHoverableContent?a.jsx(Hm,{side:c,...f,ref:r}):a.jsx(ib,{side:c,...f,ref:r})})}),ib=w.forwardRef((n,r)=>{const s=Kl(Jo,n.__scopeTooltip),l=Um(Jo,n.__scopeTooltip),c=w.useRef(null),f=at(r,c),[p,h]=w.useState(null),{trigger:g,onClose:v}=s,x=c.current,{onPointerInTransitChange:b}=l,S=w.useCallback(()=>{h(null),b(!1)},[b]),y=w.useCallback((E,k)=>{const j=E.currentTarget,_={x:E.clientX,y:E.clientY},P=db(_,j.getBoundingClientRect()),A=fb(_,P),z=pb(k.getBoundingClientRect()),O=mb([...A,...z]);h(O),b(!0)},[b]);return w.useEffect(()=>()=>S(),[S]),w.useEffect(()=>{if(g&&x){const E=j=>y(j,x),k=j=>y(j,g);return g.addEventListener("pointerleave",E),x.addEventListener("pointerleave",k),()=>{g.removeEventListener("pointerleave",E),x.removeEventListener("pointerleave",k)}}},[g,x,y,S]),w.useEffect(()=>{if(p){const E=k=>{const j=k.target,_={x:k.clientX,y:k.clientY},P=(g==null?void 0:g.contains(j))||(x==null?void 0:x.contains(j)),A=!hb(_,p);P?S():A&&(S(),v())};return document.addEventListener("pointermove",E),()=>document.removeEventListener("pointermove",E)}},[g,x,p,v,S]),a.jsx(Hm,{...n,ref:f})}),[lb,ab]=Ql(Bm,{isInside:!1}),ub=ry("TooltipContent"),Hm=w.forwardRef((n,r)=>{const{__scopeTooltip:s,children:l,"aria-label":c,onEscapeKeyDown:f,onPointerDownOutside:p,...h}=n,g=Kl(Jo,s),v=Ac(s),{onClose:x}=g;return w.useEffect(()=>(document.addEventListener(Gp,x),()=>document.removeEventListener(Gp,x)),[x]),w.useEffect(()=>{if(g.trigger){const b=S=>{const y=S.target;y!=null&&y.contains(g.trigger)&&x()};return window.addEventListener("scroll",b,{capture:!0}),()=>window.removeEventListener("scroll",b,{capture:!0})}},[g.trigger,x]),a.jsx(gc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:b=>b.preventDefault(),onDismiss:x,children:a.jsxs(J1,{"data-state":g.stateAttribute,...v,...h,ref:r,style:{...h.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[a.jsx(ub,{children:l}),a.jsx(lb,{scope:s,isInside:!0,children:a.jsx(Cy,{id:g.contentId,role:"tooltip",children:c||l})})]})})});Vm.displayName=Jo;var Wm="TooltipArrow",cb=w.forwardRef((n,r)=>{const{__scopeTooltip:s,...l}=n,c=Ac(s);return ab(Wm,s).isInside?null:a.jsx(eb,{...c,...l,ref:r})});cb.displayName=Wm;function db(n,r){const s=Math.abs(r.top-n.y),l=Math.abs(r.bottom-n.y),c=Math.abs(r.right-n.x),f=Math.abs(r.left-n.x);switch(Math.min(s,l,c,f)){case f:return"left";case c:return"right";case s:return"top";case l:return"bottom";default:throw new Error("unreachable")}}function fb(n,r,s=5){const l=[];switch(r){case"top":l.push({x:n.x-s,y:n.y+s},{x:n.x+s,y:n.y+s});break;case"bottom":l.push({x:n.x-s,y:n.y-s},{x:n.x+s,y:n.y-s});break;case"left":l.push({x:n.x+s,y:n.y-s},{x:n.x+s,y:n.y+s});break;case"right":l.push({x:n.x-s,y:n.y-s},{x:n.x-s,y:n.y+s});break}return l}function pb(n){const{top:r,right:s,bottom:l,left:c}=n;return[{x:c,y:r},{x:s,y:r},{x:s,y:l},{x:c,y:l}]}function hb(n,r){const{x:s,y:l}=n;let c=!1;for(let f=0,p=r.length-1;f<r.length;p=f++){const h=r[f],g=r[p],v=h.x,x=h.y,b=g.x,S=g.y;x>l!=S>l&&s<(b-v)*(l-x)/(S-x)+v&&(c=!c)}return c}function mb(n){const r=n.slice();return r.sort((s,l)=>s.x<l.x?-1:s.x>l.x?1:s.y<l.y?-1:s.y>l.y?1:0),gb(r)}function gb(n){if(n.length<=1)return n.slice();const r=[];for(let l=0;l<n.length;l++){const c=n[l];for(;r.length>=2;){const f=r[r.length-1],p=r[r.length-2];if((f.x-p.x)*(c.y-p.y)>=(f.y-p.y)*(c.x-p.x))r.pop();else break}r.push(c)}r.pop();const s=[];for(let l=n.length-1;l>=0;l--){const c=n[l];for(;s.length>=2;){const f=s[s.length-1],p=s[s.length-2];if((f.x-p.x)*(c.y-p.y)>=(f.y-p.y)*(c.x-p.x))s.pop();else break}s.push(c)}return s.pop(),r.length===1&&s.length===1&&r[0].x===s[0].x&&r[0].y===s[0].y?r:r.concat(s)}var vb=$m,Qm=Vm;const yb=vb,xb=w.forwardRef(({className:n,sideOffset:r=4,...s},l)=>a.jsx(Qm,{ref:l,sideOffset:r,className:Se("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...s}));xb.displayName=Qm.displayName;var ql=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Gl=typeof window>"u"||"Deno"in globalThis;function cn(){}function wb(n,r){return typeof n=="function"?n(r):n}function bb(n){return typeof n=="number"&&n>=0&&n!==1/0}function jb(n,r){return Math.max(n+(r||0)-Date.now(),0)}function ac(n,r){return typeof n=="function"?n(r):n}function kb(n,r){return typeof n=="function"?n(r):n}function Yp(n,r){const{type:s="all",exact:l,fetchStatus:c,predicate:f,queryKey:p,stale:h}=n;if(p){if(l){if(r.queryHash!==Oc(p,r.options))return!1}else if(!Js(r.queryKey,p))return!1}if(s!=="all"){const g=r.isActive();if(s==="active"&&!g||s==="inactive"&&g)return!1}return!(typeof h=="boolean"&&r.isStale()!==h||c&&c!==r.state.fetchStatus||f&&!f(r))}function Xp(n,r){const{exact:s,status:l,predicate:c,mutationKey:f}=n;if(f){if(!r.options.mutationKey)return!1;if(s){if(Zs(r.options.mutationKey)!==Zs(f))return!1}else if(!Js(r.options.mutationKey,f))return!1}return!(l&&r.state.status!==l||c&&!c(r))}function Oc(n,r){return((r==null?void 0:r.queryKeyHashFn)||Zs)(n)}function Zs(n){return JSON.stringify(n,(r,s)=>uc(s)?Object.keys(s).sort().reduce((l,c)=>(l[c]=s[c],l),{}):s)}function Js(n,r){return n===r?!0:typeof n!=typeof r?!1:n&&r&&typeof n=="object"&&typeof r=="object"?Object.keys(r).every(s=>Js(n[s],r[s])):!1}function Km(n,r){if(n===r)return n;const s=Zp(n)&&Zp(r);if(s||uc(n)&&uc(r)){const l=s?n:Object.keys(n),c=l.length,f=s?r:Object.keys(r),p=f.length,h=s?[]:{},g=new Set(l);let v=0;for(let x=0;x<p;x++){const b=s?x:f[x];(!s&&g.has(b)||s)&&n[b]===void 0&&r[b]===void 0?(h[b]=void 0,v++):(h[b]=Km(n[b],r[b]),h[b]===n[b]&&n[b]!==void 0&&v++)}return c===p&&v===c?n:h}return r}function Zp(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function uc(n){if(!Jp(n))return!1;const r=n.constructor;if(r===void 0)return!0;const s=r.prototype;return!(!Jp(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function Jp(n){return Object.prototype.toString.call(n)==="[object Object]"}function Nb(n){return new Promise(r=>{setTimeout(r,n)})}function Sb(n,r,s){return typeof s.structuralSharing=="function"?s.structuralSharing(n,r):s.structuralSharing!==!1?Km(n,r):r}function Cb(n,r,s=0){const l=[...n,r];return s&&l.length>s?l.slice(1):l}function Eb(n,r,s=0){const l=[r,...n];return s&&l.length>s?l.slice(0,-1):l}var Mc=Symbol();function qm(n,r){return!n.queryFn&&(r!=null&&r.initialPromise)?()=>r.initialPromise:!n.queryFn||n.queryFn===Mc?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}var ro,Tr,Ho,gh,Pb=(gh=class extends ql{constructor(){super();_e(this,ro);_e(this,Tr);_e(this,Ho);we(this,Ho,r=>{if(!Gl&&window.addEventListener){const s=()=>r();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){B(this,Tr)||this.setEventListener(B(this,Ho))}onUnsubscribe(){var r;this.hasListeners()||((r=B(this,Tr))==null||r.call(this),we(this,Tr,void 0))}setEventListener(r){var s;we(this,Ho,r),(s=B(this,Tr))==null||s.call(this),we(this,Tr,r(l=>{typeof l=="boolean"?this.setFocused(l):this.onFocus()}))}setFocused(r){B(this,ro)!==r&&(we(this,ro,r),this.onFocus())}onFocus(){const r=this.isFocused();this.listeners.forEach(s=>{s(r)})}isFocused(){var r;return typeof B(this,ro)=="boolean"?B(this,ro):((r=globalThis.document)==null?void 0:r.visibilityState)!=="hidden"}},ro=new WeakMap,Tr=new WeakMap,Ho=new WeakMap,gh),Gm=new Pb,Wo,Rr,Qo,vh,Tb=(vh=class extends ql{constructor(){super();_e(this,Wo,!0);_e(this,Rr);_e(this,Qo);we(this,Qo,r=>{if(!Gl&&window.addEventListener){const s=()=>r(!0),l=()=>r(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",l,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",l)}}})}onSubscribe(){B(this,Rr)||this.setEventListener(B(this,Qo))}onUnsubscribe(){var r;this.hasListeners()||((r=B(this,Rr))==null||r.call(this),we(this,Rr,void 0))}setEventListener(r){var s;we(this,Qo,r),(s=B(this,Rr))==null||s.call(this),we(this,Rr,r(this.setOnline.bind(this)))}setOnline(r){B(this,Wo)!==r&&(we(this,Wo,r),this.listeners.forEach(l=>{l(r)}))}isOnline(){return B(this,Wo)}},Wo=new WeakMap,Rr=new WeakMap,Qo=new WeakMap,vh),Il=new Tb;function Rb(){let n,r;const s=new Promise((c,f)=>{n=c,r=f});s.status="pending",s.catch(()=>{});function l(c){Object.assign(s,c),delete s.resolve,delete s.reject}return s.resolve=c=>{l({status:"fulfilled",value:c}),n(c)},s.reject=c=>{l({status:"rejected",reason:c}),r(c)},s}function _b(n){return Math.min(1e3*2**n,3e4)}function Ym(n){return(n??"online")==="online"?Il.isOnline():!0}var Xm=class extends Error{constructor(n){super("CancelledError"),this.revert=n==null?void 0:n.revert,this.silent=n==null?void 0:n.silent}};function Gu(n){return n instanceof Xm}function Zm(n){let r=!1,s=0,l=!1,c;const f=Rb(),p=k=>{var j;l||(S(new Xm(k)),(j=n.abort)==null||j.call(n))},h=()=>{r=!0},g=()=>{r=!1},v=()=>Gm.isFocused()&&(n.networkMode==="always"||Il.isOnline())&&n.canRun(),x=()=>Ym(n.networkMode)&&n.canRun(),b=k=>{var j;l||(l=!0,(j=n.onSuccess)==null||j.call(n,k),c==null||c(),f.resolve(k))},S=k=>{var j;l||(l=!0,(j=n.onError)==null||j.call(n,k),c==null||c(),f.reject(k))},y=()=>new Promise(k=>{var j;c=_=>{(l||v())&&k(_)},(j=n.onPause)==null||j.call(n)}).then(()=>{var k;c=void 0,l||(k=n.onContinue)==null||k.call(n)}),E=()=>{if(l)return;let k;const j=s===0?n.initialPromise:void 0;try{k=j??n.fn()}catch(_){k=Promise.reject(_)}Promise.resolve(k).then(b).catch(_=>{var F;if(l)return;const P=n.retry??(Gl?0:3),A=n.retryDelay??_b,z=typeof A=="function"?A(s,_):A,O=P===!0||typeof P=="number"&&s<P||typeof P=="function"&&P(s,_);if(r||!O){S(_);return}s++,(F=n.onFail)==null||F.call(n,s,_),Nb(z).then(()=>v()?void 0:y()).then(()=>{r?S(_):E()})})};return{promise:f,cancel:p,continue:()=>(c==null||c(),f),cancelRetry:h,continueRetry:g,canStart:x,start:()=>(x()?E():y().then(E),f)}}var Ab=n=>setTimeout(n,0);function Ob(){let n=[],r=0,s=h=>{h()},l=h=>{h()},c=Ab;const f=h=>{r?n.push(h):c(()=>{s(h)})},p=()=>{const h=n;n=[],h.length&&c(()=>{l(()=>{h.forEach(g=>{s(g)})})})};return{batch:h=>{let g;r++;try{g=h()}finally{r--,r||p()}return g},batchCalls:h=>(...g)=>{f(()=>{h(...g)})},schedule:f,setNotifyFunction:h=>{s=h},setBatchNotifyFunction:h=>{l=h},setScheduler:h=>{c=h}}}var wt=Ob(),oo,yh,Jm=(yh=class{constructor(){_e(this,oo)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),bb(this.gcTime)&&we(this,oo,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(Gl?1/0:5*60*1e3))}clearGcTimeout(){B(this,oo)&&(clearTimeout(B(this,oo)),we(this,oo,void 0))}},oo=new WeakMap,yh),Ko,so,Kt,io,ht,ei,lo,dn,Yn,xh,Mb=(xh=class extends Jm{constructor(r){super();_e(this,dn);_e(this,Ko);_e(this,so);_e(this,Kt);_e(this,io);_e(this,ht);_e(this,ei);_e(this,lo);we(this,lo,!1),we(this,ei,r.defaultOptions),this.setOptions(r.options),this.observers=[],we(this,io,r.client),we(this,Kt,B(this,io).getQueryCache()),this.queryKey=r.queryKey,this.queryHash=r.queryHash,we(this,Ko,Ib(this.options)),this.state=r.state??B(this,Ko),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var r;return(r=B(this,ht))==null?void 0:r.promise}setOptions(r){this.options={...B(this,ei),...r},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&B(this,Kt).remove(this)}setData(r,s){const l=Sb(this.state.data,r,this.options);return pt(this,dn,Yn).call(this,{data:l,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),l}setState(r,s){pt(this,dn,Yn).call(this,{type:"setState",state:r,setStateOptions:s})}cancel(r){var l,c;const s=(l=B(this,ht))==null?void 0:l.promise;return(c=B(this,ht))==null||c.cancel(r),s?s.then(cn).catch(cn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(B(this,Ko))}isActive(){return this.observers.some(r=>kb(r.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Mc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(r=>ac(r.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(r=>r.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(r=0){return this.state.data===void 0?!0:r==="static"?!1:this.state.isInvalidated?!0:!jb(this.state.dataUpdatedAt,r)}onFocus(){var s;const r=this.observers.find(l=>l.shouldFetchOnWindowFocus());r==null||r.refetch({cancelRefetch:!1}),(s=B(this,ht))==null||s.continue()}onOnline(){var s;const r=this.observers.find(l=>l.shouldFetchOnReconnect());r==null||r.refetch({cancelRefetch:!1}),(s=B(this,ht))==null||s.continue()}addObserver(r){this.observers.includes(r)||(this.observers.push(r),this.clearGcTimeout(),B(this,Kt).notify({type:"observerAdded",query:this,observer:r}))}removeObserver(r){this.observers.includes(r)&&(this.observers=this.observers.filter(s=>s!==r),this.observers.length||(B(this,ht)&&(B(this,lo)?B(this,ht).cancel({revert:!0}):B(this,ht).cancelRetry()),this.scheduleGc()),B(this,Kt).notify({type:"observerRemoved",query:this,observer:r}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||pt(this,dn,Yn).call(this,{type:"invalidate"})}fetch(r,s){var v,x,b;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(B(this,ht))return B(this,ht).continueRetry(),B(this,ht).promise}if(r&&this.setOptions(r),!this.options.queryFn){const S=this.observers.find(y=>y.options.queryFn);S&&this.setOptions(S.options)}const l=new AbortController,c=S=>{Object.defineProperty(S,"signal",{enumerable:!0,get:()=>(we(this,lo,!0),l.signal)})},f=()=>{const S=qm(this.options,s),E=(()=>{const k={client:B(this,io),queryKey:this.queryKey,meta:this.meta};return c(k),k})();return we(this,lo,!1),this.options.persister?this.options.persister(S,E,this):S(E)},h=(()=>{const S={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:B(this,io),state:this.state,fetchFn:f};return c(S),S})();(v=this.options.behavior)==null||v.onFetch(h,this),we(this,so,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((x=h.fetchOptions)==null?void 0:x.meta))&&pt(this,dn,Yn).call(this,{type:"fetch",meta:(b=h.fetchOptions)==null?void 0:b.meta});const g=S=>{var y,E,k,j;Gu(S)&&S.silent||pt(this,dn,Yn).call(this,{type:"error",error:S}),Gu(S)||((E=(y=B(this,Kt).config).onError)==null||E.call(y,S,this),(j=(k=B(this,Kt).config).onSettled)==null||j.call(k,this.state.data,S,this)),this.scheduleGc()};return we(this,ht,Zm({initialPromise:s==null?void 0:s.initialPromise,fn:h.fetchFn,abort:l.abort.bind(l),onSuccess:S=>{var y,E,k,j;if(S===void 0){g(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(S)}catch(_){g(_);return}(E=(y=B(this,Kt).config).onSuccess)==null||E.call(y,S,this),(j=(k=B(this,Kt).config).onSettled)==null||j.call(k,S,this.state.error,this),this.scheduleGc()},onError:g,onFail:(S,y)=>{pt(this,dn,Yn).call(this,{type:"failed",failureCount:S,error:y})},onPause:()=>{pt(this,dn,Yn).call(this,{type:"pause"})},onContinue:()=>{pt(this,dn,Yn).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),B(this,ht).start()}},Ko=new WeakMap,so=new WeakMap,Kt=new WeakMap,io=new WeakMap,ht=new WeakMap,ei=new WeakMap,lo=new WeakMap,dn=new WeakSet,Yn=function(r){const s=l=>{switch(r.type){case"failed":return{...l,fetchFailureCount:r.failureCount,fetchFailureReason:r.error};case"pause":return{...l,fetchStatus:"paused"};case"continue":return{...l,fetchStatus:"fetching"};case"fetch":return{...l,...Db(l.data,this.options),fetchMeta:r.meta??null};case"success":return we(this,so,void 0),{...l,data:r.data,dataUpdateCount:l.dataUpdateCount+1,dataUpdatedAt:r.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!r.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const c=r.error;return Gu(c)&&c.revert&&B(this,so)?{...B(this,so),fetchStatus:"idle"}:{...l,error:c,errorUpdateCount:l.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:l.fetchFailureCount+1,fetchFailureReason:c,fetchStatus:"idle",status:"error"};case"invalidate":return{...l,isInvalidated:!0};case"setState":return{...l,...r.state}}};this.state=s(this.state),wt.batch(()=>{this.observers.forEach(l=>{l.onQueryUpdate()}),B(this,Kt).notify({query:this,type:"updated",action:r})})},xh);function Db(n,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ym(r.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function Ib(n){const r=typeof n.initialData=="function"?n.initialData():n.initialData,s=r!==void 0,l=s?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:s?l??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var Rn,wh,Fb=(wh=class extends ql{constructor(r={}){super();_e(this,Rn);this.config=r,we(this,Rn,new Map)}build(r,s,l){const c=s.queryKey,f=s.queryHash??Oc(c,s);let p=this.get(f);return p||(p=new Mb({client:r,queryKey:c,queryHash:f,options:r.defaultQueryOptions(s),state:l,defaultOptions:r.getQueryDefaults(c)}),this.add(p)),p}add(r){B(this,Rn).has(r.queryHash)||(B(this,Rn).set(r.queryHash,r),this.notify({type:"added",query:r}))}remove(r){const s=B(this,Rn).get(r.queryHash);s&&(r.destroy(),s===r&&B(this,Rn).delete(r.queryHash),this.notify({type:"removed",query:r}))}clear(){wt.batch(()=>{this.getAll().forEach(r=>{this.remove(r)})})}get(r){return B(this,Rn).get(r)}getAll(){return[...B(this,Rn).values()]}find(r){const s={exact:!0,...r};return this.getAll().find(l=>Yp(s,l))}findAll(r={}){const s=this.getAll();return Object.keys(r).length>0?s.filter(l=>Yp(r,l)):s}notify(r){wt.batch(()=>{this.listeners.forEach(s=>{s(r)})})}onFocus(){wt.batch(()=>{this.getAll().forEach(r=>{r.onFocus()})})}onOnline(){wt.batch(()=>{this.getAll().forEach(r=>{r.onOnline()})})}},Rn=new WeakMap,wh),_n,xt,ao,An,Pr,bh,Lb=(bh=class extends Jm{constructor(r){super();_e(this,An);_e(this,_n);_e(this,xt);_e(this,ao);this.mutationId=r.mutationId,we(this,xt,r.mutationCache),we(this,_n,[]),this.state=r.state||zb(),this.setOptions(r.options),this.scheduleGc()}setOptions(r){this.options=r,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(r){B(this,_n).includes(r)||(B(this,_n).push(r),this.clearGcTimeout(),B(this,xt).notify({type:"observerAdded",mutation:this,observer:r}))}removeObserver(r){we(this,_n,B(this,_n).filter(s=>s!==r)),this.scheduleGc(),B(this,xt).notify({type:"observerRemoved",mutation:this,observer:r})}optionalRemove(){B(this,_n).length||(this.state.status==="pending"?this.scheduleGc():B(this,xt).remove(this))}continue(){var r;return((r=B(this,ao))==null?void 0:r.continue())??this.execute(this.state.variables)}async execute(r){var f,p,h,g,v,x,b,S,y,E,k,j,_,P,A,z,O,F,$,G;const s=()=>{pt(this,An,Pr).call(this,{type:"continue"})};we(this,ao,Zm({fn:()=>this.options.mutationFn?this.options.mutationFn(r):Promise.reject(new Error("No mutationFn found")),onFail:(Z,J)=>{pt(this,An,Pr).call(this,{type:"failed",failureCount:Z,error:J})},onPause:()=>{pt(this,An,Pr).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>B(this,xt).canRun(this)}));const l=this.state.status==="pending",c=!B(this,ao).canStart();try{if(l)s();else{pt(this,An,Pr).call(this,{type:"pending",variables:r,isPaused:c}),await((p=(f=B(this,xt).config).onMutate)==null?void 0:p.call(f,r,this));const J=await((g=(h=this.options).onMutate)==null?void 0:g.call(h,r));J!==this.state.context&&pt(this,An,Pr).call(this,{type:"pending",context:J,variables:r,isPaused:c})}const Z=await B(this,ao).start();return await((x=(v=B(this,xt).config).onSuccess)==null?void 0:x.call(v,Z,r,this.state.context,this)),await((S=(b=this.options).onSuccess)==null?void 0:S.call(b,Z,r,this.state.context)),await((E=(y=B(this,xt).config).onSettled)==null?void 0:E.call(y,Z,null,this.state.variables,this.state.context,this)),await((j=(k=this.options).onSettled)==null?void 0:j.call(k,Z,null,r,this.state.context)),pt(this,An,Pr).call(this,{type:"success",data:Z}),Z}catch(Z){try{throw await((P=(_=B(this,xt).config).onError)==null?void 0:P.call(_,Z,r,this.state.context,this)),await((z=(A=this.options).onError)==null?void 0:z.call(A,Z,r,this.state.context)),await((F=(O=B(this,xt).config).onSettled)==null?void 0:F.call(O,void 0,Z,this.state.variables,this.state.context,this)),await((G=($=this.options).onSettled)==null?void 0:G.call($,void 0,Z,r,this.state.context)),Z}finally{pt(this,An,Pr).call(this,{type:"error",error:Z})}}finally{B(this,xt).runNext(this)}}},_n=new WeakMap,xt=new WeakMap,ao=new WeakMap,An=new WeakSet,Pr=function(r){const s=l=>{switch(r.type){case"failed":return{...l,failureCount:r.failureCount,failureReason:r.error};case"pause":return{...l,isPaused:!0};case"continue":return{...l,isPaused:!1};case"pending":return{...l,context:r.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:r.isPaused,status:"pending",variables:r.variables,submittedAt:Date.now()};case"success":return{...l,data:r.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...l,data:void 0,error:r.error,failureCount:l.failureCount+1,failureReason:r.error,isPaused:!1,status:"error"}}};this.state=s(this.state),wt.batch(()=>{B(this,_n).forEach(l=>{l.onMutationUpdate(r)}),B(this,xt).notify({mutation:this,type:"updated",action:r})})},bh);function zb(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Xn,fn,ti,jh,Ub=(jh=class extends ql{constructor(r={}){super();_e(this,Xn);_e(this,fn);_e(this,ti);this.config=r,we(this,Xn,new Set),we(this,fn,new Map),we(this,ti,0)}build(r,s,l){const c=new Lb({mutationCache:this,mutationId:++wl(this,ti)._,options:r.defaultMutationOptions(s),state:l});return this.add(c),c}add(r){B(this,Xn).add(r);const s=Cl(r);if(typeof s=="string"){const l=B(this,fn).get(s);l?l.push(r):B(this,fn).set(s,[r])}this.notify({type:"added",mutation:r})}remove(r){if(B(this,Xn).delete(r)){const s=Cl(r);if(typeof s=="string"){const l=B(this,fn).get(s);if(l)if(l.length>1){const c=l.indexOf(r);c!==-1&&l.splice(c,1)}else l[0]===r&&B(this,fn).delete(s)}}this.notify({type:"removed",mutation:r})}canRun(r){const s=Cl(r);if(typeof s=="string"){const l=B(this,fn).get(s),c=l==null?void 0:l.find(f=>f.state.status==="pending");return!c||c===r}else return!0}runNext(r){var l;const s=Cl(r);if(typeof s=="string"){const c=(l=B(this,fn).get(s))==null?void 0:l.find(f=>f!==r&&f.state.isPaused);return(c==null?void 0:c.continue())??Promise.resolve()}else return Promise.resolve()}clear(){wt.batch(()=>{B(this,Xn).forEach(r=>{this.notify({type:"removed",mutation:r})}),B(this,Xn).clear(),B(this,fn).clear()})}getAll(){return Array.from(B(this,Xn))}find(r){const s={exact:!0,...r};return this.getAll().find(l=>Xp(s,l))}findAll(r={}){return this.getAll().filter(s=>Xp(r,s))}notify(r){wt.batch(()=>{this.listeners.forEach(s=>{s(r)})})}resumePausedMutations(){const r=this.getAll().filter(s=>s.state.isPaused);return wt.batch(()=>Promise.all(r.map(s=>s.continue().catch(cn))))}},Xn=new WeakMap,fn=new WeakMap,ti=new WeakMap,jh);function Cl(n){var r;return(r=n.options.scope)==null?void 0:r.id}function eh(n){return{onFetch:(r,s)=>{var x,b,S,y,E;const l=r.options,c=(S=(b=(x=r.fetchOptions)==null?void 0:x.meta)==null?void 0:b.fetchMore)==null?void 0:S.direction,f=((y=r.state.data)==null?void 0:y.pages)||[],p=((E=r.state.data)==null?void 0:E.pageParams)||[];let h={pages:[],pageParams:[]},g=0;const v=async()=>{let k=!1;const j=A=>{Object.defineProperty(A,"signal",{enumerable:!0,get:()=>(r.signal.aborted?k=!0:r.signal.addEventListener("abort",()=>{k=!0}),r.signal)})},_=qm(r.options,r.fetchOptions),P=async(A,z,O)=>{if(k)return Promise.reject();if(z==null&&A.pages.length)return Promise.resolve(A);const $=(()=>{const pe={client:r.client,queryKey:r.queryKey,pageParam:z,direction:O?"backward":"forward",meta:r.options.meta};return j(pe),pe})(),G=await _($),{maxPages:Z}=r.options,J=O?Eb:Cb;return{pages:J(A.pages,G,Z),pageParams:J(A.pageParams,z,Z)}};if(c&&f.length){const A=c==="backward",z=A?$b:th,O={pages:f,pageParams:p},F=z(l,O);h=await P(O,F,A)}else{const A=n??f.length;do{const z=g===0?p[0]??l.initialPageParam:th(l,h);if(g>0&&z==null)break;h=await P(h,z),g++}while(g<A)}return h};r.options.persister?r.fetchFn=()=>{var k,j;return(j=(k=r.options).persister)==null?void 0:j.call(k,v,{client:r.client,queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},s)}:r.fetchFn=v}}}function th(n,{pages:r,pageParams:s}){const l=r.length-1;return r.length>0?n.getNextPageParam(r[l],r,s[l],s):void 0}function $b(n,{pages:r,pageParams:s}){var l;return r.length>0?(l=n.getPreviousPageParam)==null?void 0:l.call(n,r[0],r,s[0],s):void 0}var Qe,_r,Ar,qo,Go,Or,Yo,Xo,kh,Bb=(kh=class{constructor(n={}){_e(this,Qe);_e(this,_r);_e(this,Ar);_e(this,qo);_e(this,Go);_e(this,Or);_e(this,Yo);_e(this,Xo);we(this,Qe,n.queryCache||new Fb),we(this,_r,n.mutationCache||new Ub),we(this,Ar,n.defaultOptions||{}),we(this,qo,new Map),we(this,Go,new Map),we(this,Or,0)}mount(){wl(this,Or)._++,B(this,Or)===1&&(we(this,Yo,Gm.subscribe(async n=>{n&&(await this.resumePausedMutations(),B(this,Qe).onFocus())})),we(this,Xo,Il.subscribe(async n=>{n&&(await this.resumePausedMutations(),B(this,Qe).onOnline())})))}unmount(){var n,r;wl(this,Or)._--,B(this,Or)===0&&((n=B(this,Yo))==null||n.call(this),we(this,Yo,void 0),(r=B(this,Xo))==null||r.call(this),we(this,Xo,void 0))}isFetching(n){return B(this,Qe).findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return B(this,_r).findAll({...n,status:"pending"}).length}getQueryData(n){var s;const r=this.defaultQueryOptions({queryKey:n});return(s=B(this,Qe).get(r.queryHash))==null?void 0:s.state.data}ensureQueryData(n){const r=this.defaultQueryOptions(n),s=B(this,Qe).build(this,r),l=s.state.data;return l===void 0?this.fetchQuery(n):(n.revalidateIfStale&&s.isStaleByTime(ac(r.staleTime,s))&&this.prefetchQuery(r),Promise.resolve(l))}getQueriesData(n){return B(this,Qe).findAll(n).map(({queryKey:r,state:s})=>{const l=s.data;return[r,l]})}setQueryData(n,r,s){const l=this.defaultQueryOptions({queryKey:n}),c=B(this,Qe).get(l.queryHash),f=c==null?void 0:c.state.data,p=wb(r,f);if(p!==void 0)return B(this,Qe).build(this,l).setData(p,{...s,manual:!0})}setQueriesData(n,r,s){return wt.batch(()=>B(this,Qe).findAll(n).map(({queryKey:l})=>[l,this.setQueryData(l,r,s)]))}getQueryState(n){var s;const r=this.defaultQueryOptions({queryKey:n});return(s=B(this,Qe).get(r.queryHash))==null?void 0:s.state}removeQueries(n){const r=B(this,Qe);wt.batch(()=>{r.findAll(n).forEach(s=>{r.remove(s)})})}resetQueries(n,r){const s=B(this,Qe);return wt.batch(()=>(s.findAll(n).forEach(l=>{l.reset()}),this.refetchQueries({type:"active",...n},r)))}cancelQueries(n,r={}){const s={revert:!0,...r},l=wt.batch(()=>B(this,Qe).findAll(n).map(c=>c.cancel(s)));return Promise.all(l).then(cn).catch(cn)}invalidateQueries(n,r={}){return wt.batch(()=>(B(this,Qe).findAll(n).forEach(s=>{s.invalidate()}),(n==null?void 0:n.refetchType)==="none"?Promise.resolve():this.refetchQueries({...n,type:(n==null?void 0:n.refetchType)??(n==null?void 0:n.type)??"active"},r)))}refetchQueries(n,r={}){const s={...r,cancelRefetch:r.cancelRefetch??!0},l=wt.batch(()=>B(this,Qe).findAll(n).filter(c=>!c.isDisabled()&&!c.isStatic()).map(c=>{let f=c.fetch(void 0,s);return s.throwOnError||(f=f.catch(cn)),c.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(l).then(cn)}fetchQuery(n){const r=this.defaultQueryOptions(n);r.retry===void 0&&(r.retry=!1);const s=B(this,Qe).build(this,r);return s.isStaleByTime(ac(r.staleTime,s))?s.fetch(r):Promise.resolve(s.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(cn).catch(cn)}fetchInfiniteQuery(n){return n.behavior=eh(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(cn).catch(cn)}ensureInfiniteQueryData(n){return n.behavior=eh(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return Il.isOnline()?B(this,_r).resumePausedMutations():Promise.resolve()}getQueryCache(){return B(this,Qe)}getMutationCache(){return B(this,_r)}getDefaultOptions(){return B(this,Ar)}setDefaultOptions(n){we(this,Ar,n)}setQueryDefaults(n,r){B(this,qo).set(Zs(n),{queryKey:n,defaultOptions:r})}getQueryDefaults(n){const r=[...B(this,qo).values()],s={};return r.forEach(l=>{Js(n,l.queryKey)&&Object.assign(s,l.defaultOptions)}),s}setMutationDefaults(n,r){B(this,Go).set(Zs(n),{mutationKey:n,defaultOptions:r})}getMutationDefaults(n){const r=[...B(this,Go).values()],s={};return r.forEach(l=>{Js(n,l.mutationKey)&&Object.assign(s,l.defaultOptions)}),s}defaultQueryOptions(n){if(n._defaulted)return n;const r={...B(this,Ar).queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return r.queryHash||(r.queryHash=Oc(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.queryFn===Mc&&(r.enabled=!1),r}defaultMutationOptions(n){return n!=null&&n._defaulted?n:{...B(this,Ar).mutations,...(n==null?void 0:n.mutationKey)&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){B(this,Qe).clear(),B(this,_r).clear()}},Qe=new WeakMap,_r=new WeakMap,Ar=new WeakMap,qo=new WeakMap,Go=new WeakMap,Or=new WeakMap,Yo=new WeakMap,Xo=new WeakMap,kh),Vb=w.createContext(void 0),Hb=({client:n,children:r})=>(w.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),a.jsx(Vb.Provider,{value:n,children:r}));/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Fl(){return Fl=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(n[l]=s[l])}return n},Fl.apply(this,arguments)}var Mr;(function(n){n.Pop="POP",n.Push="PUSH",n.Replace="REPLACE"})(Mr||(Mr={}));const nh="popstate";function Wb(n){n===void 0&&(n={});function r(l,c){let{pathname:f,search:p,hash:h}=l.location;return cc("",{pathname:f,search:p,hash:h},c.state&&c.state.usr||null,c.state&&c.state.key||"default")}function s(l,c){return typeof c=="string"?c:tg(c)}return Kb(r,s,null,n)}function Rt(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function eg(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Qb(){return Math.random().toString(36).substr(2,8)}function rh(n,r){return{usr:n.state,key:n.key,idx:r}}function cc(n,r,s,l){return s===void 0&&(s=null),Fl({pathname:typeof n=="string"?n:n.pathname,search:"",hash:""},typeof r=="string"?Yl(r):r,{state:s,key:r&&r.key||l||Qb()})}function tg(n){let{pathname:r="/",search:s="",hash:l=""}=n;return s&&s!=="?"&&(r+=s.charAt(0)==="?"?s:"?"+s),l&&l!=="#"&&(r+=l.charAt(0)==="#"?l:"#"+l),r}function Yl(n){let r={};if(n){let s=n.indexOf("#");s>=0&&(r.hash=n.substr(s),n=n.substr(0,s));let l=n.indexOf("?");l>=0&&(r.search=n.substr(l),n=n.substr(0,l)),n&&(r.pathname=n)}return r}function Kb(n,r,s,l){l===void 0&&(l={});let{window:c=document.defaultView,v5Compat:f=!1}=l,p=c.history,h=Mr.Pop,g=null,v=x();v==null&&(v=0,p.replaceState(Fl({},p.state,{idx:v}),""));function x(){return(p.state||{idx:null}).idx}function b(){h=Mr.Pop;let j=x(),_=j==null?null:j-v;v=j,g&&g({action:h,location:k.location,delta:_})}function S(j,_){h=Mr.Push;let P=cc(k.location,j,_);v=x()+1;let A=rh(P,v),z=k.createHref(P);try{p.pushState(A,"",z)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;c.location.assign(z)}f&&g&&g({action:h,location:k.location,delta:1})}function y(j,_){h=Mr.Replace;let P=cc(k.location,j,_);v=x();let A=rh(P,v),z=k.createHref(P);p.replaceState(A,"",z),f&&g&&g({action:h,location:k.location,delta:0})}function E(j){let _=c.location.origin!=="null"?c.location.origin:c.location.href,P=typeof j=="string"?j:tg(j);return P=P.replace(/ $/,"%20"),Rt(_,"No window.location.(origin|href) available to create URL for href: "+P),new URL(P,_)}let k={get action(){return h},get location(){return n(c,p)},listen(j){if(g)throw new Error("A history only accepts one active listener");return c.addEventListener(nh,b),g=j,()=>{c.removeEventListener(nh,b),g=null}},createHref(j){return r(c,j)},createURL:E,encodeLocation(j){let _=E(j);return{pathname:_.pathname,search:_.search,hash:_.hash}},push:S,replace:y,go(j){return p.go(j)}};return k}var oh;(function(n){n.data="data",n.deferred="deferred",n.redirect="redirect",n.error="error"})(oh||(oh={}));function qb(n,r,s){return s===void 0&&(s="/"),Gb(n,r,s)}function Gb(n,r,s,l){let c=typeof r=="string"?Yl(r):r,f=og(c.pathname||"/",s);if(f==null)return null;let p=ng(n);Yb(p);let h=null;for(let g=0;h==null&&g<p.length;++g){let v=aj(f);h=sj(p[g],v)}return h}function ng(n,r,s,l){r===void 0&&(r=[]),s===void 0&&(s=[]),l===void 0&&(l="");let c=(f,p,h)=>{let g={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:p,route:f};g.relativePath.startsWith("/")&&(Rt(g.relativePath.startsWith(l),'Absolute route path "'+g.relativePath+'" nested under path '+('"'+l+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),g.relativePath=g.relativePath.slice(l.length));let v=Vo([l,g.relativePath]),x=s.concat(g);f.children&&f.children.length>0&&(Rt(f.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+v+'".')),ng(f.children,r,x,v)),!(f.path==null&&!f.index)&&r.push({path:v,score:rj(v,f.index),routesMeta:x})};return n.forEach((f,p)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))c(f,p);else for(let g of rg(f.path))c(f,p,g)}),r}function rg(n){let r=n.split("/");if(r.length===0)return[];let[s,...l]=r,c=s.endsWith("?"),f=s.replace(/\?$/,"");if(l.length===0)return c?[f,""]:[f];let p=rg(l.join("/")),h=[];return h.push(...p.map(g=>g===""?f:[f,g].join("/"))),c&&h.push(...p),h.map(g=>n.startsWith("/")&&g===""?"/":g)}function Yb(n){n.sort((r,s)=>r.score!==s.score?s.score-r.score:oj(r.routesMeta.map(l=>l.childrenIndex),s.routesMeta.map(l=>l.childrenIndex)))}const Xb=/^:[\w-]+$/,Zb=3,Jb=2,ej=1,tj=10,nj=-2,sh=n=>n==="*";function rj(n,r){let s=n.split("/"),l=s.length;return s.some(sh)&&(l+=nj),r&&(l+=Jb),s.filter(c=>!sh(c)).reduce((c,f)=>c+(Xb.test(f)?Zb:f===""?ej:tj),l)}function oj(n,r){return n.length===r.length&&n.slice(0,-1).every((l,c)=>l===r[c])?n[n.length-1]-r[r.length-1]:0}function sj(n,r,s){let{routesMeta:l}=n,c={},f="/",p=[];for(let h=0;h<l.length;++h){let g=l[h],v=h===l.length-1,x=f==="/"?r:r.slice(f.length)||"/",b=ij({path:g.relativePath,caseSensitive:g.caseSensitive,end:v},x),S=g.route;if(!b)return null;Object.assign(c,b.params),p.push({params:c,pathname:Vo([f,b.pathname]),pathnameBase:uj(Vo([f,b.pathnameBase])),route:S}),b.pathnameBase!=="/"&&(f=Vo([f,b.pathnameBase]))}return p}function ij(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[s,l]=lj(n.path,n.caseSensitive,n.end),c=r.match(s);if(!c)return null;let f=c[0],p=f.replace(/(.)\/+$/,"$1"),h=c.slice(1);return{params:l.reduce((v,x,b)=>{let{paramName:S,isOptional:y}=x;if(S==="*"){let k=h[b]||"";p=f.slice(0,f.length-k.length).replace(/(.)\/+$/,"$1")}const E=h[b];return y&&!E?v[S]=void 0:v[S]=(E||"").replace(/%2F/g,"/"),v},{}),pathname:f,pathnameBase:p,pattern:n}}function lj(n,r,s){r===void 0&&(r=!1),s===void 0&&(s=!0),eg(n==="*"||!n.endsWith("*")||n.endsWith("/*"),'Route path "'+n+'" will be treated as if it were '+('"'+n.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+n.replace(/\*$/,"/*")+'".'));let l=[],c="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(p,h,g)=>(l.push({paramName:h,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(l.push({paramName:"*"}),c+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?c+="\\/*$":n!==""&&n!=="/"&&(c+="(?:(?=\\/|$))"),[new RegExp(c,r?void 0:"i"),l]}function aj(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return eg(!1,'The URL path "'+n+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+r+").")),n}}function og(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let s=r.endsWith("/")?r.length-1:r.length,l=n.charAt(s);return l&&l!=="/"?null:n.slice(s)||"/"}const Vo=n=>n.join("/").replace(/\/\/+/g,"/"),uj=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/");function cj(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}const sg=["post","put","patch","delete"];new Set(sg);const dj=["get",...sg];new Set(dj);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ll(){return Ll=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var s=arguments[r];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(n[l]=s[l])}return n},Ll.apply(this,arguments)}const fj=w.createContext(null),pj=w.createContext(null),ig=w.createContext(null),Xl=w.createContext(null),Zl=w.createContext({outlet:null,matches:[],isDataRoute:!1}),lg=w.createContext(null);function Dc(){return w.useContext(Xl)!=null}function ag(){return Dc()||Rt(!1),w.useContext(Xl).location}function hj(n,r){return mj(n,r)}function mj(n,r,s,l){Dc()||Rt(!1);let{navigator:c}=w.useContext(ig),{matches:f}=w.useContext(Zl),p=f[f.length-1],h=p?p.params:{};p&&p.pathname;let g=p?p.pathnameBase:"/";p&&p.route;let v=ag(),x;if(r){var b;let j=typeof r=="string"?Yl(r):r;g==="/"||(b=j.pathname)!=null&&b.startsWith(g)||Rt(!1),x=j}else x=v;let S=x.pathname||"/",y=S;if(g!=="/"){let j=g.replace(/^\//,"").split("/");y="/"+S.replace(/^\//,"").split("/").slice(j.length).join("/")}let E=qb(n,{pathname:y}),k=wj(E&&E.map(j=>Object.assign({},j,{params:Object.assign({},h,j.params),pathname:Vo([g,c.encodeLocation?c.encodeLocation(j.pathname).pathname:j.pathname]),pathnameBase:j.pathnameBase==="/"?g:Vo([g,c.encodeLocation?c.encodeLocation(j.pathnameBase).pathname:j.pathnameBase])})),f,s,l);return r&&k?w.createElement(Xl.Provider,{value:{location:Ll({pathname:"/",search:"",hash:"",state:null,key:"default"},x),navigationType:Mr.Pop}},k):k}function gj(){let n=Nj(),r=cj(n)?n.status+" "+n.statusText:n instanceof Error?n.message:JSON.stringify(n),s=n instanceof Error?n.stack:null,c={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},r),s?w.createElement("pre",{style:c},s):null,null)}const vj=w.createElement(gj,null);class yj extends w.Component{constructor(r){super(r),this.state={location:r.location,revalidation:r.revalidation,error:r.error}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,s){return s.location!==r.location||s.revalidation!=="idle"&&r.revalidation==="idle"?{error:r.error,location:r.location,revalidation:r.revalidation}:{error:r.error!==void 0?r.error:s.error,location:s.location,revalidation:r.revalidation||s.revalidation}}componentDidCatch(r,s){console.error("React Router caught the following error during render",r,s)}render(){return this.state.error!==void 0?w.createElement(Zl.Provider,{value:this.props.routeContext},w.createElement(lg.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function xj(n){let{routeContext:r,match:s,children:l}=n,c=w.useContext(fj);return c&&c.static&&c.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=s.route.id),w.createElement(Zl.Provider,{value:r},l)}function wj(n,r,s,l){var c;if(r===void 0&&(r=[]),s===void 0&&(s=null),l===void 0&&(l=null),n==null){var f;if(!s)return null;if(s.errors)n=s.matches;else if((f=l)!=null&&f.v7_partialHydration&&r.length===0&&!s.initialized&&s.matches.length>0)n=s.matches;else return null}let p=n,h=(c=s)==null?void 0:c.errors;if(h!=null){let x=p.findIndex(b=>b.route.id&&(h==null?void 0:h[b.route.id])!==void 0);x>=0||Rt(!1),p=p.slice(0,Math.min(p.length,x+1))}let g=!1,v=-1;if(s&&l&&l.v7_partialHydration)for(let x=0;x<p.length;x++){let b=p[x];if((b.route.HydrateFallback||b.route.hydrateFallbackElement)&&(v=x),b.route.id){let{loaderData:S,errors:y}=s,E=b.route.loader&&S[b.route.id]===void 0&&(!y||y[b.route.id]===void 0);if(b.route.lazy||E){g=!0,v>=0?p=p.slice(0,v+1):p=[p[0]];break}}}return p.reduceRight((x,b,S)=>{let y,E=!1,k=null,j=null;s&&(y=h&&b.route.id?h[b.route.id]:void 0,k=b.route.errorElement||vj,g&&(v<0&&S===0?(Sj("route-fallback"),E=!0,j=null):v===S&&(E=!0,j=b.route.hydrateFallbackElement||null)));let _=r.concat(p.slice(0,S+1)),P=()=>{let A;return y?A=k:E?A=j:b.route.Component?A=w.createElement(b.route.Component,null):b.route.element?A=b.route.element:A=x,w.createElement(xj,{match:b,routeContext:{outlet:x,matches:_,isDataRoute:s!=null},children:A})};return s&&(b.route.ErrorBoundary||b.route.errorElement||S===0)?w.createElement(yj,{location:s.location,revalidation:s.revalidation,component:k,error:y,children:P(),routeContext:{outlet:null,matches:_,isDataRoute:!0}}):P()},null)}var ug=function(n){return n.UseBlocker="useBlocker",n.UseLoaderData="useLoaderData",n.UseActionData="useActionData",n.UseRouteError="useRouteError",n.UseNavigation="useNavigation",n.UseRouteLoaderData="useRouteLoaderData",n.UseMatches="useMatches",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n.UseRouteId="useRouteId",n}(ug||{});function bj(n){let r=w.useContext(pj);return r||Rt(!1),r}function jj(n){let r=w.useContext(Zl);return r||Rt(!1),r}function kj(n){let r=jj(),s=r.matches[r.matches.length-1];return s.route.id||Rt(!1),s.route.id}function Nj(){var n;let r=w.useContext(lg),s=bj(ug.UseRouteError),l=kj();return r!==void 0?r:(n=s.errors)==null?void 0:n[l]}const ih={};function Sj(n,r,s){ih[n]||(ih[n]=!0)}function Cj(n,r){n==null||n.v7_startTransition,n==null||n.v7_relativeSplatPath}function dc(n){Rt(!1)}function Ej(n){let{basename:r="/",children:s=null,location:l,navigationType:c=Mr.Pop,navigator:f,static:p=!1,future:h}=n;Dc()&&Rt(!1);let g=r.replace(/^\/*/,"/"),v=w.useMemo(()=>({basename:g,navigator:f,static:p,future:Ll({v7_relativeSplatPath:!1},h)}),[g,h,f,p]);typeof l=="string"&&(l=Yl(l));let{pathname:x="/",search:b="",hash:S="",state:y=null,key:E="default"}=l,k=w.useMemo(()=>{let j=og(x,g);return j==null?null:{location:{pathname:j,search:b,hash:S,state:y,key:E},navigationType:c}},[g,x,b,S,y,E,c]);return k==null?null:w.createElement(ig.Provider,{value:v},w.createElement(Xl.Provider,{children:s,value:k}))}function Pj(n){let{children:r,location:s}=n;return hj(fc(r),s)}new Promise(()=>{});function fc(n,r){r===void 0&&(r=[]);let s=[];return w.Children.forEach(n,(l,c)=>{if(!w.isValidElement(l))return;let f=[...r,c];if(l.type===w.Fragment){s.push.apply(s,fc(l.props.children,f));return}l.type!==dc&&Rt(!1),!l.props.index||!l.props.children||Rt(!1);let p={id:l.props.id||f.join("-"),caseSensitive:l.props.caseSensitive,element:l.props.element,Component:l.props.Component,index:l.props.index,path:l.props.path,loader:l.props.loader,action:l.props.action,errorElement:l.props.errorElement,ErrorBoundary:l.props.ErrorBoundary,hasErrorBoundary:l.props.ErrorBoundary!=null||l.props.errorElement!=null,shouldRevalidate:l.props.shouldRevalidate,handle:l.props.handle,lazy:l.props.lazy};l.props.children&&(p.children=fc(l.props.children,f)),s.push(p)}),s}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Tj="6";try{window.__reactRouterVersion=Tj}catch{}const Rj="startTransition",lh=mc[Rj];function _j(n){let{basename:r,children:s,future:l,window:c}=n,f=w.useRef();f.current==null&&(f.current=Wb({window:c,v5Compat:!0}));let p=f.current,[h,g]=w.useState({action:p.action,location:p.location}),{v7_startTransition:v}=l||{},x=w.useCallback(b=>{v&&lh?lh(()=>g(b)):g(b)},[g,v]);return w.useLayoutEffect(()=>p.listen(x),[p,x]),w.useEffect(()=>Cj(l),[l]),w.createElement(Ej,{basename:r,children:s,location:h.location,navigationType:h.action,navigator:p,future:l})}var ah;(function(n){n.UseScrollRestoration="useScrollRestoration",n.UseSubmit="useSubmit",n.UseSubmitFetcher="useSubmitFetcher",n.UseFetcher="useFetcher",n.useViewTransitionState="useViewTransitionState"})(ah||(ah={}));var uh;(function(n){n.UseFetcher="useFetcher",n.UseFetchers="useFetchers",n.UseScrollRestoration="useScrollRestoration"})(uh||(uh={}));const cg={personalInfo:{ownerName:"",age:0,maritalStatus:"",familySize:0,education:"",phone:"",refereePhone:"",address:""},projectDescription:{projectSummary:"",projectName:"",projectLocation:"",projectType:"",projectGoals:"",targetMarket:"",expectedRevenue:0,startupCost:0},marketStudy:{products:"",services:"",hasCompetitors:!1,competitorsCount:0,competitorProducts:"",competitorProfitStrategies:[],competitorPricing:"same",competitorPromotionMethods:[],competitor1Customers:0,competitor2Customers:0},swotAnalysis:{strengths:"",weaknesses:"",opportunities:"",threats:""},marketingMix:{product:"",price:"",place:"",promotion:"",people:""},productionRequirements:{equipment:"",materials:"",humanResources:"",location:"",licenses:""},financialStudy:{fixedCosts:[{name:"الرواتب الثابتة (صاحب المشروع + الموظفين الثابتين)",monthly:0},{name:"الإيجار",monthly:0},{name:"الصيانة",monthly:0},{name:"التسويق والدعاية",monthly:0},{name:"تكاليف ثابتة أخرى",monthly:0}],variableCosts:[{name:"المواد الخام",monthly:0},{name:"أجور العمال المباشرين",monthly:0},{name:"فواتير الماء والكهرباء والهاتف والإنترنت",monthly:0},{name:"أجور النقل والمواصلات",monthly:0},{name:"تكاليف متغيرة أخرى",monthly:0}],profitRows:[{id:1,name:"",units:0,costPerUnit:0,pricePerUnit:0}],annualSales:[{id:1,name:"منتج (1)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))},{id:2,name:"منتج (2)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))},{id:3,name:"منتج (3)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))},{id:4,name:"منتج (4)",monthlyData:Array.from({length:12},()=>({quantity:0,price:0}))}],breakEvenInputs:{salePrice:0,variableCost:0}},lastUpdated:new Date,currentStep:0},ch=typeof window<"u"&&window.electronAPI,Aj=(n,r)=>{switch(r.type){case"UPDATE_PERSONAL_INFO":return{...n,personalInfo:{...n.personalInfo,...r.payload},lastUpdated:new Date};case"UPDATE_PROJECT_DESCRIPTION":return{...n,projectDescription:{...n.projectDescription,...r.payload},lastUpdated:new Date};case"UPDATE_MARKET_STUDY":return{...n,marketStudy:{...n.marketStudy,...r.payload},lastUpdated:new Date};case"UPDATE_SWOT_ANALYSIS":return{...n,swotAnalysis:{...n.swotAnalysis,...r.payload},lastUpdated:new Date};case"UPDATE_MARKETING_MIX":return{...n,marketingMix:{...n.marketingMix,...r.payload},lastUpdated:new Date};case"UPDATE_PRODUCTION_REQUIREMENTS":return{...n,productionRequirements:{...n.productionRequirements,...r.payload},lastUpdated:new Date};case"UPDATE_FINANCIAL_STUDY":return{...n,financialStudy:{...n.financialStudy,...r.payload},lastUpdated:new Date};case"SET_CURRENT_STEP":return{...n,currentStep:r.payload,lastUpdated:new Date};case"LOAD_PROJECT_DATA":return r.payload;case"RESET_PROJECT_DATA":return cg;case"AUTO_SAVE":return{...n,lastUpdated:new Date};default:return n}},dg=w.createContext(void 0),Yu="project-business-plan-data",Oj=({children:n})=>{const[r,s]=w.useReducer(Aj,cg);w.useEffect(()=>{y(),ch&&(window.electronAPI.onSaveProject(()=>{S()}),window.electronAPI.onLoadProject(P=>{E(P)}),window.electronAPI.onExportPDF(()=>{Je({title:"تصدير PDF",description:"ميزة تصدير PDF ستكون متاحة قريباً"})}),window.electronAPI.onExportExcel(()=>{Je({title:"تصدير Excel",description:"ميزة تصدير Excel ستكون متاحة قريباً"})}))},[]),w.useEffect(()=>{const P=setTimeout(()=>{b()},1e3);return()=>clearTimeout(P)},[r]);const l=P=>{s({type:"UPDATE_PERSONAL_INFO",payload:P})},c=P=>{s({type:"UPDATE_PROJECT_DESCRIPTION",payload:P})},f=P=>{s({type:"UPDATE_MARKET_STUDY",payload:P})},p=P=>{s({type:"UPDATE_SWOT_ANALYSIS",payload:P})},h=P=>{s({type:"UPDATE_MARKETING_MIX",payload:P})},g=P=>{s({type:"UPDATE_PRODUCTION_REQUIREMENTS",payload:P})},v=P=>{s({type:"UPDATE_FINANCIAL_STUDY",payload:P})},x=P=>{s({type:"SET_CURRENT_STEP",payload:P})},b=()=>{try{const P={...r,lastUpdated:new Date().toISOString()};localStorage.setItem(Yu,JSON.stringify(P))}catch(P){console.error("خطأ في حفظ البيانات:",P),Je({title:"خطأ في الحفظ",description:"حدث خطأ أثناء حفظ البيانات",variant:"destructive"})}},S=async()=>{if(!ch){Je({title:"غير متاح",description:"هذه الميزة متاحة فقط في تطبيق سطح المكتب",variant:"destructive"});return}try{const P={...r,lastUpdated:new Date().toISOString()},A=await window.electronAPI.saveProjectFile(P);A.success?Je({title:"تم الحفظ بنجاح",description:`تم حفظ المشروع في: ${A.path}`}):Je({title:"خطأ في الحفظ",description:A.error||"حدث خطأ أثناء حفظ الملف",variant:"destructive"})}catch(P){console.error("خطأ في حفظ الملف:",P),Je({title:"خطأ في الحفظ",description:"حدث خطأ أثناء حفظ الملف",variant:"destructive"})}},y=()=>{try{const P=localStorage.getItem(Yu);if(P){const A=JSON.parse(P);A.lastUpdated=new Date(A.lastUpdated),s({type:"LOAD_PROJECT_DATA",payload:A}),Je({title:"تم تحميل البيانات",description:"تم استرداد بيانات المشروع المحفوظة"})}}catch(P){console.error("خطأ في تحميل البيانات:",P),Je({title:"خطأ في التحميل",description:"حدث خطأ أثناء تحميل البيانات المحفوظة",variant:"destructive"})}},E=P=>{try{const A={...P,lastUpdated:new Date(P.lastUpdated)};s({type:"LOAD_PROJECT_DATA",payload:A}),Je({title:"تم تحميل المشروع",description:"تم تحميل بيانات المشروع من الملف بنجاح"})}catch(A){console.error("خطأ في تحميل الملف:",A),Je({title:"خطأ في التحميل",description:"حدث خطأ أثناء تحميل بيانات المشروع",variant:"destructive"})}},_={projectData:r,updatePersonalInfo:l,updateProjectDescription:c,updateMarketStudy:f,updateSwotAnalysis:p,updateMarketingMix:h,updateProductionRequirements:g,updateFinancialStudy:v,setCurrentStep:x,saveProject:b,saveProjectToFile:S,loadProject:y,loadProjectFromFile:E,resetProject:()=>{s({type:"RESET_PROJECT_DATA"}),localStorage.removeItem(Yu),Je({title:"تم إعادة التعيين",description:"تم مسح جميع بيانات المشروع"})},isDataValid:P=>{switch(P){case 0:return!!(r.personalInfo.ownerName&&r.personalInfo.phone&&r.projectDescription.projectName);case 1:return!!(r.marketStudy.products||r.marketStudy.services);case 2:return!!(r.swotAnalysis.strengths&&r.swotAnalysis.weaknesses);case 3:return!!(r.marketingMix.product&&r.marketingMix.price);case 4:return!!(r.productionRequirements.equipment||r.productionRequirements.materials);case 5:return r.financialStudy.fixedCosts.some(A=>A.monthly>0)||r.financialStudy.variableCosts.some(A=>A.monthly>0);default:return!0}}};return a.jsx(dg.Provider,{value:_,children:n})},co=()=>{const n=w.useContext(dg);if(!n)throw new Error("useProject must be used within a ProjectProvider");return n},qt=w.forwardRef(({className:n,...r},s)=>a.jsx("div",{ref:s,className:Se("rounded-lg border bg-card text-card-foreground shadow-sm",n),...r}));qt.displayName="Card";const Gt=w.forwardRef(({className:n,...r},s)=>a.jsx("div",{ref:s,className:Se("flex flex-col space-y-1.5 p-6",n),...r}));Gt.displayName="CardHeader";const Yt=w.forwardRef(({className:n,...r},s)=>a.jsx("h3",{ref:s,className:Se("text-2xl font-semibold leading-none tracking-tight",n),...r}));Yt.displayName="CardTitle";const fg=w.forwardRef(({className:n,...r},s)=>a.jsx("p",{ref:s,className:Se("text-sm text-muted-foreground",n),...r}));fg.displayName="CardDescription";const Xt=w.forwardRef(({className:n,...r},s)=>a.jsx("div",{ref:s,className:Se("p-6 pt-0",n),...r}));Xt.displayName="CardContent";const pg=w.forwardRef(({className:n,...r},s)=>a.jsx("div",{ref:s,className:Se("flex items-center p-6 pt-0",n),...r}));pg.displayName="CardFooter";const Mj=wc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),pn=w.forwardRef(({className:n,variant:r,size:s,asChild:l=!1,...c},f)=>{const p=l?ty:"button";return a.jsx(p,{className:Se(Mj({variant:r,size:s,className:n})),ref:f,...c})});pn.displayName="Button";const Dj=({currentStep:n,totalSteps:r})=>{const{isDataValid:s}=co(),l=(n+1)/r*100,c=["المعلومات الشخصية","دراسة السوق","تحليل SWOT","المزيج التسويقي","مستلزمات الإنتاج","الدراسة المالية","الإنهاء والحفظ"];return a.jsxs("div",{className:"w-full space-y-6 p-6 rounded-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-3d border border-white/20 dark:border-slate-700/30",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg animate-pulse"}),a.jsx("span",{className:"text-lg font-bold text-gradient-primary",children:"التقدم"})]}),a.jsx("div",{className:"px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-200 dark:border-blue-700/50",children:a.jsxs("span",{className:"text-sm font-bold text-blue-700 dark:text-blue-300",children:["الخطوة ",n+1,"/",r]})})]}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner",children:a.jsxs("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-700 ease-out shadow-lg relative overflow-hidden",style:{width:`${l}%`},children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/30 to-transparent"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"})]})}),a.jsx("div",{className:"absolute top-0 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-blue-500 transition-all duration-700 ease-out -mt-0.5",style:{left:`calc(${l}% - 8px)`},children:a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})})]}),a.jsx("div",{className:"hidden lg:flex justify-between items-center gap-2",children:c.map((f,p)=>{const h=p<n,g=p===n,v=s(p);return a.jsxs("div",{className:"flex flex-col items-center space-y-2 flex-1",children:[a.jsxs("div",{className:Se("relative flex items-center justify-center w-12 h-12 rounded-full border-3 transition-all duration-500 shadow-lg",h?"bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 text-white shadow-green-200 dark:shadow-green-900/50 scale-110":g?v?"bg-gradient-to-r from-blue-500 to-purple-500 border-blue-400 text-white shadow-blue-200 dark:shadow-blue-900/50 scale-110 animate-glow":"bg-gradient-to-r from-amber-500 to-orange-500 border-amber-400 text-white shadow-amber-200 dark:shadow-amber-900/50 scale-110 animate-pulse":"bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"),children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-white/20 to-transparent rounded-full"}),h?a.jsx(bc,{className:"w-6 h-6 relative z-10"}):g&&!v?a.jsx(rm,{className:"w-6 h-6 relative z-10"}):a.jsx("div",{className:"w-3 h-3 bg-current rounded-full relative z-10"}),g&&a.jsx("div",{className:"absolute inset-0 rounded-full bg-current opacity-20 animate-ping"})]}),a.jsx("span",{className:Se("text-center text-xs font-medium leading-tight px-2 py-1 rounded-lg transition-all duration-300",g?"text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 shadow-sm":h?"text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30":"text-gray-500 dark:text-gray-400"),children:f}),p<c.length-1&&a.jsx("div",{className:"absolute top-6 left-1/2 w-full h-0.5 -z-10",children:a.jsx("div",{className:Se("h-full transition-all duration-500",h?"bg-gradient-to-r from-green-400 to-emerald-400":p<n?"bg-gradient-to-r from-blue-400 to-purple-400":"bg-gray-300 dark:bg-gray-600")})})]},p)})})]})},ke=w.forwardRef(({className:n,type:r,...s},l)=>a.jsx("input",{type:r,className:Se("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),ref:l,...s}));ke.displayName="Input";var Ij="Label",hg=w.forwardRef((n,r)=>a.jsx($e.label,{...n,ref:r,onMouseDown:s=>{var c;s.target.closest("button, input, select, textarea")||((c=n.onMouseDown)==null||c.call(n,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));hg.displayName=Ij;var mg=hg;const Fj=wc("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ee=w.forwardRef(({className:n,...r},s)=>a.jsx(mg,{ref:s,className:Se(Fj(),n),...r}));Ee.displayName=mg.displayName;const Er=({label:n,id:r,type:s="text"})=>{const{projectData:l,updatePersonalInfo:c}=co(),f=l.personalInfo[r],p=h=>{const g=s==="number"?Number(h.target.value):h.target.value;c({[r]:g})};return a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 items-center gap-4 group",children:[a.jsx(Ee,{htmlFor:r,className:"md:text-right font-semibold text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300",children:n}),a.jsx(ke,{id:r,type:s,value:f||"",onChange:p,className:"col-span-1 md:col-span-2 input-3d shadow-lg border-2 border-white/50 dark:border-slate-600/50 focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-300 hover:shadow-xl"})]})},Lj=()=>a.jsxs(qt,{className:"w-full card-3d shadow-3d-hover border-glow bg-gradient-to-br from-sky-50/90 via-blue-50/90 to-indigo-50/90 dark:from-sky-900/30 dark:via-blue-900/30 dark:to-indigo-900/30 backdrop-blur-sm relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-sky-400/5 to-blue-400/5 animate-pulse"}),a.jsxs(Gt,{className:"border-b border-sky-200/50 dark:border-sky-700/50 relative z-10 bg-gradient-to-r from-white/50 to-sky-50/50 dark:from-slate-800/50 dark:to-sky-900/50",children:[a.jsxs(Yt,{className:"flex items-center gap-4 text-2xl md:text-3xl font-bold",children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full blur opacity-50"}),a.jsx("div",{className:"relative w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg",children:a.jsx(im,{className:"h-6 w-6 text-white"})})]}),a.jsx("span",{className:"text-gradient-primary",children:"المعلومات الشخصية"})]}),a.jsx("div",{className:"mt-2 w-16 h-1 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full shadow-lg"})]}),a.jsx(Xt,{className:"pt-8 pb-6 relative z-10",children:a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsx(Er,{label:"إسم صاحب/ة المشروع",id:"ownerName"}),a.jsx(Er,{label:"العمر",id:"age",type:"number"})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsx(Er,{label:"الحالة الإجتماعية",id:"maritalStatus"}),a.jsx(Er,{label:"عدد أفراد الأسرة",id:"familySize",type:"number"})]}),a.jsx(Er,{label:"المؤهل العلمي",id:"education"}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsx(Er,{label:"رقم الهاتف",id:"phone"}),a.jsx(Er,{label:"رقم هاتف شخص معرف",id:"refereePhone"})]}),a.jsx(Er,{label:"مكان السكن",id:"address"})]})}),a.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-sky-500 to-blue-500 opacity-50"})]}),hn=w.forwardRef(({className:n,...r},s)=>a.jsx("textarea",{className:Se("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),ref:s,...r}));hn.displayName="Textarea";var zj=w.createContext(void 0);function gg(n){const r=w.useContext(zj);return n||r||"ltr"}var Xu="rovingFocusGroup.onEntryFocus",Uj={bubbles:!1,cancelable:!0},si="RovingFocusGroup",[pc,vg,$j]=Th(si),[Bj,yg]=zr(si,[$j]),[Vj,Hj]=Bj(si),xg=w.forwardRef((n,r)=>a.jsx(pc.Provider,{scope:n.__scopeRovingFocusGroup,children:a.jsx(pc.Slot,{scope:n.__scopeRovingFocusGroup,children:a.jsx(Wj,{...n,ref:r})})}));xg.displayName=si;var Wj=w.forwardRef((n,r)=>{const{__scopeRovingFocusGroup:s,orientation:l,loop:c=!1,dir:f,currentTabStopId:p,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:g,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...b}=n,S=w.useRef(null),y=at(r,S),E=gg(f),[k,j]=Ul({prop:p,defaultProp:h??null,onChange:g,caller:si}),[_,P]=w.useState(!1),A=Zn(v),z=vg(s),O=w.useRef(!1),[F,$]=w.useState(0);return w.useEffect(()=>{const G=S.current;if(G)return G.addEventListener(Xu,A),()=>G.removeEventListener(Xu,A)},[A]),a.jsx(Vj,{scope:s,orientation:l,dir:E,loop:c,currentTabStopId:k,onItemFocus:w.useCallback(G=>j(G),[j]),onItemShiftTab:w.useCallback(()=>P(!0),[]),onFocusableItemAdd:w.useCallback(()=>$(G=>G+1),[]),onFocusableItemRemove:w.useCallback(()=>$(G=>G-1),[]),children:a.jsx($e.div,{tabIndex:_||F===0?-1:0,"data-orientation":l,...b,ref:y,style:{outline:"none",...n.style},onMouseDown:Oe(n.onMouseDown,()=>{O.current=!0}),onFocus:Oe(n.onFocus,G=>{const Z=!O.current;if(G.target===G.currentTarget&&Z&&!_){const J=new CustomEvent(Xu,Uj);if(G.currentTarget.dispatchEvent(J),!J.defaultPrevented){const pe=z().filter(se=>se.focusable),te=pe.find(se=>se.active),ge=pe.find(se=>se.id===k),ve=[te,ge,...pe].filter(Boolean).map(se=>se.ref.current);jg(ve,x)}}O.current=!1}),onBlur:Oe(n.onBlur,()=>P(!1))})})}),wg="RovingFocusGroupItem",bg=w.forwardRef((n,r)=>{const{__scopeRovingFocusGroup:s,focusable:l=!0,active:c=!1,tabStopId:f,children:p,...h}=n,g=Mw(),v=f||g,x=Hj(wg,s),b=x.currentTabStopId===v,S=vg(s),{onFocusableItemAdd:y,onFocusableItemRemove:E,currentTabStopId:k}=x;return w.useEffect(()=>{if(l)return y(),()=>E()},[l,y,E]),a.jsx(pc.ItemSlot,{scope:s,id:v,focusable:l,active:c,children:a.jsx($e.span,{tabIndex:b?0:-1,"data-orientation":x.orientation,...h,ref:r,onMouseDown:Oe(n.onMouseDown,j=>{l?x.onItemFocus(v):j.preventDefault()}),onFocus:Oe(n.onFocus,()=>x.onItemFocus(v)),onKeyDown:Oe(n.onKeyDown,j=>{if(j.key==="Tab"&&j.shiftKey){x.onItemShiftTab();return}if(j.target!==j.currentTarget)return;const _=qj(j,x.orientation,x.dir);if(_!==void 0){if(j.metaKey||j.ctrlKey||j.altKey||j.shiftKey)return;j.preventDefault();let A=S().filter(z=>z.focusable).map(z=>z.ref.current);if(_==="last")A.reverse();else if(_==="prev"||_==="next"){_==="prev"&&A.reverse();const z=A.indexOf(j.currentTarget);A=x.loop?Gj(A,z+1):A.slice(z+1)}setTimeout(()=>jg(A))}}),children:typeof p=="function"?p({isCurrentTabStop:b,hasTabStop:k!=null}):p})})});bg.displayName=wg;var Qj={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Kj(n,r){return r!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function qj(n,r,s){const l=Kj(n.key,s);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return Qj[l]}function jg(n,r=!1){const s=document.activeElement;for(const l of n)if(l===s||(l.focus({preventScroll:r}),document.activeElement!==s))return}function Gj(n,r){return n.map((s,l)=>n[(r+l)%n.length])}var Yj=xg,Xj=bg;function kg(n){const r=w.useRef({value:n,previous:n});return w.useMemo(()=>(r.current.value!==n&&(r.current.previous=r.current.value,r.current.value=n),r.current.previous),[n])}var Ic="Radio",[Zj,Ng]=zr(Ic),[Jj,e2]=Zj(Ic),Sg=w.forwardRef((n,r)=>{const{__scopeRadio:s,name:l,checked:c=!1,required:f,disabled:p,value:h="on",onCheck:g,form:v,...x}=n,[b,S]=w.useState(null),y=at(r,j=>S(j)),E=w.useRef(!1),k=b?v||!!b.closest("form"):!0;return a.jsxs(Jj,{scope:s,checked:c,disabled:p,children:[a.jsx($e.button,{type:"button",role:"radio","aria-checked":c,"data-state":Tg(c),"data-disabled":p?"":void 0,disabled:p,value:h,...x,ref:y,onClick:Oe(n.onClick,j=>{c||g==null||g(),k&&(E.current=j.isPropagationStopped(),E.current||j.stopPropagation())})}),k&&a.jsx(Pg,{control:b,bubbles:!E.current,name:l,value:h,checked:c,required:f,disabled:p,form:v,style:{transform:"translateX(-100%)"}})]})});Sg.displayName=Ic;var Cg="RadioIndicator",Eg=w.forwardRef((n,r)=>{const{__scopeRadio:s,forceMount:l,...c}=n,f=e2(Cg,s);return a.jsx(ni,{present:l||f.checked,children:a.jsx($e.span,{"data-state":Tg(f.checked),"data-disabled":f.disabled?"":void 0,...c,ref:r})})});Eg.displayName=Cg;var t2="RadioBubbleInput",Pg=w.forwardRef(({__scopeRadio:n,control:r,checked:s,bubbles:l=!0,...c},f)=>{const p=w.useRef(null),h=at(p,f),g=kg(s),v=Rc(r);return w.useEffect(()=>{const x=p.current;if(!x)return;const b=window.HTMLInputElement.prototype,y=Object.getOwnPropertyDescriptor(b,"checked").set;if(g!==s&&y){const E=new Event("click",{bubbles:l});y.call(x,s),x.dispatchEvent(E)}},[g,s,l]),a.jsx($e.input,{type:"radio","aria-hidden":!0,defaultChecked:s,...c,tabIndex:-1,ref:h,style:{...c.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Pg.displayName=t2;function Tg(n){return n?"checked":"unchecked"}var n2=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Jl="RadioGroup",[r2,V2]=zr(Jl,[yg,Ng]),Rg=yg(),_g=Ng(),[o2,s2]=r2(Jl),Ag=w.forwardRef((n,r)=>{const{__scopeRadioGroup:s,name:l,defaultValue:c,value:f,required:p=!1,disabled:h=!1,orientation:g,dir:v,loop:x=!0,onValueChange:b,...S}=n,y=Rg(s),E=gg(v),[k,j]=Ul({prop:f,defaultProp:c??null,onChange:b,caller:Jl});return a.jsx(o2,{scope:s,name:l,required:p,disabled:h,value:k,onValueChange:j,children:a.jsx(Yj,{asChild:!0,...y,orientation:g,dir:E,loop:x,children:a.jsx($e.div,{role:"radiogroup","aria-required":p,"aria-orientation":g,"data-disabled":h?"":void 0,dir:E,...S,ref:r})})})});Ag.displayName=Jl;var Og="RadioGroupItem",Mg=w.forwardRef((n,r)=>{const{__scopeRadioGroup:s,disabled:l,...c}=n,f=s2(Og,s),p=f.disabled||l,h=Rg(s),g=_g(s),v=w.useRef(null),x=at(r,v),b=f.value===c.value,S=w.useRef(!1);return w.useEffect(()=>{const y=k=>{n2.includes(k.key)&&(S.current=!0)},E=()=>S.current=!1;return document.addEventListener("keydown",y),document.addEventListener("keyup",E),()=>{document.removeEventListener("keydown",y),document.removeEventListener("keyup",E)}},[]),a.jsx(Xj,{asChild:!0,...h,focusable:!p,active:b,children:a.jsx(Sg,{disabled:p,required:f.required,checked:b,...g,...c,name:f.name,ref:x,onCheck:()=>f.onValueChange(c.value),onKeyDown:Oe(y=>{y.key==="Enter"&&y.preventDefault()}),onFocus:Oe(c.onFocus,()=>{var y;S.current&&((y=v.current)==null||y.click())})})})});Mg.displayName=Og;var i2="RadioGroupIndicator",Dg=w.forwardRef((n,r)=>{const{__scopeRadioGroup:s,...l}=n,c=_g(s);return a.jsx(Eg,{...c,...l,ref:r})});Dg.displayName=i2;var Ig=Ag,Fg=Mg,l2=Dg;const Dr=w.forwardRef(({className:n,...r},s)=>a.jsx(Ig,{className:Se("grid gap-2",n),...r,ref:s}));Dr.displayName=Ig.displayName;const ea=w.forwardRef(({className:n,...r},s)=>a.jsx(Fg,{ref:s,className:Se("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),...r,children:a.jsx(l2,{className:"flex items-center justify-center",children:a.jsx(rx,{className:"h-2.5 w-2.5 fill-current text-current"})})}));ea.displayName=Fg.displayName;var ta="Checkbox",[a2,H2]=zr(ta),[u2,Fc]=a2(ta);function c2(n){const{__scopeCheckbox:r,checked:s,children:l,defaultChecked:c,disabled:f,form:p,name:h,onCheckedChange:g,required:v,value:x="on",internal_do_not_use_render:b}=n,[S,y]=Ul({prop:s,defaultProp:c??!1,onChange:g,caller:ta}),[E,k]=w.useState(null),[j,_]=w.useState(null),P=w.useRef(!1),A=E?!!p||!!E.closest("form"):!0,z={checked:S,disabled:f,setChecked:y,control:E,setControl:k,name:h,form:p,value:x,hasConsumerStoppedPropagationRef:P,required:v,defaultChecked:Ir(c)?!1:c,isFormControl:A,bubbleInput:j,setBubbleInput:_};return a.jsx(u2,{scope:r,...z,children:d2(b)?b(z):l})}var Lg="CheckboxTrigger",zg=w.forwardRef(({__scopeCheckbox:n,onKeyDown:r,onClick:s,...l},c)=>{const{control:f,value:p,disabled:h,checked:g,required:v,setControl:x,setChecked:b,hasConsumerStoppedPropagationRef:S,isFormControl:y,bubbleInput:E}=Fc(Lg,n),k=at(c,x),j=w.useRef(g);return w.useEffect(()=>{const _=f==null?void 0:f.form;if(_){const P=()=>b(j.current);return _.addEventListener("reset",P),()=>_.removeEventListener("reset",P)}},[f,b]),a.jsx($e.button,{type:"button",role:"checkbox","aria-checked":Ir(g)?"mixed":g,"aria-required":v,"data-state":Hg(g),"data-disabled":h?"":void 0,disabled:h,value:p,...l,ref:k,onKeyDown:Oe(r,_=>{_.key==="Enter"&&_.preventDefault()}),onClick:Oe(s,_=>{b(P=>Ir(P)?!0:!P),E&&y&&(S.current=_.isPropagationStopped(),S.current||_.stopPropagation())})})});zg.displayName=Lg;var Lc=w.forwardRef((n,r)=>{const{__scopeCheckbox:s,name:l,checked:c,defaultChecked:f,required:p,disabled:h,value:g,onCheckedChange:v,form:x,...b}=n;return a.jsx(c2,{__scopeCheckbox:s,checked:c,defaultChecked:f,disabled:h,required:p,onCheckedChange:v,name:l,form:x,value:g,internal_do_not_use_render:({isFormControl:S})=>a.jsxs(a.Fragment,{children:[a.jsx(zg,{...b,ref:r,__scopeCheckbox:s}),S&&a.jsx(Vg,{__scopeCheckbox:s})]})})});Lc.displayName=ta;var Ug="CheckboxIndicator",$g=w.forwardRef((n,r)=>{const{__scopeCheckbox:s,forceMount:l,...c}=n,f=Fc(Ug,s);return a.jsx(ni,{present:l||Ir(f.checked)||f.checked===!0,children:a.jsx($e.span,{"data-state":Hg(f.checked),"data-disabled":f.disabled?"":void 0,...c,ref:r,style:{pointerEvents:"none",...n.style}})})});$g.displayName=Ug;var Bg="CheckboxBubbleInput",Vg=w.forwardRef(({__scopeCheckbox:n,...r},s)=>{const{control:l,hasConsumerStoppedPropagationRef:c,checked:f,defaultChecked:p,required:h,disabled:g,name:v,value:x,form:b,bubbleInput:S,setBubbleInput:y}=Fc(Bg,n),E=at(s,y),k=kg(f),j=Rc(l);w.useEffect(()=>{const P=S;if(!P)return;const A=window.HTMLInputElement.prototype,O=Object.getOwnPropertyDescriptor(A,"checked").set,F=!c.current;if(k!==f&&O){const $=new Event("click",{bubbles:F});P.indeterminate=Ir(f),O.call(P,Ir(f)?!1:f),P.dispatchEvent($)}},[S,k,f,c]);const _=w.useRef(Ir(f)?!1:f);return a.jsx($e.input,{type:"checkbox","aria-hidden":!0,defaultChecked:p??_.current,required:h,disabled:g,name:v,value:x,form:b,...r,tabIndex:-1,ref:E,style:{...r.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});Vg.displayName=Bg;function d2(n){return typeof n=="function"}function Ir(n){return n==="indeterminate"}function Hg(n){return Ir(n)?"indeterminate":n?"checked":"unchecked"}const ii=w.forwardRef(({className:n,...r},s)=>a.jsx(Lc,{ref:s,className:Se("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",n),...r,children:a.jsx($g,{className:Se("flex items-center justify-center text-current"),children:a.jsx(tx,{className:"h-4 w-4"})})}));ii.displayName=Lc.displayName;const Ft=({label:n,id:r,children:s,type:l="text"})=>{const{projectData:c,updateProjectDescription:f}=co();if(s)return a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 items-start gap-4",children:[a.jsx(Ee,{htmlFor:r,className:"md:text-right pt-2",children:n}),a.jsx("div",{className:"col-span-1 md:col-span-2",children:s})]});const p=c.projectDescription[r],h=g=>{const v=l==="number"?Number(g.target.value):g.target.value;f({[r]:v})};return a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 items-start gap-4",children:[a.jsx(Ee,{htmlFor:r,className:"md:text-right pt-2",children:n}),a.jsx("div",{className:"col-span-1 md:col-span-2",children:l==="textarea"?a.jsx(hn,{id:r,value:p||"",onChange:h}):a.jsx(ke,{id:r,type:l,value:p||"",onChange:h})})]})},qn=({label:n,id:r})=>a.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center gap-2 cursor-pointer",children:[a.jsx("span",{children:n}),a.jsx(ii,{id:r})]}),dh=({label:n,id:r,value:s})=>a.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center gap-2 cursor-pointer",children:[a.jsx("span",{children:n}),a.jsx(ea,{value:s,id:r})]}),f2=()=>a.jsxs(qt,{className:"w-full bg-blue-50 dark:bg-blue-900/30",children:[a.jsx(Gt,{className:"border-b",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[a.jsx(om,{className:"h-6 w-6 text-primary"}),"وصف المشروع"]})}),a.jsx(Xt,{className:"pt-6",children:a.jsxs("div",{className:"space-y-6",children:[a.jsx(Ft,{label:"ملخص وصف خصائص المشروع",id:"projectSummary",type:"textarea"}),a.jsx(Ft,{label:"اسم المشروع",id:"projectName"}),a.jsx(Ft,{label:"موقع المشروع",id:"projectLocation"}),a.jsx(Ft,{label:"قيمة المنحة المطلوبة",id:"grantAmount",children:a.jsx(ke,{id:"grantAmount",type:"number"})}),a.jsx(Ft,{label:"التمويل الذاتي أو مصادر التمويل الأخرى المتوفرة",id:"selfFunding",children:a.jsx(ke,{id:"selfFunding"})}),a.jsx(Ft,{label:"تكلفة المشروع الكلية",id:"totalCost",children:a.jsx(ke,{id:"totalCost",type:"number"})}),a.jsx(Ft,{label:"تاريخ تقديم خطة المشروع",id:"submissionDate",children:a.jsx(ke,{id:"submissionDate",type:"date"})}),a.jsx(Ft,{label:"وصف أهمية الفكرة ونوع المشروع",id:"ideaDescription",children:a.jsx(hn,{id:"ideaDescription"})}),a.jsx(Ft,{label:"وصف المهارات اللازم امتلاكها لتنفيذ المشروع",id:"skillsDescription",children:a.jsx(hn,{id:"skillsDescription"})}),a.jsx(Ft,{label:"وصف حاجة المجتمع للمشروع",id:"communityNeed",children:a.jsx(hn,{id:"communityNeed"})}),a.jsx(Ft,{label:"هل يحتاج المشروع إلى ترخيص؟",id:"licenseRequired",children:a.jsxs(Dr,{defaultValue:"no",className:"flex items-center gap-x-6",children:[a.jsx(dh,{label:"نعم",id:"licenseYes",value:"yes"}),a.jsx(dh,{label:"لا",id:"licenseNo",value:"no"})]})}),a.jsx(Ft,{label:"إذا كان نعم، أذكر جهة الترخيص",id:"licensingAuthority",children:a.jsx(ke,{id:"licensingAuthority"})}),a.jsx(Ft,{label:"الفئة المستهدفة بالمشروع",id:"targetAudience",children:a.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-4",children:[a.jsx(qn,{label:"أطفال",id:"targetChildren"}),a.jsx(qn,{label:"شباب",id:"targetYouth"}),a.jsx(qn,{label:"نساء",id:"targetWomen"}),a.jsx(qn,{label:"رجال",id:"targetMen"}),a.jsx(qn,{label:"كبار سن",id:"targetSeniors"}),a.jsx(qn,{label:"مؤسسات/شركات",id:"targetCompanies"}),a.jsx(qn,{label:"جمعيات",id:"targetAssociations"}),a.jsx(qn,{label:"مدارس",id:"targetSchools"}),a.jsx(qn,{label:"فنادق",id:"targetHotels"}),a.jsxs("div",{className:"flex items-center col-span-2 sm:col-span-3 gap-2",children:[a.jsxs(Ee,{htmlFor:"targetOther",className:"font-normal flex items-center gap-2 cursor-pointer",children:[a.jsx("span",{children:"أخرى:"}),a.jsx(ii,{id:"targetOther"})]}),a.jsx(ke,{id:"targetOtherText",className:"flex-1"})]})]})})]})})]}),p2=()=>a.jsxs("div",{className:"space-y-8",children:[a.jsx(Lj,{}),a.jsx(f2,{})]}),Qs=({label:n,id:r,value:s})=>a.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[a.jsx(ea,{value:s,id:r}),a.jsx("span",{children:n})]}),Tn=({label:n,id:r})=>a.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[a.jsx(ii,{id:r}),a.jsx("span",{children:n})]}),h2=()=>a.jsxs(qt,{className:"w-full bg-teal-50 dark:bg-teal-900/30",children:[a.jsx(Gt,{className:"border-b",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[a.jsx(ex,{className:"h-6 w-6 text-primary"}),"دراسة السوق والمنافسين"]})}),a.jsxs(Xt,{className:"pt-6 space-y-8",children:[a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"ماذا ستقدم في مشروعك؟"}),a.jsxs("div",{className:"pr-4 space-y-4",children:[a.jsxs("div",{children:[a.jsx(Ee,{htmlFor:"products",className:"font-normal text-muted-foreground",children:"◼️ منتجات (اذكرها):"}),a.jsx(hn,{id:"products",className:"mt-1"})]}),a.jsxs("div",{children:[a.jsx(Ee,{htmlFor:"services",className:"font-normal text-muted-foreground",children:"◼️ خدمات (اذكرها):"}),a.jsx(hn,{id:"services",className:"mt-1"})]})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"هل يوجد منافسين يبيعون نفس المنتج أو منتج شبيه في منطقة مشروعك؟"}),a.jsx("div",{className:"pr-4",children:a.jsxs(Dr,{defaultValue:"no",className:"flex items-center flex-wrap gap-x-6 gap-y-2",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(Qs,{label:"نعم – كم عددهم؟",id:"competitorsYes",value:"yes"}),a.jsx(ke,{id:"competitorsCount",type:"number",className:"w-24 h-8"})]}),a.jsx(Qs,{label:"لا",id:"competitorsNo",value:"no"})]})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{htmlFor:"competitorProducts",className:"text-base font-semibold",children:"ما هي المنتجات المنافسة أو الشبيهة التي يبيعها المنافسون؟"}),a.jsx("div",{className:"pr-4",children:a.jsx(hn,{id:"competitorProducts"})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"كيف يحاول المنافسون أن يحققوا الربح؟"}),a.jsxs("div",{className:"pr-4 space-y-2",children:[a.jsx(Tn,{label:"من خلال السعر المنخفض",id:"profitPrice"}),a.jsx(Tn,{label:"من خلال جودة المنتج",id:"profitQuality"}),a.jsx(Tn,{label:"من خلال الخدمة المميزة",id:"profitService"}),a.jsx(Tn,{label:"من خلال التكلفة المنخفضة",id:"profitCost"}),a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[a.jsx(Tn,{label:"أخرى (اذكرها):",id:"profitOther"}),a.jsx(ke,{id:"profitOtherText",className:"flex-1 h-8"})]})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"ما هي أسعار المنافسين مقارنة بالسعر الذي تنوي البيع به؟"}),a.jsx("div",{className:"pr-4",children:a.jsxs(Dr,{className:"space-y-2",children:[a.jsx(Qs,{label:"نفس سعر البيع الذي سأبيع به تقريبًا",id:"priceSame",value:"same"}),a.jsx(Qs,{label:"أسعار المنافسين أعلى من سعري",id:"priceHigher",value:"higher"}),a.jsx(Qs,{label:"أسعار المنافسين أقل من سعري",id:"priceLower",value:"lower"})]})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"كيف يبيع ويُروّج المنافسون منتجاتهم؟"}),a.jsxs("div",{className:"pr-4 space-y-2",children:[a.jsx(Tn,{label:"من خلال الإنترنت ووسائل التواصل الاجتماعي",id:"promoSocial"}),a.jsx(Tn,{label:"من خلال البيع المباشر للناس",id:"promoDirect"}),a.jsx(Tn,{label:"من خلال زيارة الزبائن والترويج للمنتج",id:"promoVisits"}),a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[a.jsx(Tn,{label:"الإعلان – وسيلة الإعلان:",id:"promoAd"}),a.jsx(ke,{id:"promoAdText",className:"flex-1 h-8"})]}),a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[a.jsx(Tn,{label:"أخرى (اذكرها):",id:"promoOther"}),a.jsx(ke,{id:"promoOtherText",className:"flex-1 h-8"})]})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"قم بمراقبة اثنين من المنافسين وقدر عدد الزبائن الذين يترددون عليهم في اليوم الواحد:"}),a.jsxs("div",{className:"pr-4 space-y-3",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(Ee,{className:"font-normal w-48",children:"◼️ عدد الزبائن عند المنافس 1:"}),a.jsx(ke,{type:"number",className:"w-24 h-8"})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(Ee,{className:"font-normal w-48",children:"◼️ عدد الزبائن عند المنافس 2:"}),a.jsx(ke,{type:"number",className:"w-24 h-8"})]})]})]})]})]}),un=({label:n,id:r,value:s})=>a.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[a.jsx(ea,{value:s,id:r}),a.jsx("span",{children:n})]}),Gn=({label:n,id:r})=>a.jsxs(Ee,{htmlFor:r,className:"font-normal flex items-center justify-start gap-2 cursor-pointer",children:[a.jsx(ii,{id:r}),a.jsx("span",{children:n})]}),m2=()=>a.jsxs(qt,{className:"w-full bg-emerald-50 dark:bg-emerald-900/30",children:[a.jsx(Gt,{className:"border-b",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[a.jsx(Jy,{className:"h-6 w-6 text-primary"}),"دراسة السوق والمنافسين – الجزء الثاني"]})}),a.jsxs(Xt,{className:"pt-6 space-y-8",children:[a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{htmlFor:"potentialCustomers",className:"text-base font-semibold",children:"كم أعداد الزبائن المحتمل أن يشتروا منتجاتك (العدد الكلي للزبائن المحتملين)"}),a.jsx("div",{className:"pr-4",children:a.jsx(ke,{type:"number",id:"potentialCustomers"})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{htmlFor:"consumptionRate",className:"text-base font-semibold",children:"كم معدل استهلاكهم للمنتج في الشهر"}),a.jsx("div",{className:"pr-4",children:a.jsx(ke,{type:"number",id:"consumptionRate"})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"هل المنتجات الشبيهة تلبي احتياج السوق أم أن هناك طلبًا كبيرًا على المنتجات والمعروض أقل:"}),a.jsx("div",{className:"pr-4",children:a.jsxs(Dr,{className:"space-y-2",children:[a.jsx(un,{label:"هناك طلب كبير على المنتجات والمعروض أقل",id:"demandHigh",value:"high"}),a.jsx(un,{label:"المعروض من المنتجات الشبيهة أكبر من الطلب",id:"supplyHigh",value:"supply_high"}),a.jsx(un,{label:"المعروض في السوق هو نفس حاجة السوق (العرض = الطلب)",id:"demandEqual",value:"equal"})]})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{htmlFor:"peakSeasons",className:"text-base font-semibold",children:"ما هي المواسم التي يشتد فيها البيع لمنتجاتك أو خدماتك (مثل: الأعياد، رمضان، الصيف...)"}),a.jsx("div",{className:"pr-4",children:a.jsx(hn,{id:"peakSeasons"})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"بماذا سيتميز (يتفوق) منتجك عن المنتجات الشبيهة في السوق؟"}),a.jsxs("div",{className:"pr-4 space-y-2",children:[a.jsx(Gn,{label:"السعر المنخفض",id:"advantagePrice"}),a.jsx(Gn,{label:"الجودة العالية",id:"advantageQuality"}),a.jsx(Gn,{label:"الخدمة المميزة",id:"advantageService"}),a.jsx(Gn,{label:"التكلفة المنخفضة",id:"advantageCost"}),a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[a.jsx(Gn,{label:"أخرى (اذكرها):",id:"advantageOther"}),a.jsx(ke,{id:"advantageOtherText",className:"flex-1 h-8"})]})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"كيف ستقوم ببيع وتسويق منتجك؟"}),a.jsxs("div",{className:"pr-4 space-y-2",children:[a.jsx(Gn,{label:"من خلال الإنترنت ووسائل التواصل الاجتماعي",id:"marketingSocial"}),a.jsx(Gn,{label:"من خلال البيع المباشر للناس",id:"marketingDirect"}),a.jsx(Gn,{label:"من خلال زيارة الزبائن والترويج للمنتج",id:"marketingVisits"}),a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-sm",children:[a.jsx(Gn,{label:"أخرى (اذكرها):",id:"marketingOther"}),a.jsx(ke,{id:"marketingOtherText",className:"flex-1 h-8"})]})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"هل سيحتاج مشروعك إلى موردين أو مزودين للمواد الخام (الأولية) أو الخدمات؟"}),a.jsx("div",{className:"pr-4",children:a.jsxs(Dr,{defaultValue:"no",className:"flex items-center gap-x-6",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(un,{label:"نعم – كم عددهم؟",id:"suppliersYes",value:"yes"}),a.jsx(ke,{id:"suppliersCount",type:"number",className:"w-24 h-8"})]}),a.jsx(un,{label:"لا",id:"suppliersNo",value:"no"})]})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"هل المزودين أو الموردين سهل الوصول إليهم؟"}),a.jsx("div",{className:"pr-4",children:a.jsxs(Dr,{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-md",children:[a.jsx(un,{label:"نعم (كيف؟)",id:"suppliersAccessYes",value:"yes"}),a.jsx(ke,{id:"suppliersAccessYesText",className:"flex-1 h-8"})]}),a.jsxs("div",{className:"flex items-center gap-2 w-full max-w-md",children:[a.jsx(un,{label:"لا (لماذا؟)",id:"suppliersAccessNo",value:"no"}),a.jsx(ke,{id:"suppliersAccessNoText",className:"flex-1 h-8"})]})]})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsx(Ee,{className:"text-base font-semibold",children:"هل أسعار المزودين أو الموردين مناسبة؟"}),a.jsx("div",{className:"pr-4",children:a.jsxs(Dr,{className:"space-y-2",children:[a.jsx(un,{label:"مناسبة",id:"supplierPriceGood",value:"good"}),a.jsx(un,{label:"مرتفعة",id:"supplierPriceHigh",value:"high"}),a.jsx(un,{label:"منخفضة",id:"supplierPriceLow",value:"low"}),a.jsx(un,{label:"متذبذبة حسب السوق",id:"supplierPriceFluctuates",value:"fluctuates"})]})})]})]})]}),g2=()=>a.jsxs("div",{className:"space-y-8",children:[a.jsx(h2,{}),a.jsx(m2,{})]}),El=({title:n,description:r,id:s,icon:l,color:c})=>{const{projectData:f,updateSwotAnalysis:p}=co(),h=f.swotAnalysis[s],g=v=>{p({[s]:v.target.value})};return a.jsxs("div",{className:"space-y-4 p-6 rounded-2xl border-2 border-white/30 dark:border-slate-600/30 bg-gradient-card shadow-3d-hover transition-all duration-300 hover:scale-105 relative overflow-hidden group",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),a.jsxs("div",{className:"flex items-center gap-4 relative z-10",children:[a.jsxs("div",{className:Se("relative flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-2xl shadow-lg transition-transform duration-300 group-hover:scale-110",c),children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-2xl"}),a.jsx(l,{className:"h-7 w-7 text-white relative z-10"}),a.jsx("div",{className:Se("absolute inset-0 rounded-2xl opacity-50 animate-ping",c.replace("bg-","bg-"))})]}),a.jsxs("div",{className:"flex-1",children:[a.jsx(Ee,{htmlFor:s,className:"font-bold text-xl md:text-2xl text-gradient-primary block",children:n}),a.jsx("p",{className:"text-sm text-muted-foreground mt-1 leading-relaxed",children:r})]})]}),a.jsx(hn,{id:s,rows:6,className:"input-3d shadow-lg border-2 border-white/50 dark:border-slate-600/50 focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-300 hover:shadow-xl resize-none relative z-10",value:h||"",onChange:g,placeholder:`اكتب ${n.toLowerCase()} هنا...`}),a.jsx("div",{className:Se("absolute bottom-0 left-0 right-0 h-1 opacity-60",c)})]})},v2=()=>a.jsxs(qt,{className:"w-full card-3d shadow-3d-hover border-glow bg-gradient-to-br from-amber-50/90 via-orange-50/90 to-yellow-50/90 dark:from-amber-900/30 dark:via-orange-900/30 dark:to-yellow-900/30 backdrop-blur-sm relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-400/5 to-orange-400/5 animate-pulse"}),a.jsxs(Gt,{className:"border-b border-amber-200/50 dark:border-amber-700/50 relative z-10 bg-gradient-to-r from-white/50 to-amber-50/50 dark:from-slate-800/50 dark:to-amber-900/50",children:[a.jsxs(Yt,{className:"flex items-center gap-4 text-2xl md:text-3xl font-bold",children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full blur opacity-50"}),a.jsx("div",{className:"relative w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg",children:a.jsx(cx,{className:"h-6 w-6 text-white"})})]}),a.jsxs("div",{className:"flex flex-col",children:[a.jsx("span",{className:"text-gradient-primary",children:"التحليل الرباعي"}),a.jsx("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-300",children:"SWOT Analysis"})]})]}),a.jsx("div",{className:"mt-2 w-16 h-1 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full shadow-lg"})]}),a.jsx(Xt,{className:"pt-8 pb-6 relative z-10",children:a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[a.jsx(El,{title:"نقاط القوة",description:"(المهارات، القدرة المالية، الدعم العائلي، الخبرة السابقة...)",id:"strengths",icon:fx,color:"bg-gradient-to-r from-green-500 to-emerald-500"}),a.jsx(El,{title:"نقاط الضعف",description:"(عدم وجود المهارات، ضعف القدرة المالية، قلة الخبرة...)",id:"weaknesses",icon:dx,color:"bg-gradient-to-r from-red-500 to-rose-500"}),a.jsx(El,{title:"الفرص",description:"(عوامل اقتصادية، قانونية، اجتماعية إيجابية، اتجاهات السوق...)",id:"opportunities",icon:sm,color:"bg-gradient-to-r from-blue-500 to-cyan-500"}),a.jsx(El,{title:"التهديدات",description:"(عوامل خارجية قد تضر المشروع، منافسة شرسة، تغيرات قانونية...)",id:"threats",icon:hx,color:"bg-gradient-to-r from-yellow-500 to-amber-500"})]})}),a.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-500 to-orange-500 opacity-50"})]}),y2=()=>a.jsx(v2,{}),Ks=({title:n,questions:r,icon:s})=>a.jsxs("div",{className:"relative pl-8",children:[a.jsx("div",{className:"absolute top-1 right-0 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground",children:a.jsx(s,{className:"h-5 w-5"})}),a.jsxs("div",{className:"border-r-2 border-primary/20 pr-8 space-y-6 pb-8 last:border-r-0 last:pb-0",children:[a.jsx("h3",{className:"text-xl font-bold pt-1",children:n}),a.jsx("div",{className:"space-y-4",children:r.map((l,c)=>a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ee,{children:l}),a.jsx(hn,{})]},c))})]})]}),x2=()=>a.jsxs(qt,{className:"w-full bg-indigo-50 dark:bg-indigo-900/30",children:[a.jsx(Gt,{className:"border-b",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[a.jsx(ux,{className:"h-6 w-6 text-primary"}),"عناصر المزيج التسويقي – Marketing Mix (4Ps + 1)"]})}),a.jsxs(Xt,{className:"pt-6 space-y-2",children:[a.jsx(Ks,{title:"المنتج (Product)",icon:Yy,questions:["ما هي المنتجات التي ستقدمها؟ وهل فيها تنوع؟","هل جودتها عالية؟ وهل ستقدم خدمات مصاحبة؟","هل ستميزها بعلامة تجارية وتغليف مميز؟"]}),a.jsx(Ks,{title:"السعر (Price)",icon:ox,questions:["ما هي أسعار البيع؟ وهل هناك أسعار جملة وتجزئة؟","هل ستقدم تخفيضات أو خصومات؟ وهل ستسمح بالبيع الآجل؟"]}),a.jsx(Ks,{title:"المكان (Place)",icon:ix,questions:["ما هي قنوات البيع والتوزيع؟ وما مدى التغطية السوقية؟","كيف سيكون الموقع والديكور؟ وكيف ستدير المخزون؟"]}),a.jsx(Ks,{title:"الترويج (Promotion)",icon:lx,questions:["كيف ستروج للمشروع؟ (إعلان، بيع شخصي، عروض...)","هل ستستخدم التسويق الإلكتروني ووسائل التواصل الاجتماعي؟"]}),a.jsx(Ks,{title:"الناس (People)",icon:mx,questions:["من سيساعدك في المشروع؟ (موظفين، أفراد من العائلة، أصدقاء)"]})]})]}),w2=()=>a.jsx(x2,{}),es=w.forwardRef(({className:n,...r},s)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:s,className:Se("w-full caption-bottom text-sm",n),...r})}));es.displayName="Table";const ts=w.forwardRef(({className:n,...r},s)=>a.jsx("thead",{ref:s,className:Se("[&_tr]:border-b",n),...r}));ts.displayName="TableHeader";const ns=w.forwardRef(({className:n,...r},s)=>a.jsx("tbody",{ref:s,className:Se("[&_tr:last-child]:border-0",n),...r}));ns.displayName="TableBody";const rs=w.forwardRef(({className:n,...r},s)=>a.jsx("tfoot",{ref:s,className:Se("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",n),...r}));rs.displayName="TableFooter";const qe=w.forwardRef(({className:n,...r},s)=>a.jsx("tr",{ref:s,className:Se("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",n),...r}));qe.displayName="TableRow";const Ke=w.forwardRef(({className:n,...r},s)=>a.jsx("th",{ref:s,className:Se("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",n),...r}));Ke.displayName="TableHead";const me=w.forwardRef(({className:n,...r},s)=>a.jsx("td",{ref:s,className:Se("p-4 align-middle [&:has([role=checkbox])]:pr-0",n),...r}));me.displayName="TableCell";const b2=w.forwardRef(({className:n,...r},s)=>a.jsx("caption",{ref:s,className:Se("mt-4 text-sm text-muted-foreground",n),...r}));b2.displayName="TableCaption";const fh=({title:n,example:r})=>{const s=[{id:1,item:"",unitPrice:"",quantity:"",total:""},{id:2,item:"",unitPrice:"",quantity:"",total:""},{id:3,item:"",unitPrice:"",quantity:"",total:""}];return a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-lg font-semibold",children:n}),a.jsxs("p",{className:"text-sm text-muted-foreground",children:["مثال: ",r]}),a.jsx("div",{className:"rounded-md border",children:a.jsxs(es,{children:[a.jsx(ts,{children:a.jsxs(qe,{children:[a.jsx(Ke,{className:"w-[40%]",children:"البند"}),a.jsx(Ke,{children:"سعر الوحدة"}),a.jsx(Ke,{children:"عدد الوحدات"}),a.jsx(Ke,{className:"text-left",children:"المجموع"})]})}),a.jsx(ns,{children:s.map(l=>a.jsxs(qe,{children:[a.jsx(me,{children:a.jsx(ke,{placeholder:"اسم البند..."})}),a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0.00"})}),a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0"})}),a.jsx(me,{className:"text-left",children:a.jsx(ke,{readOnly:!0,placeholder:"0.00"})})]},l.id))}),a.jsx(rs,{children:a.jsxs(qe,{children:[a.jsx(me,{colSpan:3,className:"font-bold",children:"الإجمالي الكلي"}),a.jsx(me,{className:"text-left font-bold",children:"0.00"})]})})]})}),a.jsx(pn,{variant:"outline",size:"sm",children:"إضافة صف جديد"})]})},j2=()=>a.jsxs(qt,{className:"w-full bg-stone-50 dark:bg-stone-900/30",children:[a.jsx(Gt,{className:"border-b",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[a.jsx(gx,{className:"h-6 w-6 text-primary"}),"مستلزمات الإنتاج للمشروع"]})}),a.jsxs(Xt,{className:"pt-6 space-y-8",children:[a.jsx(fh,{title:"1. الأجهزة والآلات والمعدات والأثاث المطلوبة عند التأسيس",example:"ماكينة خياطة – عجانة – مولينكس – طاولات – كراسي – أرفف ..."}),a.jsx(fh,{title:"2. المواد الخام المطلوبة لمدة شهر",example:"طحين – قماش – صبغات – خيوط – أعلاف ..."})]})]}),k2=()=>a.jsx(j2,{});var N2="Separator",ph="horizontal",S2=["horizontal","vertical"],Wg=w.forwardRef((n,r)=>{const{decorative:s,orientation:l=ph,...c}=n,f=C2(l)?l:ph,h=s?{role:"none"}:{"aria-orientation":f==="vertical"?f:void 0,role:"separator"};return a.jsx($e.div,{"data-orientation":f,...h,...c,ref:r})});Wg.displayName=N2;function C2(n){return S2.includes(n)}var Qg=Wg;const Uo=w.forwardRef(({className:n,orientation:r="horizontal",decorative:s=!0,...l},c)=>a.jsx(Qg,{ref:c,decorative:s,orientation:r,className:Se("shrink-0 bg-border",r==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",n),...l}));Uo.displayName=Qg.displayName;const qs=({title:n})=>a.jsx("h3",{className:"text-xl font-bold text-primary mt-8 mb-4",children:n}),hh=({title:n,costs:r,onCostChange:s,totalLabel:l,showRawMaterials:c=!1})=>{const f=w.useMemo(()=>r.reduce((g,v)=>g+v.monthly,0),[r]),p=f*12,h=w.useMemo(()=>{var v;if(!c)return 0;const g=((v=r.find(x=>x.name==="المواد الخام"))==null?void 0:v.monthly)||0;return f-g},[r,f,c]);return a.jsxs("div",{className:"space-y-2",children:[a.jsx("h4",{className:"font-semibold",children:n}),a.jsx("div",{className:"rounded-md border",children:a.jsxs(es,{children:[a.jsx(ts,{children:a.jsxs(qe,{children:[a.jsx(Ke,{className:"w-[50%]",children:"البند"}),a.jsx(Ke,{children:"المبلغ الشهري"}),a.jsx(Ke,{className:"text-left",children:"المبلغ السنوي"})]})}),a.jsx(ns,{children:r.map((g,v)=>a.jsxs(qe,{children:[a.jsx(me,{children:g.name}),a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0.00",value:g.monthly||"",onChange:x=>s(v,x.target.value),className:"max-w-32"})}),a.jsx(me,{className:"text-left font-medium",children:(g.monthly*12).toFixed(2)})]},v))}),a.jsxs(rs,{children:[a.jsxs(qe,{className:"bg-muted/50",children:[a.jsx(me,{className:"font-bold",children:l}),a.jsx(me,{className:"font-bold",children:f.toFixed(2)}),a.jsx(me,{className:"text-left font-bold",children:p.toFixed(2)})]}),c&&a.jsxs(a.Fragment,{children:[a.jsxs(qe,{children:[a.jsx(me,{className:"font-bold",children:"🔸 مجموع التكاليف المتغيرة (بدون المواد الخام)"}),a.jsx(me,{className:"font-bold",children:h.toFixed(2)}),a.jsx(me,{className:"text-left font-bold",children:(h*12).toFixed(2)})]}),a.jsxs(qe,{className:"bg-muted/50",children:[a.jsx(me,{className:"font-bold",children:"🔹 مجموع التكاليف المتغيرة (مع المواد الخام)"}),a.jsx(me,{className:"font-bold",children:f.toFixed(2)}),a.jsx(me,{className:"text-left font-bold",children:p.toFixed(2)})]})]})]})]})})]})},mh=({title:n,items:r,totalLabel:s})=>a.jsxs("div",{className:"space-y-2",children:[a.jsx("h4",{className:"font-semibold",children:n}),a.jsx("div",{className:"rounded-md border",children:a.jsxs(es,{children:[a.jsx(ts,{children:a.jsxs(qe,{children:[a.jsx(Ke,{children:"البند"}),a.jsx(Ke,{className:"text-left w-48",children:"المبلغ بالدينار"})]})}),a.jsx(ns,{children:r.map(l=>a.jsxs(qe,{children:[a.jsx(me,{children:l}),a.jsx(me,{className:"text-left",children:a.jsx(ke,{type:"number",placeholder:"0.00"})})]},l))}),a.jsx(rs,{children:a.jsxs(qe,{className:"bg-muted/50",children:[a.jsx(me,{className:"font-bold",children:s}),a.jsx(me,{className:"text-left font-bold",children:"0.00"})]})})]})})]}),E2=()=>{const{projectData:n,updateFinancialStudy:r}=co(),{fixedCosts:s,variableCosts:l,profitRows:c,annualSales:f,breakEvenInputs:p}=n.financialStudy,h=(O,F)=>{const $=[...s];$[O]={...$[O],monthly:Number(F)||0},r({fixedCosts:$})},g=(O,F)=>{const $=[...l];$[O]={...$[O],monthly:Number(F)||0},r({variableCosts:$})},v=(O,F,$)=>{const G=[...c];F==="name"?G[O][F]=$:G[O][F]=Number($)||0,r({profitRows:G})},x=()=>{const O=[...c,{id:Date.now(),name:"",units:0,costPerUnit:0,pricePerUnit:0}];r({profitRows:O})},b=O=>{const F=c.filter($=>$.id!==O);r({profitRows:F})},S=(O,F,$,G)=>{const Z=JSON.parse(JSON.stringify(f));Z[O].monthlyData[F][$]=Number(G)||0,r({annualSales:Z})},y=(O,F)=>{const $={...p,[O]:Number(F)||0};r({breakEvenInputs:$})},E=w.useMemo(()=>s.reduce((O,F)=>O+F.monthly,0),[s]),k=w.useMemo(()=>l.reduce((O,F)=>O+F.monthly,0),[l]),j=E+k,_=w.useMemo(()=>c.reduce((O,F)=>{const $=F.units*F.costPerUnit,G=F.units*F.pricePerUnit;return O.totalCost+=$,O.totalRevenue+=G,O.totalProfit+=G-$,O},{totalCost:0,totalRevenue:0,totalProfit:0}),[c]),P=w.useMemo(()=>{const O=Array(12).fill(0);return f.forEach(F=>{F.monthlyData.forEach(($,G)=>{O[G]+=$.quantity*$.price})}),O},[f]),A=w.useMemo(()=>P.reduce((O,F)=>O+F,0),[P]),z=w.useMemo(()=>{const O=p.salePrice-p.variableCost;return O<=0?0:E/O},[E,p]);return a.jsxs(qt,{className:"w-full bg-purple-50 dark:bg-purple-900/30",children:[a.jsx(Gt,{className:"border-b",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text",children:[a.jsx(Xy,{className:"h-6 w-6 text-primary"}),"الدراسة المالية للمشروع"]})}),a.jsxs(Xt,{className:"pt-6",children:[a.jsx(qs,{title:"💰 أولًا: رأس المال العامل – تكاليف المشروع التشغيلية"}),a.jsxs("div",{className:"space-y-6",children:[a.jsx(hh,{title:"1. التكاليف الثابتة",costs:s,onCostChange:h,totalLabel:"🔹 مجموع التكاليف الثابتة"}),a.jsx(hh,{title:"2. التكاليف المتغيرة",costs:l,onCostChange:g,totalLabel:"",showRawMaterials:!0}),a.jsxs("div",{className:"p-4 bg-primary text-primary-foreground rounded-lg flex justify-between items-center",children:[a.jsx("span",{className:"font-bold",children:"✅ إجمالي رأس المال العامل (التكاليف التشغيلية)"}),a.jsxs("div",{className:"text-right",children:[a.jsxs("p",{className:"font-bold text-lg",children:[j.toFixed(2)," دينار / شهريًا"]}),a.jsxs("p",{className:"font-bold text-sm",children:[(j*12).toFixed(2)," دينار / سنويًا"]})]})]})]}),a.jsx(Uo,{className:"my-10"}),a.jsx(qs,{title:"🏗️ ثانيًا: النفقات التأسيسية – ما قبل التشغيل"}),a.jsx(mh,{title:"",items:["التسجيل والترخيص","توصيل الخدمات","خلو / تجهيز الموقع","تجهيز أولي","أخرى"],totalLabel:"مجموع نفقات ما قبل التشغيل"}),a.jsx(Uo,{className:"my-10"}),a.jsx(qs,{title:"💼 ثالثًا: إجمالي رأس مال المشروع المتوقع"}),a.jsx(mh,{title:"",items:["رأس المال الثابت (تكاليف المشروع الرأسمالية – المعدات، الأجهزة...)","رأس المال العامل (لأول شهر فقط)"],totalLabel:"🔷 إجمالي رأس مال المشروع المتوقع (مجموع النفقات والتكاليف)"}),a.jsx(Uo,{className:"my-10"}),a.jsx(qs,{title:"📊 رابعًا: حساب الربح والخسارة الشهري لكل منتج"}),a.jsx("div",{className:"rounded-md border",children:a.jsxs(es,{children:[a.jsx(ts,{children:a.jsxs(qe,{children:[a.jsx(Ke,{className:"w-[20%]",children:"المنتج / الخدمة"}),a.jsx(Ke,{children:"عدد الوحدات"}),a.jsx(Ke,{children:"تكلفة الوحدة"}),a.jsx(Ke,{children:"إجمالي التكلفة"}),a.jsx(Ke,{children:"سعر بيع الوحدة"}),a.jsx(Ke,{children:"إجمالي الإيراد"}),a.jsx(Ke,{children:"إجمالي الربح"}),a.jsx(Ke,{className:"w-[5%]"})]})}),a.jsx(ns,{children:c.map((O,F)=>{const $=O.units*O.costPerUnit,G=O.units*O.pricePerUnit,Z=G-$;return a.jsxs(qe,{children:[a.jsx(me,{children:a.jsx(ke,{placeholder:`منتج ${F+1}`,value:O.name,onChange:J=>v(F,"name",J.target.value)})}),a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0",value:O.units||"",onChange:J=>v(F,"units",J.target.value)})}),a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0.00",value:O.costPerUnit||"",onChange:J=>v(F,"costPerUnit",J.target.value)})}),a.jsx(me,{children:a.jsx(ke,{readOnly:!0,value:$.toFixed(2),className:"font-medium bg-muted"})}),a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0.00",value:O.pricePerUnit||"",onChange:J=>v(F,"pricePerUnit",J.target.value)})}),a.jsx(me,{children:a.jsx(ke,{readOnly:!0,value:G.toFixed(2),className:"font-medium bg-muted"})}),a.jsx(me,{children:a.jsx(ke,{readOnly:!0,value:Z.toFixed(2),className:Se("font-bold bg-muted",Z>0?"text-green-600":Z<0?"text-red-600":"")})}),a.jsx(me,{children:c.length>1&&a.jsx(pn,{variant:"ghost",size:"icon",onClick:()=>b(O.id),children:a.jsx(px,{className:"h-4 w-4 text-red-500"})})})]},O.id)})}),a.jsx(rs,{children:a.jsxs(qe,{className:"bg-primary/10 font-bold text-lg",children:[a.jsx(me,{colSpan:3,children:"الإجمالي الشهري"}),a.jsx(me,{children:_.totalCost.toFixed(2)}),a.jsx(me,{}),a.jsx(me,{children:_.totalRevenue.toFixed(2)}),a.jsx(me,{colSpan:2,className:Se(_.totalProfit>0?"text-green-700":_.totalProfit<0?"text-red-700":""),children:_.totalProfit.toFixed(2)})]})})]})}),a.jsx("div",{className:"mt-4 flex justify-start",children:a.jsxs(pn,{onClick:x,variant:"outline",children:[a.jsx(nx,{className:"ml-2 h-4 w-4"}),"إضافة منتج جديد"]})}),a.jsx(Uo,{className:"my-10"}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"text-xl font-bold text-white bg-red-800 p-2 rounded-md text-center",children:"🧾 تقدير الإيرادات / المبيعات لسنة"}),a.jsx("div",{className:"rounded-md border overflow-x-auto",children:a.jsxs(es,{className:"min-w-[1200px]",children:[a.jsxs(ts,{children:[a.jsxs(qe,{children:[a.jsx(Ke,{rowSpan:2,className:"w-[200px] sticky right-0 bg-muted align-bottom text-center",children:"المبيعات / الإيرادات"}),a.jsx(Ke,{colSpan:12,className:"text-center bg-muted/50",children:"أشهر السنة الأولى"}),a.jsx(Ke,{rowSpan:2,className:"w-[120px] sticky left-0 bg-muted align-bottom text-center",children:"المجموع السنوي"})]}),a.jsx(qe,{children:Array.from({length:12},(O,F)=>a.jsx(Ke,{className:"text-center bg-muted/50",children:F+1},F))})]}),a.jsx(ns,{children:f.map((O,F)=>{const $=O.monthlyData.reduce((Z,J)=>Z+J.quantity*J.price,0),G=O.monthlyData.reduce((Z,J)=>Z+J.quantity,0);return a.jsxs(w.Fragment,{children:[a.jsx(qe,{className:"bg-primary/5",children:a.jsx(me,{colSpan:14,className:"font-bold sticky right-0 bg-primary/5",children:O.name})}),a.jsxs(qe,{children:[a.jsx(me,{className:"font-medium sticky right-0 bg-background",children:"الكمية المتوقع بيعها"}),O.monthlyData.map((Z,J)=>a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0",className:"min-w-[65px] text-center",value:f[F].monthlyData[J].quantity||"",onChange:pe=>S(F,J,"quantity",pe.target.value)})},J)),a.jsx(me,{className:"text-center font-bold bg-muted sticky left-0",children:G})]}),a.jsxs(qe,{children:[a.jsx(me,{className:"font-medium sticky right-0 bg-background",children:"سعر البيع للوحدة"}),O.monthlyData.map((Z,J)=>a.jsx(me,{children:a.jsx(ke,{type:"number",placeholder:"0.00",className:"min-w-[65px] text-center",value:f[F].monthlyData[J].price||"",onChange:pe=>S(F,J,"price",pe.target.value)})},J)),a.jsx(me,{className:"text-center font-bold bg-muted sticky left-0",children:"-"})]}),a.jsxs(qe,{className:"bg-muted/20",children:[a.jsx(me,{className:"font-medium sticky right-0 bg-muted/20",children:"الإيراد (الكمية × السعر)"}),O.monthlyData.map((Z,J)=>a.jsx(me,{className:"text-center font-semibold",children:(Z.quantity*Z.price).toFixed(2)},J)),a.jsx(me,{className:"text-center font-bold bg-muted/50 sticky left-0",children:$.toFixed(2)})]})]},O.id)})}),a.jsx(rs,{children:a.jsxs(qe,{className:"bg-primary text-primary-foreground",children:[a.jsx(me,{className:"font-bold text-lg sticky right-0 bg-primary",children:"مجموع الإيرادات الشهرية"}),P.map((O,F)=>a.jsx(me,{className:"text-center font-bold",children:O.toFixed(2)},F)),a.jsx(me,{className:"text-center font-bold text-lg sticky left-0 bg-primary",children:A.toFixed(2)})]})})]})})]}),a.jsx(Uo,{className:"my-10"}),a.jsx(qs,{title:"📍 خامسًا: حساب نقطة التعادل (Break-even Point)"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"هي عدد الوحدات التي يجب بيعها لتغطية جميع التكاليف (الثابتة + المتغيرة)، بحيث لا يوجد ربح ولا خسارة."}),a.jsx("p",{className:"text-sm text-muted-foreground font-mono p-2 bg-muted rounded-md",children:"نقطة التعادل (بالوحدات) = التكاليف الثابتة الشهرية ÷ (سعر بيع الوحدة – تكلفة الوحدة المتغيرة)"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ee,{children:"التكاليف الثابتة الشهرية"}),a.jsx(ke,{type:"number",value:E.toFixed(2),readOnly:!0,disabled:!0})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ee,{children:"متوسط سعر بيع الوحدة"}),a.jsx(ke,{type:"number",placeholder:"0.00",value:p.salePrice||"",onChange:O=>y("salePrice",O.target.value)})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ee,{children:"متوسط تكلفة الوحدة المتغيرة"}),a.jsx(ke,{type:"number",placeholder:"0.00",value:p.variableCost||"",onChange:O=>y("variableCost",O.target.value)})]}),a.jsxs("div",{className:"p-4 bg-primary text-primary-foreground rounded-lg flex justify-between items-center md:col-span-2",children:[a.jsx("span",{className:"font-bold",children:"🔹 نقطة التعادل (عدد الوحدات)"}),a.jsxs("span",{className:"font-bold text-lg",children:[z.toFixed(2)," وحدة"]})]})]})]})]})},P2=()=>a.jsx(E2,{}),T2=()=>{const{projectData:n,saveProject:r,resetProject:s}=co(),l=()=>{r(),Je({title:"تم الحفظ بنجاح!",description:"تم حفظ جميع بيانات المشروع في التخزين المحلي"})},c=()=>{Je({title:"قريباً",description:"ميزة تصدير PDF ستكون متاحة قريباً",variant:"destructive"})},f=()=>{Je({title:"قريباً",description:"ميزة تصدير Excel ستكون متاحة قريباً",variant:"destructive"})},p=()=>{confirm("هل أنت متأكد من أنك تريد مسح جميع البيانات؟")&&s()},h=g=>new Intl.DateTimeFormat("ar-SA",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(g);return a.jsxs("div",{className:"space-y-8",children:[a.jsxs("div",{className:"text-center p-8 space-y-8 relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/10 via-blue-400/10 to-purple-400/10 rounded-3xl animate-pulse"}),a.jsx("div",{className:"flex justify-center relative z-10",children:a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-xl opacity-50 animate-pulse"}),a.jsx("div",{className:"relative w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-3d animate-glow",children:a.jsx(bc,{className:"h-12 w-12 text-white"})}),a.jsx("div",{className:"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-ping"}),a.jsx("div",{className:"absolute -bottom-2 -left-2 w-3 h-3 bg-blue-400 rounded-full animate-ping",style:{animationDelay:"0.5s"}}),a.jsx("div",{className:"absolute top-2 -left-4 w-2 h-2 bg-purple-400 rounded-full animate-ping",style:{animationDelay:"1s"}})]})}),a.jsxs("div",{className:"space-y-4 relative z-10",children:[a.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gradient-primary animate-glow",children:"🎉 تهانينا! 🎉"}),a.jsx("h3",{className:"text-2xl md:text-3xl font-bold text-green-600 dark:text-green-400",children:"لقد أكملت خطة المشروع بنجاح!"}),a.jsx("p",{className:"text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:"عمل رائع! لقد قمت بإنشاء خطة عمل شاملة ومتكاملة لمشروعك. يمكنك الآن حفظ خطة عملك أو تصديرها كملف PDF أو Excel للمشاركة والعرض."})]}),a.jsx("div",{className:"flex justify-center relative z-10",children:a.jsx("div",{className:"w-32 h-1 bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 rounded-full shadow-lg animate-pulse"})})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[a.jsxs(qt,{className:"card-3d shadow-3d-hover border-glow bg-gradient-to-br from-blue-50/90 to-indigo-50/90 dark:from-blue-900/30 dark:to-indigo-900/30 backdrop-blur-sm relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400/5 to-indigo-400/5"}),a.jsx(Gt,{className:"relative z-10 border-b border-blue-200/50 dark:border-blue-700/50",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-xl font-bold",children:[a.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg",children:a.jsx(im,{className:"h-5 w-5 text-white"})}),a.jsx("span",{className:"text-gradient-primary",children:"ملخص المشروع"})]})}),a.jsxs(Xt,{className:"space-y-4 pt-6 relative z-10",children:[a.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30",children:[a.jsx("strong",{className:"text-blue-700 dark:text-blue-300",children:"اسم المشروع:"}),a.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:n.projectDescription.projectName||"غير محدد"})]}),a.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30",children:[a.jsx("strong",{className:"text-blue-700 dark:text-blue-300",children:"صاحب المشروع:"}),a.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:n.personalInfo.ownerName||"غير محدد"})]}),a.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-blue-200/30 dark:border-blue-700/30",children:[a.jsx("strong",{className:"text-blue-700 dark:text-blue-300",children:"موقع المشروع:"}),a.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:n.projectDescription.projectLocation||"غير محدد"})]})]}),a.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 opacity-50"})]}),a.jsxs(qt,{className:"card-3d shadow-3d-hover border-glow bg-gradient-to-br from-green-50/90 to-emerald-50/90 dark:from-green-900/30 dark:to-emerald-900/30 backdrop-blur-sm relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-emerald-400/5"}),a.jsx(Gt,{className:"relative z-10 border-b border-green-200/50 dark:border-green-700/50",children:a.jsxs(Yt,{className:"flex items-center gap-3 text-xl font-bold",children:[a.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg",children:a.jsx(Zy,{className:"h-5 w-5 text-white"})}),a.jsx("span",{className:"text-gradient-primary",children:"معلومات الحفظ"})]})}),a.jsxs(Xt,{className:"space-y-4 pt-6 relative z-10",children:[a.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-green-200/30 dark:border-green-700/30",children:[a.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"آخر تحديث:"}),a.jsx("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:h(n.lastUpdated)})]}),a.jsxs("div",{className:"p-3 rounded-lg bg-white/50 dark:bg-slate-800/50 border border-green-200/30 dark:border-green-700/30",children:[a.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"الخطوة الحالية:"}),a.jsxs("span",{className:"mr-2 text-gray-700 dark:text-gray-300",children:[n.currentStep+1," من 7"]})]}),a.jsxs("div",{className:"p-3 rounded-lg bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 border border-green-200 dark:border-green-700/50",children:[a.jsx("strong",{className:"text-green-700 dark:text-green-300",children:"حالة الإكمال:"}),a.jsx("span",{className:"mr-2 text-green-600 dark:text-green-400 font-bold",children:"✅ مكتمل 100%"})]})]}),a.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500 opacity-50"})]})]}),a.jsxs("div",{className:"flex flex-wrap justify-center gap-6 pt-8",children:[a.jsxs(pn,{size:"lg",variant:"outline",onClick:l,className:"btn-3d shadow-3d border-2 border-blue-200 dark:border-blue-700 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-blue-50 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300",children:[a.jsx(sx,{className:"ml-2 h-5 w-5"}),"حفظ التقدم"]}),a.jsxs(pn,{size:"lg",onClick:c,className:"btn-3d shadow-3d bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white border-0 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),a.jsxs("span",{className:"relative flex items-center",children:[a.jsx(om,{className:"ml-2 h-5 w-5"}),"تصدير PDF"]})]}),a.jsxs(pn,{size:"lg",onClick:f,className:"btn-3d shadow-3d bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),a.jsxs("span",{className:"relative flex items-center",children:[a.jsx(ax,{className:"ml-2 h-5 w-5"}),"تصدير Excel"]})]}),a.jsxs(pn,{size:"lg",variant:"destructive",onClick:p,className:"btn-3d shadow-3d bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white border-0 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),a.jsx("span",{className:"relative flex items-center",children:"🗑️ مسح البيانات"})]})]})]})},no=[{id:1,title:"المعلومات الشخصية ووصف المشروع",component:a.jsx(p2,{})},{id:2,title:"دراسة السوق والمنافسين",component:a.jsx(g2,{})},{id:3,title:"التحليل الرباعي (SWOT)",component:a.jsx(y2,{})},{id:4,title:"المزيج التسويقي (4Ps + 1)",component:a.jsx(w2,{})},{id:5,title:"مستلزمات الإنتاج",component:a.jsx(k2,{})},{id:6,title:"الدراسة المالية",component:a.jsx(P2,{})},{id:7,title:"حفظ وتصدير",component:a.jsx(T2,{})}],R2=()=>{const{projectData:n,setCurrentStep:r,isDataValid:s,saveProject:l}=co(),c=n.currentStep,f=()=>{if(!s(c)){Je({title:"بيانات غير مكتملة",description:"يرجى إكمال البيانات المطلوبة قبل الانتقال للخطوة التالية",variant:"destructive"});return}c<no.length-1&&(r(c+1),Je({title:"تم الحفظ",description:"تم حفظ بيانات الخطوة الحالية تلقائياً"}))},p=()=>{c>0&&r(c-1)},h=()=>{if(!s(c)){Je({title:"بيانات غير مكتملة",description:"يرجى إكمال البيانات المطلوبة قبل الإنهاء",variant:"destructive"});return}l(),Je({title:"تم إنهاء المشروع بنجاح!",description:"تم حفظ جميع بيانات خطة العمل"})},g=no[c].component;return a.jsxs("div",{className:"w-full max-w-6xl mx-auto space-y-8 relative",children:[a.jsx("div",{className:"absolute -inset-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-2xl opacity-30"}),a.jsx("div",{className:"relative",children:a.jsx(Dj,{currentStep:c,totalSteps:no.length})}),a.jsxs(qt,{className:"card-3d shadow-3d-hover border-glow relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-card"}),a.jsxs(Gt,{className:"relative z-10 border-b border-white/20 dark:border-slate-700/50",children:[a.jsxs(Yt,{className:"text-gradient-primary text-2xl md:text-3xl font-bold flex items-center gap-3",children:[a.jsx("div",{className:"w-2 h-8 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full shadow-lg"}),no[c].title]}),a.jsxs(fg,{className:"text-lg text-gray-600 dark:text-gray-300 mt-2",children:["الخطوة ",c+1," من ",no.length," • ",Math.round((c+1)/no.length*100),"% مكتمل"]})]}),a.jsx(Xt,{className:"relative z-10 p-6 md:p-8",children:a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute -inset-2 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl"}),a.jsx("div",{className:"relative",children:g})]})}),a.jsxs(pg,{className:"relative z-10 flex justify-between items-center p-6 md:p-8 border-t border-white/20 dark:border-slate-700/50 bg-gradient-to-r from-white/50 to-gray-50/50 dark:from-slate-800/50 dark:to-slate-700/50",children:[a.jsxs(pn,{onClick:p,disabled:c===0,variant:"outline",className:"btn-3d shadow-3d border-2 border-white/30 dark:border-slate-600/30 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(Gy,{className:"ml-2 h-4 w-4"}),"السابق"]}),a.jsx("div",{className:"flex items-center gap-2",children:!s(c)&&a.jsxs("div",{className:"flex items-center gap-2 px-3 py-2 rounded-full bg-amber-100 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700/50 shadow-lg backdrop-blur-sm",children:[a.jsx(rm,{className:"h-4 w-4 text-amber-600 dark:text-amber-400 animate-pulse"}),a.jsx("span",{className:"text-sm font-medium text-amber-700 dark:text-amber-300",children:"بيانات غير مكتملة"})]})}),c<no.length-1?a.jsxs(pn,{onClick:f,className:"btn-3d shadow-3d bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),a.jsxs("span",{className:"relative flex items-center",children:["التالي",a.jsx(qy,{className:"mr-2 h-4 w-4"})]})]}):a.jsxs(pn,{onClick:h,className:"btn-3d shadow-3d bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 relative overflow-hidden animate-glow",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"}),a.jsxs("span",{className:"relative flex items-center",children:["إنهاء وحفظ",a.jsx(bc,{className:"mr-2 h-4 w-4"})]})]})]})]})]})},_2=()=>a.jsx("div",{className:"p-4 text-center",children:a.jsx("a",{href:"https://www.dyad.sh/",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:"Made with Dyad"})}),A2=()=>a.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 mb-8",children:[a.jsxs("div",{className:"relative group",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300 animate-pulse"}),a.jsxs("div",{className:"relative w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 p-5 rounded-full shadow-3d group-hover:scale-110 transition-transform duration-300",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-full"}),a.jsx(sm,{className:"h-10 w-10 text-white relative z-10 group-hover:animate-pulse"}),a.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"}),a.jsx("div",{className:"absolute -bottom-1 -left-1 w-2 h-2 bg-blue-300 rounded-full animate-ping",style:{animationDelay:"0.5s"}})]})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("span",{className:"text-3xl md:text-4xl font-bold text-gradient-primary tracking-tight block",children:"خطة مشروعي"}),a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1 block",children:"Business Plan Builder"})]})]}),O2=()=>a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-animated opacity-5"}),a.jsx("div",{className:"absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"}),a.jsx("div",{className:"absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float",style:{animationDelay:"2s"}}),a.jsx("div",{className:"absolute -bottom-32 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float",style:{animationDelay:"4s"}}),a.jsxs("div",{className:"container mx-auto py-10 px-4 relative z-10",children:[a.jsxs("header",{className:"text-center mb-12",children:[a.jsxs("div",{className:"w-full max-w-4xl mx-auto mb-8 relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-20"}),a.jsx("img",{src:"https://images.unsplash.com/photo-1556740738-b6a63e27c4df?q=80&w=2070&auto=format&fit=crop",alt:"Business Planning Session",className:"relative rounded-2xl shadow-3d object-cover w-full h-64 border-2 border-white/20 backdrop-blur-sm"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"})]}),a.jsx("div",{className:"mb-8 animate-float",children:a.jsx(A2,{})}),a.jsx("h1",{className:"text-5xl md:text-6xl font-extrabold tracking-tight mb-6 text-gradient-primary animate-glow",children:"حوّل فكرتك إلى مشروع ناجح"}),a.jsx("p",{className:"text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"أداة متكاملة لمساعدتك على بناء خطة عمل احترافية خطوة بخطوة مع تقنيات حديثة وتصميم أنيق."}),a.jsx("div",{className:"mt-8 flex justify-center",children:a.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg"})})]}),a.jsxs("main",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-3xl"}),a.jsx(R2,{})]}),a.jsx("footer",{className:"mt-16 text-center relative",children:a.jsx("div",{className:"inline-block p-4 rounded-2xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-3d border border-white/20",children:a.jsx(_2,{})})})]})]}),M2=()=>{const n=ag();return w.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",n.pathname)},[n.pathname]),a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),a.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),a.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},D2=new Bb,I2=()=>a.jsx(Hb,{client:D2,children:a.jsx(Oj,{children:a.jsxs(yb,{children:[a.jsx(Jx,{}),a.jsx(_w,{}),a.jsx(_j,{children:a.jsxs(Pj,{children:[a.jsx(dc,{path:"/",element:a.jsx(O2,{})}),a.jsx(dc,{path:"*",element:a.jsx(M2,{})})]})})]})})});qv.createRoot(document.getElementById("root")).render(a.jsx(I2,{}));

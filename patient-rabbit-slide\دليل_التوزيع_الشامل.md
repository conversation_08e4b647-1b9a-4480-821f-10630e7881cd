# دليل التوزيع الشامل لتطبيق خطة مشروعي 📦

## 🎯 الهدف
تحويل تطبيق خطة مشروعي إلى برنامج قابل للتثبيت والتوزيع على أجهزة Windows.

## 📋 الطرق المتاحة للتوزيع

### 1. النسخة المحمولة (Portable) ⭐ الأسهل
```bash
# تشغيل السكريبت
.\create-portable.bat
```

**المميزات:**
- ✅ لا يحتاج تثبيت معقد
- ✅ يمكن نسخه على فلاشة USB
- ✅ يعمل مباشرة بعد النسخ
- ✅ لا يحتاج صلاحيات إدارية

**كيفية الاستخدام:**
1. شغل `create-portable.bat`
2. انسخ مجلد "خطة-مشروعي-محمول" بالكامل
3. في أي جهاز، انقر على "تشغيل التطبيق.bat"

### 2. مثبت NSIS (إذا كان متوفراً)
```bash
# تشغيل السكريبت الشامل
.\create-installer.bat
```

**المتطلبات:**
- تثبيت NSIS من: https://nsis.sourceforge.io/Download

**المميزات:**
- ✅ مثبت احترافي مع واجهة عربية
- ✅ إنشاء اختصارات تلقائياً
- ✅ إضافة/إزالة البرامج في Windows
- ✅ معالج إلغاء تثبيت

### 3. استخدام Electron Builder (للمطورين)
```bash
# بناء مثبت متقدم
npm run electron:build
```

**ملاحظة:** قد يحتاج ذاكرة كبيرة وقد يفشل على بعض الأجهزة.

## 🛠️ الملفات المطلوبة للتوزيع

### الملفات الأساسية:
- `dist-electron/` - ملفات Electron المبنية
- `resources/` - الأيقونات والموارد
- `package.json` - معلومات التطبيق
- `LICENSE.txt` - ترخيص التطبيق

### ملفات التثبيت:
- `create-portable.bat` - إنشاء نسخة محمولة
- `create-installer.bat` - إنشاء مثبت شامل
- `installer-script.nsi` - سكريبت NSIS
- `build-simple.bat` - بناء مبسط

## 📦 خطوات التوزيع الموصى بها

### للمستخدم العادي:
1. **إنشاء النسخة المحمولة:**
   ```bash
   .\create-portable.bat
   ```

2. **ضغط المجلد:**
   - انقر بالزر الأيمن على مجلد "خطة-مشروعي-محمول"
   - اختر "إرسال إلى" → "مجلد مضغوط"

3. **التوزيع:**
   - رفع الملف المضغوط على Google Drive/OneDrive
   - إرسال الرابط للمستخدمين
   - أو نسخه على فلاشة USB

### للمطور المتقدم:
1. **تثبيت NSIS**
2. **إنشاء المثبت:**
   ```bash
   .\create-installer.bat
   ```
3. **توزيع ملف .exe**

## 🔧 استكشاف الأخطاء

### مشكلة: "Node.js غير مثبت"
**الحل:** تحميل وتثبيت Node.js من nodejs.org

### مشكلة: "فشل في بناء ملفات Electron"
**الحل:**
```bash
# تنظيف وإعادة بناء
rmdir /s /q dist-electron
npx tsc -p electron/tsconfig.json
```

### مشكلة: "NSIS غير موجود"
**الحل:** تحميل NSIS من الموقع الرسمي أو استخدام النسخة المحمولة

### مشكلة: "نفاد الذاكرة أثناء البناء"
**الحل:** استخدام النسخة المحمولة بدلاً من electron-builder

## 📋 قائمة التحقق قبل التوزيع

- [ ] تم اختبار التطبيق محلياً
- [ ] تم إنشاء النسخة المحمولة بنجاح
- [ ] تم اختبار النسخة المحمولة على جهاز آخر
- [ ] تم إنشاء ملف README واضح
- [ ] تم تحديث معلومات المطور (saif aldulaimi)
- [ ] تم اختبار عملية التثبيت/إلغاء التثبيت

## 🎯 التوصيات

### للتوزيع السريع:
استخدم **النسخة المحمولة** - الأسهل والأسرع

### للتوزيع الاحترافي:
استخدم **مثبت NSIS** - أكثر احترافية

### للتطوير المستمر:
استخدم **Electron Builder** - مع حل مشاكل الذاكرة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع قسم استكشاف الأخطاء
2. تأكد من تثبيت Node.js
3. جرب النسخة المحمولة كبديل

---
**تم إعداد هذا الدليل بواسطة: saif aldulaimi** 🚀

**التطبيق جاهز للتوزيع!** 🎉

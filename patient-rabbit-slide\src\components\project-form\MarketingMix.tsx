import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Box, DollarSign, MapPin, Megaphone, Users, ShoppingBag } from "lucide-react";
import React from "react";

const MixSection = ({ title, questions, icon: Icon }: { title: string; questions: string[], icon: React.ElementType }) => (
    <div className="relative pl-8">
        <div className="absolute top-1 right-0 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            <Icon className="h-5 w-5" />
        </div>
        <div className="border-r-2 border-primary/20 pr-8 space-y-6 pb-8 last:border-r-0 last:pb-0">
            <h3 className="text-xl font-bold pt-1">{title}</h3>
            <div className="space-y-4">
                {questions.map((q, i) => (
                    <div key={i} className="space-y-2">
                        <Label>{q}</Label>
                        <Textarea />
                    </div>
                ))}
            </div>
        </div>
    </div>
);

export const MarketingMix = () => {
  return (
    <Card className="w-full bg-indigo-50 dark:bg-indigo-900/30">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center gap-3 text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-sky-400 text-transparent bg-clip-text">
            <ShoppingBag className="h-6 w-6 text-primary" />
            عناصر المزيج التسويقي – Marketing Mix (4Ps + 1)
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6 space-y-2">
        <MixSection 
            title="المنتج (Product)"
            icon={Box}
            questions={[
                "ما هي المنتجات التي ستقدمها؟ وهل فيها تنوع؟",
                "هل جودتها عالية؟ وهل ستقدم خدمات مصاحبة؟",
                "هل ستميزها بعلامة تجارية وتغليف مميز؟"
            ]}
        />
        <MixSection 
            title="السعر (Price)"
            icon={DollarSign}
            questions={[
                "ما هي أسعار البيع؟ وهل هناك أسعار جملة وتجزئة؟",
                "هل ستقدم تخفيضات أو خصومات؟ وهل ستسمح بالبيع الآجل؟"
            ]}
        />
        <MixSection 
            title="المكان (Place)"
            icon={MapPin}
            questions={[
                "ما هي قنوات البيع والتوزيع؟ وما مدى التغطية السوقية؟",
                "كيف سيكون الموقع والديكور؟ وكيف ستدير المخزون؟"
            ]}
        />
        <MixSection 
            title="الترويج (Promotion)"
            icon={Megaphone}
            questions={[
                "كيف ستروج للمشروع؟ (إعلان، بيع شخصي، عروض...)",
                "هل ستستخدم التسويق الإلكتروني ووسائل التواصل الاجتماعي؟"
            ]}
        />
        <MixSection 
            title="الناس (People)"
            icon={Users}
            questions={[
                "من سيساعدك في المشروع؟ (موظفين، أفراد من العائلة، أصدقاء)"
            ]}
        />
      </CardContent>
    </Card>
  );
};
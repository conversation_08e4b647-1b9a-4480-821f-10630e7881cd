import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { ThumbsUp, ThumbsDown, Lightbulb, Alert<PERSON>riangle, Target } from "lucide-react";
import { useProject } from "@/contexts/ProjectContext";
import React from "react";

const SwotField = ({
    title,
    description,
    id,
    icon: Icon,
    color
}: {
    title: string;
    description: string;
    id: keyof import("@/types/project").SwotAnalysis;
    icon: React.ElementType;
    color: string;
}) => {
    const { projectData, updateSwotAnalysis } = useProject();
    const value = projectData.swotAnalysis[id];

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        updateSwotAnalysis({ [id]: e.target.value });
    };

    return (
        <div className="space-y-4 p-6 rounded-2xl border-2 border-white/30 dark:border-slate-600/30 bg-gradient-card shadow-3d-hover transition-all duration-300 hover:scale-105 relative overflow-hidden group">
            {/* تأثير الخلفية المتحركة */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div className="flex items-center gap-4 relative z-10">
                <div className={cn("relative flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-2xl shadow-lg transition-transform duration-300 group-hover:scale-110", color)}>
                    {/* تأثير الإضاءة الداخلية */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-2xl"></div>
                    <Icon className="h-7 w-7 text-white relative z-10" />

                    {/* تأثير النبضة */}
                    <div className={cn("absolute inset-0 rounded-2xl opacity-50 animate-ping", color.replace('bg-', 'bg-'))}></div>
                </div>
                <div className="flex-1">
                    <Label htmlFor={id} className="font-bold text-xl md:text-2xl text-gradient-primary block">{title}</Label>
                    <p className="text-sm text-muted-foreground mt-1 leading-relaxed">{description}</p>
                </div>
            </div>

            <Textarea
                id={id}
                rows={6}
                className="input-3d shadow-lg border-2 border-white/50 dark:border-slate-600/50 focus:border-blue-400 dark:focus:border-blue-500 transition-all duration-300 hover:shadow-xl resize-none relative z-10"
                value={value || ''}
                onChange={handleChange}
                placeholder={`اكتب ${title.toLowerCase()} هنا...`}
            />

            {/* شريط ملون في الأسفل */}
            <div className={cn("absolute bottom-0 left-0 right-0 h-1 opacity-60", color)}></div>
        </div>
    );
};

export const SwotAnalysis = () => {
  return (
    <Card className="w-full card-3d shadow-3d-hover border-glow bg-gradient-to-br from-amber-50/90 via-orange-50/90 to-yellow-50/90 dark:from-amber-900/30 dark:via-orange-900/30 dark:to-yellow-900/30 backdrop-blur-sm relative overflow-hidden">
      {/* تأثير الخلفية المتحركة */}
      <div className="absolute inset-0 bg-gradient-to-r from-amber-400/5 to-orange-400/5 animate-pulse"></div>

      <CardHeader className="border-b border-amber-200/50 dark:border-amber-700/50 relative z-10 bg-gradient-to-r from-white/50 to-amber-50/50 dark:from-slate-800/50 dark:to-amber-900/50">
        <CardTitle className="flex items-center gap-4 text-2xl md:text-3xl font-bold">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full blur opacity-50"></div>
            <div className="relative w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
              <Target className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="flex flex-col">
            <span className="text-gradient-primary">التحليل الرباعي</span>
            <span className="text-lg font-normal text-gray-600 dark:text-gray-300">SWOT Analysis</span>
          </div>
        </CardTitle>
        <div className="mt-2 w-16 h-1 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full shadow-lg"></div>
      </CardHeader>

      <CardContent className="pt-8 pb-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <SwotField
                title="نقاط القوة"
                description="(المهارات، القدرة المالية، الدعم العائلي، الخبرة السابقة...)"
                id="strengths"
                icon={ThumbsUp}
                color="bg-gradient-to-r from-green-500 to-emerald-500"
            />
            <SwotField
                title="نقاط الضعف"
                description="(عدم وجود المهارات، ضعف القدرة المالية، قلة الخبرة...)"
                id="weaknesses"
                icon={ThumbsDown}
                color="bg-gradient-to-r from-red-500 to-rose-500"
            />
            <SwotField
                title="الفرص"
                description="(عوامل اقتصادية، قانونية، اجتماعية إيجابية، اتجاهات السوق...)"
                id="opportunities"
                icon={Lightbulb}
                color="bg-gradient-to-r from-blue-500 to-cyan-500"
            />
            <SwotField
                title="التهديدات"
                description="(عوامل خارجية قد تضر المشروع، منافسة شرسة، تغيرات قانونية...)"
                id="threats"
                icon={AlertTriangle}
                color="bg-gradient-to-r from-yellow-500 to-amber-500"
            />
        </div>
      </CardContent>

      {/* تأثير الإضاءة السفلية */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-500 to-orange-500 opacity-50"></div>
    </Card>
  );
};
import { Lightbulb } from "lucide-react";

export const Logo = () => {
  return (
    <div className="flex flex-col items-center justify-center gap-4 mb-8">
      <div className="relative group">
        {/* تأثير الإضاءة الخارجية */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-300 animate-pulse"></div>

        {/* الشعار الرئيسي */}
        <div className="relative w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 p-5 rounded-full shadow-3d group-hover:scale-110 transition-transform duration-300">
          {/* تأثير الإضاءة الداخلية */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-full"></div>
          <Lightbulb className="h-10 w-10 text-white relative z-10 group-hover:animate-pulse" />

          {/* نقاط مضيئة */}
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
          <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
        </div>
      </div>

      <div className="text-center">
        <span className="text-3xl md:text-4xl font-bold text-gradient-primary tracking-tight block">
          خطة مشروعي
        </span>
        <span className="text-sm text-gray-600 dark:text-gray-400 mt-1 block">
          Business Plan Builder
        </span>
      </div>
    </div>
  );
};
; تطبيق خطة مشروعي - مثبت NSIS
; Business Plan Builder - NSIS Installer
; المطور: sa<PERSON>

!define APP_NAME "خطة مشروعي"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "saif aldulaimi"
!define APP_EXE "run-app.bat"
!define APP_ICON "icon.ico"

; إعدادات عامة
Name "${APP_NAME}"
OutFile "خطة-مشروعي-مثبت-1.0.0.exe"
InstallDir "$PROGRAMFILES\${APP_NAME}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; واجهة المثبت
!include "MUI2.nsh"
!define MUI_ABORTWARNING
!define MUI_ICON "resources\icon.ico"
!define MUI_UNICON "resources\icon.ico"

; صفحات المثبت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; قسم التثبيت الرئيسي
Section "التطبيق الأساسي" SecMain
  SectionIn RO
  
  ; تعيين مجلد الإخراج
  SetOutPath "$INSTDIR"
  
  ; نسخ ملفات التطبيق
  File /r "business-plan-clean\*.*"
  
  ; إنشاء اختصار سطح المكتب
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_ICON}" 0
  
  ; إنشاء اختصار قائمة ابدأ
  CreateDirectory "$SMPROGRAMS\${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_ICON}" 0
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe"
  
  ; كتابة معلومات إلغاء التثبيت في الريجستري
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\${APP_ICON}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
  
  ; إنشاء ملف إلغاء التثبيت
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; حفظ مجلد التثبيت
  WriteRegStr HKLM "Software\${APP_NAME}" "InstallDir" "$INSTDIR"
  
SectionEnd

; وصف الأقسام
LangString DESC_SecMain ${LANG_ARABIC} "الملفات الأساسية للتطبيق"
LangString DESC_SecMain ${LANG_ENGLISH} "Main application files"

!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; قسم إلغاء التثبيت
Section "Uninstall"
  
  ; حذف الملفات
  RMDir /r "$INSTDIR"
  
  ; حذف الاختصارات
  Delete "$DESKTOP\${APP_NAME}.lnk"
  RMDir /r "$SMPROGRAMS\${APP_NAME}"
  
  ; حذف مفاتيح الريجستري
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
  DeleteRegKey HKLM "Software\${APP_NAME}"
  
SectionEnd

; دالة التحقق من المتطلبات
Function .onInit
  ; التحقق من وجود Node.js
  nsExec::ExecToStack 'node --version'
  Pop $0
  Pop $1
  ${If} $0 != 0
    MessageBox MB_YESNO|MB_ICONQUESTION "Node.js غير مثبت على النظام.$\n$\nهل تريد المتابعة؟ (ستحتاج لتثبيت Node.js لاحقاً)" IDYES +2
    Abort
  ${EndIf}
FunctionEnd

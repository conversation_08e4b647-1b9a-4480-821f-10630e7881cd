@echo off
echo 🎯 إنشاء مثبت خطة مشروعي - خيارات متعددة
echo.

echo اختر طريقة إنشاء المثبت:
echo.
echo 1. NSIS (مجاني - حجم صغير)
echo 2. Inno Setup (مجاني - سهل الاستخدام)
echo 3. مثبت بسيط (ZIP مع سكريبت)
echo 4. إنشاء جميع الأنواع
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto nsis
if "%choice%"=="2" goto inno
if "%choice%"=="3" goto simple
if "%choice%"=="4" goto all
goto invalid

:nsis
echo.
echo 🔨 بناء مثبت NSIS...
call بناء_المثبت_النهائي.bat
goto end

:inno
echo.
echo 🔨 بناء مثبت Inno Setup...
where iscc >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Inno Setup غير مثبت
    echo 📥 يرجى تحميله من: https://jrsoftware.org/isinfo.php
    pause
    goto end
)
iscc installer-inno.iss
if %ERRORLEVEL% EQ 0 (
    echo ✅ تم بناء مثبت Inno Setup بنجاح!
) else (
    echo ❌ فشل في بناء مثبت Inno Setup
)
goto end

:simple
echo.
echo 📦 إنشاء مثبت بسيط...
goto create_simple

:all
echo.
echo 🔨 إنشاء جميع أنواع المثبتات...
call :create_simple
call بناء_المثبت_النهائي.bat
where iscc >nul 2>nul
if %ERRORLEVEL% EQ 0 (
    iscc installer-inno.iss
)
goto end

:create_simple
echo 📦 إنشاء المثبت البسيط...

REM إنشاء مجلد المثبت البسيط
if exist "مثبت-بسيط" rmdir /s /q "مثبت-بسيط"
mkdir "مثبت-بسيط"

REM نسخ النسخة النظيفة
if not exist "business-plan-clean" (
    echo 🔧 إنشاء النسخة النظيفة...
    call create-clean-version.bat
)

echo 📋 نسخ ملفات التطبيق...
xcopy "business-plan-clean" "مثبت-بسيط\app\" /E /I /Q

REM إنشاء سكريبت التثبيت
echo 📝 إنشاء سكريبت التثبيت...
echo @echo off > "مثبت-بسيط\تثبيت.bat"
echo echo 🚀 مثبت خطة مشروعي البسيط >> "مثبت-بسيط\تثبيت.bat"
echo echo. >> "مثبت-بسيط\تثبيت.bat"
echo echo 📁 اختر مجلد التثبيت: >> "مثبت-بسيط\تثبيت.bat"
echo set /p install_dir="المسار (افتراضي: C:\خطة مشروعي): " >> "مثبت-بسيط\تثبيت.bat"
echo if "%%install_dir%%"=="" set install_dir=C:\خطة مشروعي >> "مثبت-بسيط\تثبيت.bat"
echo. >> "مثبت-بسيط\تثبيت.bat"
echo echo 📂 إنشاء مجلد التثبيت... >> "مثبت-بسيط\تثبيت.bat"
echo if not exist "%%install_dir%%" mkdir "%%install_dir%%" >> "مثبت-بسيط\تثبيت.bat"
echo. >> "مثبت-بسيط\تثبيت.bat"
echo echo 📋 نسخ ملفات التطبيق... >> "مثبت-بسيط\تثبيت.bat"
echo xcopy "app" "%%install_dir%%" /E /I /Q >> "مثبت-بسيط\تثبيت.bat"
echo. >> "مثبت-بسيط\تثبيت.bat"
echo echo 🔗 إنشاء اختصار سطح المكتب... >> "مثبت-بسيط\تثبيت.bat"
echo powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%%USERPROFILE%%\Desktop\خطة مشروعي.lnk'); $Shortcut.TargetPath = '%%install_dir%%\run-app.bat'; $Shortcut.IconLocation = '%%install_dir%%\icon.ico'; $Shortcut.Save()" >> "مثبت-بسيط\تثبيت.bat"
echo. >> "مثبت-بسيط\تثبيت.bat"
echo echo ✅ تم التثبيت بنجاح! >> "مثبت-بسيط\تثبيت.bat"
echo echo 🚀 يمكنك الآن تشغيل التطبيق من سطح المكتب >> "مثبت-بسيط\تثبيت.bat"
echo pause >> "مثبت-بسيط\تثبيت.bat"

REM إنشاء دليل الاستخدام
echo 📖 إنشاء دليل الاستخدام...
echo # مثبت خطة مشروعي البسيط > "مثبت-بسيط\اقرأني.txt"
echo. >> "مثبت-بسيط\اقرأني.txt"
echo ## كيفية التثبيت: >> "مثبت-بسيط\اقرأني.txt"
echo 1. انقر مرتين على "تثبيت.bat" >> "مثبت-بسيط\اقرأني.txt"
echo 2. اختر مجلد التثبيت أو اتركه افتراضي >> "مثبت-بسيط\اقرأني.txt"
echo 3. انتظر حتى اكتمال التثبيت >> "مثبت-بسيط\اقرأني.txt"
echo 4. ستجد اختصار على سطح المكتب >> "مثبت-بسيط\اقرأني.txt"
echo. >> "مثبت-بسيط\اقرأني.txt"
echo ## المتطلبات: >> "مثبت-بسيط\اقرأني.txt"
echo - Node.js مثبت على النظام >> "مثبت-بسيط\اقرأني.txt"
echo. >> "مثبت-بسيط\اقرأني.txt"
echo تم تطويره بواسطة: saif aldulaimi >> "مثبت-بسيط\اقرأني.txt"

REM ضغط المثبت البسيط
echo 📦 ضغط المثبت البسيط...
powershell "Compress-Archive -Path 'مثبت-بسيط\*' -DestinationPath 'خطة-مشروعي-مثبت-بسيط.zip' -Force"

echo ✅ تم إنشاء المثبت البسيط: خطة-مشروعي-مثبت-بسيط.zip
goto :eof

:invalid
echo ❌ اختيار غير صحيح
goto end

:end
echo.
echo 🎉 انتهى إنشاء المثبت!
echo.
echo 📁 الملفات المنشأة:
if exist "خطة-مشروعي-مثبت-1.0.0.exe" echo   ✅ NSIS: خطة-مشروعي-مثبت-1.0.0.exe
if exist "خطة-مشروعي-مثبت-inno-1.0.0.exe" echo   ✅ Inno Setup: خطة-مشروعي-مثبت-inno-1.0.0.exe
if exist "خطة-مشروعي-مثبت-بسيط.zip" echo   ✅ مثبت بسيط: خطة-مشروعي-مثبت-بسيط.zip
echo.
pause
